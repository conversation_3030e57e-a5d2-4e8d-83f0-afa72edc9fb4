import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";
import { MailMessageSearchComponent } from "../../requirements/components/search-selects/mail-message-search.js?v=#{BuildVersion}#";

export class SendToPurchaseHubHandler {
    constructor(data) {
        this.data = data;
        this.form = null;
        this.ccAutoMailSearch = null;
        this.init();
    }

    init() {
        this.form = new LiteFormDialog("#hubrfq-send-to-purchase-hub-dialog", {
            width: "600px",
            closeWhenSuccess: true,
            url: "/api/orders/bom/send-to-purchase-hub",
            method: "POST",
            buttons: [
                { name: "save", icon: "check", alt: "send", display: "Yes" },
                {
                    name: "cancel", icon: "xmark", alt: "cancel", display: "No", onClick: () => {
                        $('#selectedItems-hubrfq-send-to-purchase-hub-cc-search').empty();
                    }
                },
            ],
            validationRules: {
                assignUserNo: {
                    required: true,
                    notEqualTo: "0"
                }
            },
            validationMessages: {
                assignUserNo: {
                    required: "Please enter a value",
                    notEqualTo: "Please enter a value"
                }
            },
            errorPlacement: function (error, element) {
                let errorContainer = element.closest('.form-control-wrapper').find('.validation-message');
                if (errorContainer.length === 0) {
                    errorContainer = $('<div class="validation-message text-danger mt-1"></div>');
                    element.closest('.form-control-wrapper').append(errorContainer);
                }
                errorContainer.html(error);
                const errorSummary = $("#hubrfq-send-to-purchase-hub-dialog .form-error-summary");
                errorSummary.show();
            },
        });

        $.validator.addMethod("notEqualTo", function (value, element, param) {
            return this.optional(element) || value !== param;
        }, "Please enter a value");

        this.form.populateForm(this.data);
        if (!this.ccAutoMailSearch) {
            this.ccAutoMailSearch = new MailMessageSearchComponent(
                "hubrfq-send-to-purchase-hub-cc-search",
                "hubrfq-send-to-purchase-hub-cc",
                "multiple",
                "nameSearch",
                "/user-account/mail-messages/auto-search"
            );
        }
        this.form.getFormData = () => {
            let data = {};
            this.form.$form.serializeArray().forEach(({ name, value }) => {
                data[name] = value;
            });
            data['recipientLoginIDs'] = JSON.parse(data['recipientLoginIDs'] || '[]');
            return data;
        }

        this.form.$form.on('closedWithSuccessedResponse.mf', async (e, { response }) => {
            $('#send-purchase-hub-btn').prop('disabled', true);
            $('#bom-details-status').text('RPQ');
        });
    }

    // Override
    open() {
        if (this.form) {
            this.form.open();
            if (this.data.isReqInValid && this.data.validMessage) {
                this.showErrorMessage(this.data.validMessage, true);
            } else if (!this.data.pvvbomCountValid && this.data.pvvbomValidateMessage) {
                this.showErrorMessage(this.data.pvvbomValidateMessage);
            } else {
                this.hideErrorMessage();
            }
        }
    }

    updateData(newData) {
        this.data = newData;
    }

    showErrorMessage(message = null, hideFormElements = false) {
        const errorSummary = $(
            "#hubrfq-send-to-purchase-hub-dialog .form-error-summary"
        );
        if (errorSummary.length > 0) {
            // If a custom message is provided, update the error text
            if (message) {
                errorSummary.find("p").last().text(message);
            }
            errorSummary.show();

            // Hide form elements if requested (for isReqInValid case)
            if (hideFormElements) {
                this.toggleFormElements(false);
            }
        }
    }

    hideErrorMessage() {
        const errorSummary = $(
            "#hubrfq-send-to-purchase-hub-dialog .form-error-summary"
        );
        if (errorSummary.length > 0) {
            errorSummary.hide();
        }

        // Show form elements when hiding error
        this.toggleFormElements(true);
    }

    toggleFormElements(show) {
        // Toggle all form inputs
        if (!show) {
            this.form.$form.find("input, select, textarea").closest('.form-control-wrapper').hide();
            $(this.form.$form).closest('.ui-dialog').find('.btn-save').hide();

            let $cancelBtn = $(this.form.$form).closest('.ui-dialog').find('.btn-cancel');
            let $img = $cancelBtn.find('img');
            if ($img.length > 0) {
                $cancelBtn.html($img[0].outerHTML + ' Back');
            } else {
                $cancelBtn.text('Back');
            }
        } else {
            this.form.$form.find("input, select, textarea").closest('.form-control-wrapper').show();
            $(this.form.$form).closest('.ui-dialog').find('.btn-save').show();

            let $cancelBtn = $(this.form.$form).closest('.ui-dialog').find('.btn-cancel');
            let $img = $cancelBtn.find('img');
            if ($img.length > 0) {
                $cancelBtn.html($img[0].outerHTML + ' No');
            } else {
                $cancelBtn.text('No');
            }
        }
    }
}
