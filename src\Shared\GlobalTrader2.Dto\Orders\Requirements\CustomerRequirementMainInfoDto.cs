﻿
namespace GlobalTrader2.Dto.Orders.Requirements
{
    public class CustomerRequirementMainInfoDto
    {
        public int CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }
        public string? CompanyName { get; set; } = string.Empty;
        public string? Part { get; set; } = string.Empty;
        public byte? ROHS { get; set; }
        public DateTime? ReceivedDate { get; set; }
        public string? ReceivedDateText
        {
            get => ReceivedDate.HasValue ? ReceivedDate.Value.ToString("dd/MM/yyyy") : string.Empty;
        }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public string? FormatedPrice { get; set; }
        public string? CurrencyCode { get; set; }
        public int RowCnt { get; set; }
    }
}
