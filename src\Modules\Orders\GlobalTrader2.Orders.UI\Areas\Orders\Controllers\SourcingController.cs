using GlobalTrader2.Core.Models;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UI.Helper.Sourcing;
using GlobalTrader2.Orders.UserCases.Commons.Constants;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.AddOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.AddSourcingInfo;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.AddSourcingResultFromAltPart;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.AddTrusted;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.EditOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.EditReverseLogistic;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.EditSourcingInfo;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.EditStrategicOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.EditTrusted;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.RequestForQuote;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetAltParts;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetCustomerRequirements;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetEditStrategicOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetEpo;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetOffersHistory;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetPreviousHUBRFQOffer;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetPurchaseRequestLine;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetPurchaseOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetQuoteLine;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetReverseLogistics;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSales;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingBulkEditLog;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingInfo;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingRFQData;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetStockOnOrder;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSupplierLiveDataFeeds;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetTrusted;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Globalization;
using Microsoft.AspNetCore.Http;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifySaleperson;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartWatch.Queries;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.CloneTrustedAddToSourcing;
using GlobalTrader2.Core.Constants;
using Microsoft.AspNetCore.Http.HttpResults;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.CloneOfferAddToSourcing;
using GlobalTrader2.SharedUI;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartDetail.Queries;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/sourcing")]
[SectionAuthorize(SecurityFunction.OrdersSection_View)]
public class SourcingController : ApiBaseController
{
    private readonly IMediator _mediator;

    private readonly IStringLocalizer<SharedUI.OfferStatus> _offerStatusLocalizer;

    private readonly SecurityManager _securityManager;

    private readonly SessionManager _sessionManager;

    private readonly IStringLocalizer<MessageResources> _messageLocalizer;

    public SourcingController(IMediator mediator, IConfiguration configuration, IStringLocalizer<SharedUI.OfferStatus> offerStatusLocalizer, SecurityManager securityManager, SessionManager sessionManager, IStringLocalizer<MessageResources> messageLocalizer)
    {
        _mediator = mediator;
        _offerStatusLocalizer = offerStatusLocalizer;
        _securityManager = securityManager;
        _sessionManager = sessionManager;
        _messageLocalizer = messageLocalizer;
    }

    [HttpPost("stock-on-order")]
    public async Task<IActionResult> GetStockOnOrder([FromBody] SourcingPartSearchRequest request)
    {
        const int SORT_INDEX_RESALE_PRICE = 6;
        const int SORT_INDEX_CLIENT_UP_LIFT_PRICE = 7;

        if (!IsPOHub && request.OrderBy == SORT_INDEX_RESALE_PRICE)
        {
            request.OrderBy = SORT_INDEX_CLIENT_UP_LIFT_PRICE;
        }

        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(request.PartNo ?? string.Empty);

        var response = await _mediator.Send(new GetStockOnOrderQuery
        {
            PartSearch = partSearch,
            ClientId = ClientId,
            ClientDMCC = Core.Constants.ClientId.DMCC,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir
        });

        if (response.Data == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data)
        {
            x.StockDetailUrl = $"{string.Format(V2Paths.StockDetailWithParams, x.StockId)}";
            x.CurrentClient = x.ClientNo == ClientId;
        }

        return new JsonResult(response);
    }

    [HttpPost("sourcing-info")]
    public async Task<IActionResult> GetSourcingInfoAsync([FromBody] SourcingPartSearchRequest request)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(request.PartNo ?? string.Empty);

        var response = await _mediator.Send(
            new GetSourcingInfoQuery
            {
                ClientId = ClientId,
                CultureInfo = new CultureInfo(Culture),
                IsPoHub = IsPOHub,
                PartSearch = partSearch,
                OrderBy = request.OrderBy,
                SortDir = request.SortDir
            });

        if (response.Data == null || !response.Data.Any())
        {
            return new JsonResult(response);

        }

        response.Data = response.Data.Select(x =>
        {
            if (x.CurrentClient)
            {
                x.SupplierRMADetailUrl = $"{string.Format(V2Paths.SupplierRMADtailsParams, x.SupplierRMAId)}";
            }

            return x;
        });

        return new JsonResult(response);
    }

    [HttpPost("trusted")]
    public async Task<IActionResult> GetSourcingTrustedAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);

        var mainQuery = new GetTrustedQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(mainQuery);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            x.SupplierNo = x.CompanyNo;
            x.CurrentClient = x.ClientNo == ClientId;
            x.OfferStatus = x.OfferStatusNo.HasValue
                ? _offerStatusLocalizer.GetString(((UserCases.Commons.Enums.OfferStatus)x.OfferStatusNo).ToString())
                : string.Empty;
        }

        return new JsonResult(response);
    }

    [HttpGet("sourcing-info/{id}")]
    public async Task<IActionResult> GetSourcingInfoByIdAsync(int id)
    {
        var response = await _mediator.Send(
            new GetSourcingInfoByIdQuery { SourcingInfoDataId = id });
        if (response.Data?.ClientNo == ClientId)
        {
            return new JsonResult(response);
        }
        return new JsonResult(null);
    }

    [HttpPost("alt-parts")]
    public async Task<IActionResult> GetAltParts([FromBody] SourcingPartSearchRequest request)
    {
        var cultureInfo = new CultureInfo(Culture);
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(request.PartNo ?? string.Empty);


        var response = await _mediator.Send(new GetAltPartsQuery
        {
            PartSearch = partSearch,
            ClientId = ClientId,
            CultureInfo = cultureInfo,
            IsPoHub = IsPOHub,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir
        });

        if (response.Data == null || !response.Data.Any())
        {
            return new JsonResult(response);
        }

        response.Data = response.Data.Select(x =>
        {
            string strStatus = "";
            if (x.OfferStatusNo != null) strStatus = _offerStatusLocalizer.GetString(((UserCases.Commons.Enums.OfferStatus)x.OfferStatusNo).ToString());
            x.Status = strStatus;

            return x;
        });

        return new JsonResult(response);
    }

    [HttpGet("alt-parts/{id}")]
    public async Task<IActionResult> GetAltPartByIdAsync(int id)
    {
        var response = await _mediator.Send(
            new GetAltPartByIdQuery(id));

        return new JsonResult(response);
    }

    [HttpPut("alt-parts/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditAltPartAsync([FromBody] EditAltPartRequestDto request, int id)
    {
        var command = new EditAlPartCommand
        {
            AlternativePartId = id,
            Part = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            CurrencyNo = request.CurrencyNo,
            Salesman = request.Salesman,
            SupplierNo = request.CompanyNo,
            ROHS = request.Rohs,
            Notes = request.Notes,
            UpdatedBy = UserId,
            OfferStatusNo = request.OfferStatusNo,
            SupplierTotalQSA = request.SupplierTotalQSA,
            SupplierMOQ = request.SupplierMOQ,
            SupplierLTB = request.SupplierLTB,
            MslLevelNo = request.MslLevelNo,
            Spq = request.Spq,
            LeadTime = request.LeadTime,
            FactorySealed = request.FactorySealed,
            RohsStatus = request.RohsStatus
        };

        var result = await _mediator.Send(command);
        var subject = _messageLocalizer["You have a new Partwatch Match"];
        await SendMailMessageDialogHubrfq(request.IsHubrfqPage, result.Success, id, subject);

        return Ok(result);
    }

    [HttpPost("add-sourcing-info")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddSourcingInfoAsync([FromBody] SourcingInfoCreateRequest request)
    {
        var hasPermission = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_AddStockInfo,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_AddStockInfo
        );

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var command = new AddSourcingInfoCommand
        {
            SupplierId = request.SupplierId,
            PartNo = request.PartNo,
            SupplierPart = request.SupplierPart?.Trim() ?? string.Empty,
            ManufacturerId = request.ManufacturerId,
            DateCode = request.DateCode?.Trim() ?? string.Empty,
            ProductId = request.ProductId,
            AlternatePartNo = request.AlternatePartNo?.Trim() ?? string.Empty,
            Notes = request.Notes?.Trim() ?? string.Empty,
            UpdatedBy = UserId,
            ClientNo = ClientId

        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPut("edit-sourcing-info/{id}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditSourcingInfoAsync([FromBody] SourcingInfoCreateRequest request, int id)
    {
        var hasPermission = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_EditStockInfo,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_EditStockInfo
        );

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var command = new EditSourcingInfoCommand
        {
            Id = id,
            SupplierId = request.SupplierId,
            PartNo = request.PartNo,
            SupplierPart = request.SupplierPart?.Trim() ?? string.Empty,
            ManufacturerId = request.ManufacturerId,
            DateCode = request.DateCode?.Trim() ?? string.Empty,
            ProductId = request.ProductId,
            AlternatePartNo = request.AlternatePartNo?.Trim() ?? string.Empty,
            Notes = request.Notes?.Trim() ?? string.Empty,
            UpdatedBy = UserId,
            ClientNo = ClientId

        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpGet("trusted/{id}")]
    public async Task<IActionResult> GetTrustedByIdAsync(int id)
    {
        var response = await _mediator.Send(new GetTrustedByIdQuery(id));

        return new JsonResult(response);
    }

    [HttpPost("add-trusted")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddTrustedAsync([FromBody] CreateTrustedRequestDto request)
    {
        var hasPermission = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_AddTrusted,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_AddTrusted
        );

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var command = new AddTrustedCommand
        {
            Part = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            CurrencyNo = request.CurrencyNo,
            OriginalEntryDate = request.OriginalEntryDate,
            Salesman = UserId,
            CompanyNo = request.CompanyNo,
            CompanyName = request.CompanyName,
            Rohs = request.Rohs,
            Notes = request.Notes,
            UpdatedBy = UserId,
            ClientNo = ClientId,
            OfferStatusNo = request.OfferStatusNo,
            IsPoHub = IsPOHub,
            FactorySealed = request.FactorySealed,
            LeadTime = request.LeadTime,
            MslLevelNo = request.MslLevelNo,
            RohsStatus = request.RohsStatus,
            Spq = request.Spq,
            SupplierLTB = request.SupplierLTB,
            SupplierMOQ = request.SupplierMOQ,
            SupplierTotalQSA = request.SupplierTotalQSA
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPut("edit-trusted/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditTrustedAsync([FromBody] EditTrustedRequestDto request, int id)
    {
        var command = new EditTrustedCommand
        {
            ExcessId = id,
            Part = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            CurrencyNo = request.CurrencyNo,
            CompanyNo = request.CompanyNo,
            Rohs = request.Rohs,
            Notes = request.Notes,
            UpdatedBy = UserId,
            OfferStatusNo = request.OfferStatusNo,
            IsPoHub = IsPOHub,
            FactorySealed = request.FactorySealed,
            LeadTime = request.LeadTime,
            MslLevelNo = request.MslLevelNo,
            RohsStatus = request.RohsStatus,
            Spq = request.Spq,
            SupplierLTB = request.SupplierLTB,
            SupplierMOQ = request.SupplierMOQ,
            SupplierTotalQSA = request.SupplierTotalQSA,
            IsArchived = request.IsArchived
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPost("add-sourcing-result")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddSourcingResult([FromBody] AddSourcingResultRequest request)
    {
        var canAddSourcingResult = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_AddToRequirement,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_AddToRequirement
        );

        if (!canAddSourcingResult ||
            !Enum.TryParse<AddToRequirementSource>(request.Source, true, out var source) ||
            (source == AddToRequirementSource.StockOnOrder && (request.PageType != (int)SourcingPageType.HUBRFQDetails || !IsPOHub)))
        {
            return StatusCode(StatusCodes.Status403Forbidden);
        }

        var partDetail = await _mediator.Send(new GetPartDetailQuery()
        {
            CustomerRequirementId = request.CustomerRequirementId,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
        });

        if (partDetail?.Data?.Closed == true)
        {
            return Ok(new BaseResponse<CloneAddToRequirementResultDto>
            {
                Success = false,
                Message = _messageLocalizer["Part Closed Cannot Clone Or Add To Requirement"]
            });
        }

        var command = new AddSourcingResultCommand()
        {
            AddToRequirementSource = source,
            LoginId = UserId,
            IsPOHub = IsPOHub,
            CustomerRequirementId = request.CustomerRequirementId,
            LineIDs = request.LineIDs,
            ClientNo = ClientId
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }


    [HttpPost("supplier-live-data-feeds")]
    public async Task<IActionResult> GetSupplierLiveDataFeedsAsync([FromBody] SourcingPartSearchRequest request)
    {
        var cultureInfo = new CultureInfo(Culture);
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(request.PartNo ?? string.Empty);
        var keySearch = partSearch?.Split("\\n", StringSplitOptions.RemoveEmptyEntries).ToArray();
        string message = string.Empty;

        if (keySearch is null || keySearch.Length == 0)
        {
            return new JsonResult(new BaseResponse<IEnumerable<SupplierInfoDto>> { Success = true, Data = [] });
        }

        var lstApiDetails = await _mediator.Send(new GetApiExternalLinksDetailQuery());
        var lst = new List<ApiExternalLinkDto>();

        foreach (var rw in lstApiDetails?.Data ?? [])
        {
            var processApiExternalLinkMessage = await ProcessApiExternalLinkAsync(rw, keySearch, lst);
            if (!string.IsNullOrEmpty(processApiExternalLinkMessage))
                message = processApiExternalLinkMessage;
        }

        lst = lst.DistinctBy(x => new { x.FElectronicsId, x.Part, x.Reference, x.Quantity, x.UnitCostPrice }).ToList();
        lst = SourcingHelper.SortExternalLinks(lst, request.OrderBy, request.SortDir);

        var jsonResponse = new BaseResponse<IEnumerable<SupplierInfoDto>>
        {
            Success = true,
            Message = message,
            Data = lst.Select(fe =>
            {
                var dto = new SupplierInfoDto
                {
                    SplrID = fe.SupplierNo,
                    Splr = fe.SupplierName,
                    By = string.IsNullOrEmpty(fe.SalesmanName) ? fe.EpoStatusChangeEmployeeName : fe.SalesmanName,
                    MfrNo = fe.ManufacturerNo,
                    ClientCode = string.Empty,
                    Price = Functions.FormatCurrency(fe.Price, cultureInfo, fe.CurrencyCode, 5, false),
                    UpliftPrice = Functions.FormatCurrency(fe.UpliftPrice, cultureInfo, fe.CurrencyCode, 5, false),
                    ID = fe.EpoId,
                    ROHS = fe.ROHS,
                    SplrEmail = fe.SupplierEmail,
                    Date = Functions.FormatDate(fe.EpoStatusChangeDate, false, false, cultureInfo),
                    Status = string.Empty,
                    Notes = Functions.ReplaceLineBreaks(fe.Notes),
                    CurrentClient = ClientId == fe.ClientNo,
                    MSL = fe.MSL,
                    SPQ = fe.SPQ,
                    RoHSStatus = fe.RoHSStatus,
                    SupplierTotalQSA = fe.SupplierTotalQSA,
                    SupplierLTB = fe.SupplierLTB,
                    IsSourcingHub = fe.IsSourcingHub,
                    Qty = fe.Quantity,
                    PartNo = fe.Part ?? string.Empty,
                    EntrDate = Functions.FormatDate(fe.OriginalEntryDate, false, false, cultureInfo),
                    Description = fe.Description ?? string.Empty,
                    Mfr = fe.ManufacturerCode ?? string.Empty,
                    DC = fe.DateCode ?? string.Empty,
                    SupplierName = fe.SupplierName ?? string.Empty,
                    SupplierType = fe.SupplierType ?? string.Empty,
                    Reference = fe.Reference ?? string.Empty,
                    Product = fe.ProductName ?? string.Empty,
                    PackageType = fe.PackageName ?? string.Empty,
                    SupplierPackageType = fe.SupplierPackageType ?? string.Empty,
                    ECCN = fe.ECCN ?? string.Empty,
                    PublishDate = Functions.FormatDate(fe.PublishDate, false, false, cultureInfo),
                    SupplierMOQ = fe.MOQ,
                    UnitCostPrice = fe.UnitCostPrice,
                    ApiShortName = fe.ApiShortName ?? string.Empty,
                    FElectronicsId = fe.FElectronicsId,
                    ApiName = fe.ApiName ?? string.Empty,
                    IsRestrictedManufacturer = fe.IsRestrictedManufacturer
                };

                if (fe.EpoStatusNo.HasValue)
                {
                    dto.Status = _offerStatusLocalizer.GetString(
                        fe.EpoStatusNo != null ?
                        ((UserCases.Commons.Enums.OfferStatus)fe.EpoStatusNo).ToString()
                        : string.Empty);
                }

                return dto;
            }).ToList()
        };

        return new JsonResult(jsonResponse);
    }

    private static void AddSupplierDataToList<T>(BaseResponse<IEnumerable<T>> response, List<T> targetList)
    {
        if (response?.Data != null)
        {
            targetList.AddRange(response.Data);
        }
    }

    [HttpGet("request-for-quote-data/{id}")]
    public async Task<IActionResult> GetSourcingRFQDataAsync(int id, SourcingRFQMode mode, int clientNo, bool currentClient)
    {
        var response = await _mediator.Send(new GetSourcingRFQDataQuery(id, mode, clientNo, currentClient, IsPOHub, Culture));
        return new JsonResult(response);
    }

    [HttpPost("request-for-quote")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateRequestForQuoteAsync([FromBody] RequestForQuote request)
    {
        var hasPermission = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_RFQ,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_RFQ
        );

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var response = await _mediator.Send(new CreateRequestForQuoteCommand(UserId, ClientId, Culture, request));
        return new JsonResult(response);
    }


    [HttpPost("offers")]
    public async Task<IActionResult> GetSourcingOfferAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);

        var query = new GetOfferQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            x.CurrentClient = x.ClientNo == ClientId;
            x.OfferStatus = x.OfferStatusNo.HasValue
                ? _offerStatusLocalizer.GetString(((UserCases.Commons.Enums.OfferStatus)x.OfferStatusNo).ToString())
                : string.Empty;
        }

        return new JsonResult(response);
    }

    [HttpPost("epo")]
    public async Task<IActionResult> GetEpoAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        const int EPO_PRICE_SORT_INDEX = 8;
        const int EPO_UPLIFT_PRICE_SORT_INDEX = 9;

        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetEpoQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = (searchSourcingRequest.OrderBy == EPO_PRICE_SORT_INDEX && !IsPOHub) ? EPO_UPLIFT_PRICE_SORT_INDEX : searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            x.CurrentClient = x.ClientNo == ClientId;
            x.ClientCode = null;
            if (!IsPOHub)
            {
                x.SupplierName = x.SupplierEpo;
                x.SupplierNo = null;
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("reverse-logistics")]
    public async Task<IActionResult> GetReverseLogisticsAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        const int EPO_PRICE_SORT_INDEX = 8;
        const int EPO_UPLIFT_PRICE_SORT_INDEX = 9;

        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetReverseLogisticsQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = (searchSourcingRequest.OrderBy == EPO_PRICE_SORT_INDEX && !IsPOHub) ? EPO_UPLIFT_PRICE_SORT_INDEX : searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            x.CurrentClient = x.ClientNo == ClientId;
            x.ClientCode = null;
            if (!IsPOHub)
            {
                x.SupplierName = x.SupplierReverseLogistic;
                x.SupplierNo = null;
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("previous-hubrfq-offer")]
    public async Task<IActionResult> GetPreviousHubrfqOfferAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetPreviousHubrfqOfferQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }
        foreach (var x in response.Data.LstDto)
        {
            if (x.SourcingTable == "PQ" || x.SourcingTable == "OFPH" || x.SourcingTable == "EXPH")
            {
                x.SupplierNo = IsPOHub ? x.POHubCompanyNo : x.ClientCompanyNo;
                x.SupplierName = IsPOHub ? x.POHubSupplierName : x.ClientSupplierName;
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("customer-requirements")]
    public async Task<IActionResult> GetDataCustomerRequirementsAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetCustomerRequirementsQuery()
        {
            ClientId = ClientId,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            bool isCurrentClient = x.ClientNo == ClientId;
            if (IsPOHub || isCurrentClient)
            {
                x.HubRfqDetailUrl = $"{string.Format(V2Paths.HubRfqDetailsParams, x.BOMID)}";
            }

            if (isCurrentClient)
            {
                x.CustomerDetailUrl = $"{string.Format(V2Paths.CompanyDetailWithCmParam, x.CompanyNo)}";
                x.RequirementDetailUrl = $"{string.Format(V2Paths.CustomerReqDetailsParams, x.CustomerRequirementId)}";
            }
            else
            {
                if (IsPOHub || x.ClientDataVisibleToOthers)
                {
                    x.CompanyName = !string.IsNullOrEmpty(x.ClientCode) ? $"{x.CompanyName} ({x.ClientCode})" : x.CompanyName;
                }
                else
                {
                    x.CompanyName = x.ClientName ?? "";
                }
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("quote")]
    public async Task<IActionResult> GetQuoteAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetQuoteLineQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            if (x.ClientNo == ClientId)
            {
                x.Cust = x.CompanyName ?? string.Empty;
                x.CustId = x.CompanyNo;
                x.QuoteId = x.QuoteNo;
                x.QuoteDetailUrl = x.QuoteId != 0
                    ? $"{string.Format(V2Paths.QuoteDetailWithQtParam, x.QuoteId)}"
                    : null;
            }
            else
            {
                x.QuoteLineId = 0;
                x.QuoteId= 0;
                x.CustId = null;
                x.QuoteDetailUrl = null;
                x.Salesman = 0;
                x.Cust = $"{x.CompanyName} ({x.ClientCode})";
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("price-request")]
    public async Task<IActionResult> GetPriceRequestAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetPurchaseRequestLineQuery()
        {
            ClientId = ClientId,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            bool isCurrentClient = x.ClientNo == ClientId;

            if (isCurrentClient)
            {
                x.PoQuoteDetailUrl = $"{string.Format(V2Paths.PoQuoteDetailWithPqtParam, x.PurchaseRequestNumber)}";
                x.CustomerDetailUrl = $"{string.Format(V2Paths.CompanyDetailWithCmParam, x.CompanyNo)}";
            }
        }

        return new JsonResult(response);
    }

    [HttpPost("purchases")]
    public async Task<IActionResult> GetPurchasesAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);
        var query = new GetPurchaseOrderLineQuery()
        {
            ClientId = ClientId,
            IsPoHub = IsPOHub,
            PartSearch = partSearch,
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        foreach (var x in response.Data.LstDto)
        {
            x.CanShowIPO = x.InternalPurchaseOrderId.HasValue && x.InternalPurchaseOrderId.Value > 0;
            x.IsSRMA = x.IsSRMA ?? false;
            if (x.ClientNo == ClientId)
            {
                x.Cust = x.CompanyName ?? string.Empty;
                x.CustNo = x.CompanyNo;
                x.PurchaseOrderDetailUrl = setDetailUrl(x.PurchaseOrderNo, V2Paths.PurchaseOrderDetailWithPOParam, null);
                x.InternalPurchaseOrderDetailUrl = setDetailUrl(x.InternalPurchaseOrderId, V2Paths.InternalPurchaseOrderDetailWithIPOParam, null);
            }
            else
            {
                x.PurchaseOrderLineId = 0;
                x.PurchaseOrderNo = 0;
                x.CustNo = null;
                x.InternalPurchaseOrderId = IsPOHub ? x.InternalPurchaseOrderId : 0;
                x.Cust = IsPOHub ? x.CompanyName + " (" + x.ClientCode + ")" : x.ClientName;
                x.PurchaseOrderDetailUrl = null;
                x.InternalPurchaseOrderDetailUrl = setDetailUrl(x.InternalPurchaseOrderId, V2Paths.InternalPurchaseOrderDetailWithIPOParam, IsPOHub);
            }
        }

        return new JsonResult(response);
    }

    [HttpGet("offers/{id}")]
    public async Task<IActionResult> GetOfferByIdAsync(int id)
    {
        var response = await _mediator.Send(new GetOfferByIdQuery(id));

        return new JsonResult(response);
    }

    [HttpPut("edit-offer/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditOfferAsync([FromBody] EditOfferRequestDto request, int id)
    {
        var command = new EditOfferCommand
        {
            OfferId = id,
            Part = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            Salesman = UserId,
            CurrencyNo = request.CurrencyNo,
            CompanyNo = request.CompanyNo,
            Rohs = request.Rohs,
            UpdatedBy = UserId,
            OfferStatusNo = request.OfferStatusNo,
            FactorySealed = request.FactorySealed,
            LeadTime = request.LeadTime,
            MslLevelNo = request.MslLevelNo,
            RohsStatus = request.RohsStatus,
            Spq = request.Spq,
            SupplierLTB = request.SupplierLTB,
            SupplierMOQ = request.SupplierMOQ,
            SupplierTotalQSA = request.SupplierTotalQSA,
            Notes = request.Notes,
            IsPoHub = IsPOHub,
            IsArchived = request.IsArchived
        };

        var result = await _mediator.Send(command);

        var subject = _messageLocalizer["You have a new Partwatch Match"];
        await SendMailMessageDialogHubrfq(request.IsHubrfqPage, result.Success, id, subject);

        return Ok(result);
    }

    [HttpPut("clone-add-to-requirement/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_AddToRequirement)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CloneAndAddToRequirementAsync([FromBody] EditOfferRequestDto request, int id)
    {
        if (!Enum.TryParse<CloneAndAddToRequirementSource>(request.CloneSource, true, out var source) || request.PageType != (int)SourcingPageType.HUBRFQDetails || !IsPOHub)
        {
            return StatusCode(StatusCodes.Status403Forbidden);
        }

        var partDetail = await _mediator.Send(new GetPartDetailQuery()
        {
            CustomerRequirementId = request.CustReqNo.HasValue ? request.CustReqNo.Value : -1,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
        });

        if (partDetail?.Data?.Closed == true)
        {
            return Ok(new BaseResponse<CloneAddToRequirementResultDto>
            {
                Success = false,
                Message = _messageLocalizer["Part Closed Cannot Clone Or Add To Requirement"]
            });
        }

        var command = new CloneAddToRequirementCommand {
            AddToRequirementSource = source,
            Id = id,
            PartNo = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            Currency = request.CurrencyNo,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = UserId,
            Supplier = request.CompanyNo,
            ROHS = request.Rohs,
            Notes = request.Notes,
            LoginID = UserId,
            ClientID = ClientId,
            OfferStatus = request.OfferStatusNo,
            IsPOHub = IsPOHub,
            SupplierTotalQSA = request.SupplierTotalQSA,
            SupplierMOQ = request.SupplierMOQ,
            SupplierLTB = request.SupplierLTB,
            MSLNo = request.MslLevelNo,
            SPQ = request.Spq,
            LeadTime = request.LeadTime,
            FactorySealed = request.FactorySealed,
            ROHSStatus = request.RohsStatus,
            CustReqNo = request.CustReqNo,
            MSL = string.Empty
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPost("add-offer")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddOfferAsync([FromBody] CreateOfferRequestDto request)
    {
        var hasPermission = await CheckPermissionManual(
            request.PageType == (int)SourcingPageType.RequirementDetails,
            SecurityFunction.Orders_Sourcing_AddOffer,
            SecurityFunction.Orders_CustomerRequirement_Sourcing_AddOffer
        );

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var command = new AddOfferCommand
        {
            Part = request.PartNo,
            ManufacturerNo = request.ManufacturerNo,
            DateCode = request.DateCode,
            ProductNo = request.ProductNo,
            PackageNo = request.PackageNo,
            Quantity = request.Quantity,
            Price = request.Price,
            Salesman = UserId,
            CurrencyNo = request.CurrencyNo,
            CompanyNo = request.CompanyNo,
            Rohs = request.Rohs,
            UpdatedBy = UserId,
            MslLevelNo = request.MslLevelNo,
            RohsStatus = request.RohsStatus,
            Spq = request.Spq,
            SupplierLTB = request.SupplierLTB,
            SupplierMOQ = request.SupplierMOQ,
            SupplierTotalQSA = request.SupplierTotalQSA,
            Notes = request.Notes,
            IsPoHub = IsPOHub,
            OfferStatusNo = request.OfferStatusNo,
            FactorySealed = request.FactorySealed,
            LeadTime = request.LeadTime,
            ClientNo = ClientId
        };

        var result = await _mediator.Send(command);

        var subject = _messageLocalizer["You have a new Partwatch Match"];
        await SendMailMessageDialogHubrfq(request.IsHubrfqPage, result.Success, result.Data, subject);

        return Ok(result);
    }


    [HttpGet("bulk-edit-log")]
    public async Task<IActionResult> GetSourcingBulkEditLogAsync(
        [FromQuery] GetSourcingBulkEditLogRequestDto parameters
    )
    {
        var response = await _mediator.Send(new GetSourcingBulkEditLogQuery
        {
            Options = parameters,
            Culture = Culture
        });

        return new JsonResult(response);
    }

    [HttpGet("reverse-logistics/{id}")]
    public async Task<IActionResult> GetReverseLogisticByIdAsync(int id)
    {
        var response = await _mediator.Send(new GetReverseLogisticByIdQuery(id));

        return new JsonResult(response);
    }

    [HttpPut("edit-reverse-logistic/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditReverseLogisticAsync([FromBody] EditReverseLogisticRequestDto request, int id)
    {
        request.Salesman = UserId;

        var command = new EditReverseLogisticCommand
        {
            ReverseLogisticId = id,
            Model = request,
            UpdatedBy = UserId,
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    private async Task<string> ProcessApiExternalLinkAsync(ApiKeyValueDetailDto rw, string[] keySearch, List<ApiExternalLinkDto> lst)
    {
        string message = string.Empty;

        if (!Enum.TryParse<ApiName>(rw.ApiShortName, out var apiName))
            return message;

        foreach (var partNo in keySearch)
        {
            if (rw.ApiURLKeyId == 1 && apiName == ApiName.FE)
            {
                var lstSupplierData = await _mediator.Send(new GetSupplierDataQuery
                {
                    ClientId = ClientId,
                    PartSearch = partNo,
                    ApiURLKeyId = rw.ApiURLKeyId,
                    ApiType = rw.ApiShortName,
                    ApiUrl = rw.Url,
                    LicenseKey = rw.Licensekey,
                    UserId = UserId
                });

                AddSupplierDataToList(lstSupplierData, lst);
            }

            if (rw.ApiURLKeyId == 2 && apiName == ApiName.DG)
            {
                var lstSupplierDGData = await _mediator.Send(new GetSupplierDataQuery
                {
                    ClientId = ClientId,
                    PartSearch = partNo,
                    ApiURLKeyId = rw.ApiURLKeyId,
                    ApiType = rw.ApiShortName,
                    UserId = UserId
                });

                if (lstSupplierDGData.Message == "Too Many Requests")
                {
                    message = "Digikey Daily Request Limit Reached.";
                }

                AddSupplierDataToList(lstSupplierDGData, lst);
            }
        }

        return message;
    }

    [HttpGet("strategic-offer/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    public async Task<IActionResult> GetStrategicOfferByIdAsync(int id)
    {
        var cultureInfo = new CultureInfo(Culture);
        var response = await _mediator.Send(new GetStrategicOfferByIdQuery(id, cultureInfo));

        return new JsonResult(response);
    }

    [HttpPut("strategic-offer/{id}")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_EditOffer)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditStrategicOfferAsync([FromBody] EditStrategicOfferRequestDto request, int id)
    {

        if(request.PageType == (int)SourcingPageType.Sourcing || request.PageType == (int)SourcingPageType.HUBRFQDetails)
        {
            var isStrategicOffersVirtualCostPrice = await _securityManager.CheckFunctionPermissions(UserId, false, new List<SecurityFunction>() { SecurityFunction.Orders_Sourcing_Edit_StrategicOffers_VirtualCostPrice });
            var cultureInfo = new CultureInfo(Culture);

            if (!isStrategicOffersVirtualCostPrice)
            {
                var response = await _mediator.Send(new GetStrategicOfferByIdQuery(id, cultureInfo));
                request.UpliftPrice = response.Data?.UpliftPrice;
            }
        }        

        var command = new EditStrategicOfferCommand
        {
            EpoId = id,
            Part = request.PartNo,
            ManufacturerNo = request.MfrNo,
            DateCode = request.DC,
            ProductNo = request.Product,
            PackageNo = request.Package,
            Quantity = request.Qty,
            Price = request.Price,
            CurrencyNo = request.Currency,
            Salesman = request.Salesman,
            SupplierNo = request.SupplierNo,
            Rohs = request.Rohs,
            Notes = request.Notes,
            UpdatedBy = UserId,
            EpoStatusNo = request.OfferStatus,
            SupplierTotalQSA = request.SupplierTotalQSA,
            SupplierMOQ = request.SupplierMOQ,
            SupplierLTB = request.SupplierLTB,
            Spq = request.Spq,
            LeadTime = request.LeadTime,
            FactorySealed = request.FactorySealed,
            UpliftPrice = request.UpliftPrice,
            Description = request.Description ?? string.Empty,
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPost("bulk-strategic-offer")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_BulkEdit_StrategicStock)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditStrategicOfferAsync([FromBody] EditBulkStrategicOfferRequestDto request, int id)
    {
        var command = new EditBulkStrategicOfferCommand
        {
            ListId = request.ListId,
            Action = request.Action ?? string.Empty,
            LoginID = UserId,
            SourcingType = SourcingTypes.StrategicStock,
        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPost("bulk-reverse-logistic")]
    [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_HideEdit_ReverseLogistic_Bulk)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditBulkReverseLogisticAsync([FromBody] EditBulkStrategicOfferRequestDto request, int id)
    {
        var command = new EditBulkStrategicOfferCommand
        {
            ListId = request.ListId,
            Action = request.Action ?? string.Empty,
            LoginID = UserId,
            SourcingType = SourcingTypes.ReverseLogistic,

        };

        var result = await _mediator.Send(command);

        return Ok(result);
    }

    [HttpPost("offers-history")]
    public async Task<IActionResult> GetOffersHistoryAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);

        var query = new GetOffersHistoryQuery()
        {
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            PartSearch = partSearch,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir,
            ClientId = ClientId,
            IsPoHub = IsPOHub,
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }
        
        response.Data.LstDto = response.Data.LstDto.Select(x =>
        {
            x.OfferStatus = x.OfferStatusNo.HasValue
                ? _offerStatusLocalizer.GetString(((UserCases.Commons.Enums.OfferStatus)x.OfferStatusNo).ToString())
                : string.Empty;
            x.CurrentClient = x.ClientNo == ClientId;

            return x;
        });

        return new JsonResult(response);
    }

    [HttpPost("sales")]
    public async Task<IActionResult> GetSalesAsync([FromBody] SourcingPaginatedPartSearchRequest searchSourcingRequest)
    {
        string IPOsSeperator = "||";
        string IPOPairSeperator = ",";

        var partSearch = StringHelper.RemoveNonAlphanumericRetainingPercentSignsAndBreakLines(searchSourcingRequest.PartNo ?? string.Empty);

        var query = new GetSalesQuery()
        {
            Index = searchSourcingRequest.Index,
            EndDate = searchSourcingRequest.MaxDateString,
            PartSearch = partSearch,
            OrderBy = searchSourcingRequest.OrderBy,
            SortDir = searchSourcingRequest.SortDir,
            ClientId = ClientId,
            IsPoHub = IsPOHub,
        };

        var response = await _mediator.Send(query);

        if (response.Data?.LstDto == null)
        {
            return new JsonResult(response);
        }

        var ipoDetailUrlDict = new Dictionary<string, SalesDetailsDto.IpoDetailUrlDto>();

        response.Data.LstDto = response.Data.LstDto.Select(x =>
        {
            var ipoNos = !string.IsNullOrWhiteSpace(x.IPOs) ? x.IPOs.Split(IPOsSeperator)[0].Split(IPOPairSeperator) : [];
            var ipoNumbers = !string.IsNullOrWhiteSpace(x.IPOs) ? x.IPOs.Split(IPOsSeperator)[1].Split(IPOPairSeperator) : [];

            for (int i = 0; i < ipoNos.Length; i++)
            {
                var ipoNo = ipoNos[i];

                if (ipoDetailUrlDict.TryGetValue(ipoNo, out var ipoDetailUrlDto))
                {
                    x.IPODetailUrls.Add(ipoDetailUrlDto);
                }
                else
                {
                    ipoDetailUrlDto = new SalesDetailsDto.IpoDetailUrlDto
                    {
                        IPONo = ipoNo,
                        IPONumber = ipoNumbers[i],
                        IPODetailUrl = $"{string.Format(V2Paths.InternalPurchaseOrderDetailWithIPOParam, ipoNo)}"
                    };

                    x.IPODetailUrls.Add(ipoDetailUrlDto);

                    ipoDetailUrlDict.Add(ipoNo, ipoDetailUrlDto);
                }
            }

            x.SalesOrderDetailUrl = $"{string.Format(V2Paths.SalesOrderDetailWithSOParam, x.SalesOrderNo)}";

            x.CurrentClient = x.ClientNo == ClientId;

            return x;
        });

        return new JsonResult(response);
    }



    private string? setDetailUrl(int? id, string v1Path, bool? isPoHub)
    {
        var result = id != 0 ? $"{string.Format(v1Path, id)}" : null;
        if (isPoHub.HasValue) 
        { 
            result = isPoHub.Value && id != 0 ? $"{string.Format(v1Path, id)}" : null;
        }
        return result;
    }

    private async Task<bool> CheckPermissionManual(bool? isRequirementPage, SecurityFunction securityFunctionSourcing, SecurityFunction securityFunctionCustomerRequirement)
    {
        if (isRequirementPage.HasValue && isRequirementPage.Value)
        {
            return await _securityManager.CheckFunctionPermissions(UserId, false, new List<SecurityFunction>() { securityFunctionCustomerRequirement });
        }
        else
        {
            return await _securityManager.CheckFunctionPermissions(UserId, false, new List<SecurityFunction>() { securityFunctionSourcing });
        }
    }

    private async Task SendMailMessageDialogHubrfq(bool? isHubrfqPage, bool success, int id, string subject)
    {
        if (isHubrfqPage.HasValue && isHubrfqPage.Value && success && id > 0)
        {
            var matchedReqs = await _mediator.Send(new GetPartWatchMatchingRequirementQuery() { ClientId = ClientId, OfferId = id });
            if (matchedReqs.Data != null && matchedReqs.Data.Any())
            {
                var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
                var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
                foreach (var matchedReq in matchedReqs.Data)
                {
                    await _mediator.Send(new NotifySalepersonCommand
                    {
                        UserId = UserId,
                        CustomerRequirementNo = matchedReq.CustomerRequirementNo,
                        SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                        SalepersonId = matchedReq.Salesman ?? 0,
                        CustomRequirementUrl = $"{urlBase}{urlPath}?req={matchedReq.CustomerRequirementId}",
                        SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail),
                        Subject = subject
                    });
                }
            }
        }
    }
}
