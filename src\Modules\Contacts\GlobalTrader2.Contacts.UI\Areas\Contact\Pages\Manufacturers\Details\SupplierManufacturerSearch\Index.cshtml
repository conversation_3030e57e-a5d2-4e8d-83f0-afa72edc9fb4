﻿@page
@using GlobalTrader2.Contacts.UI.Areas.Contact.Pages.Manufacturers.Details.SupplierManufacturerSearch
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.DualListbox
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.SharedUI.Services
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IWebResourceManager WebResourceManager
@inject IViewLocalizer _localizer
@inject SettingManager _settingManager

@{
    Layout = "_SystemSetupLayout";
    ViewData["Title"] = "Sourcing";
    ViewData["IsMasterPage"] = true;
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

<div class="card mt-2">
    <h5 class="card-header">Manufacturer / Supplier Sourcing</h5>
    <div class="card-body">
        <form id="manufacturer-supplier-sourcing" class="row common-form">
            <div class="col-5">
                <div class="fs-12">Enter all the required Sourcing fields and click <b>Search</b></div>
                <div class="form-control-wrapper row">
                    <label for="ManufacturerSearch" class="form-label col-3">
                        Manufacturer
                    </label>
                    <div class="col-7">
                        <div class="d-flex align-items-center gap-2">
                            <input type="checkbox" class="form-check-input check-md mt-0" name="ManufacturerSearchCheckbox" id="manufacturerSearchCheckbox">
                            <input type="text" id="ManufacturerSearch" class="form-control form-input input-md" />
                        </div>
                        <input type="hidden" name="Manufacturer" id="Manufacturer" />
                    </div>

                </div>

                <div class="form-control-wrapper row">
                    <label for="ManufacturerGroupSearch" class="form-label col-3">
                        Manufacturer Group
                    </label>
                    <div class="col-7">
                        <div class="d-flex align-items-center gap-2">
                            <input type="checkbox" class="form-check-input check-md mt-0" name="ManufacturerGroupSearchCheckbox" id="manufacturerGroupSearchCheckbox">
                            <input type="text" id="ManufacturerGroupSearch" class="form-control form-input input-md" />
                        </div>
                        <input type="hidden" name="ManufacturerGroup" id="ManufacturerGroup" />
                    </div>

                </div>

                <div class="line"></div>

                <div class="form-control-wrapper row">
                    <span class="form-label col-3">
                        Supplier Type
                    </span>
                    <div class="col-7 d-flex gap-3">
                        <div class="form-check">
                            <input class="form-check-input check-md" type="radio" name="SupplierType" id="SupplierTypeFranchised" value="1">
                            <label class="form-check-label" for="SupplierTypeFranchised">
                                Franchised
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input check-md" type="radio" name="SupplierType" id="SupplierTypeNonFranchised" value="2">
                            <label class="form-check-label" for="SupplierTypeNonFranchised">
                                Non Franchised
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input check-md" type="radio" name="SupplierType" id="SupplierTypeAll" value="0" checked>
                            <label class="form-check-label" for="SupplierTypeAll">
                                All
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-control-wrapper row">
                    <span class="form-label col-3">
                        Product Type
                    </span>
                    <div class="col-3">
                        <select class="form-select select-md" name="ProductTypeSelection" id="ProductTypeSelection">
                            <option value="0">--Product Category--</option>
                        </select>
                    </div>
                    <div class="col-3">
                        <select class="form-select select-md" name="GlobalCategorySelection" id="GlobalCategorySelection">
                            <option value="0">--Global Category--</option>
                        </select>
                    </div>
                    <div class="col-3">
                        <select class="form-select select-md" name="ProductSelection" id="ProductSelection">
                            <option value="0">--Product--</option>
                        </select>
                    </div>
                </div>

                <div class="form-control-wrapper row">
                    <label for="OrderDate" class="form-label col-3">
                        Last Order Date
                    </label>
                    <div class="col-3">
                        <select class="form-select select-md" name="OrderDate" id="OrderDate">
                            <option value="0">--Order Date--</option>
                            <option value="1">--1 Month--</option>
                            <option value="2">--3 Month--</option>
                            <option value="3">--6 Month--</option>
                            <option value="4">--1 Year--</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-7">
                <div class="fs-12">Please note Supplier List will update dynamically based on the filter group</div>
                <div class="form-control-wrapper row align-items-start">
                    <span class="form-label col-2">
                        Supplier
                    </span>
                    @await Component.InvokeAsync(nameof(DualListbox), new
                        {
                            model = new DualListboxModel()
                    {
                       Id = "manufacturer-supplier-dual-listbox",
                       UnselectedText = _commonLocalizer["Unselected"].Value,
                       SelectedText = _commonLocalizer["Selected"].Value,
                       NotFoundText = _messageLocalizer["NoResult"].Value,
                       ContainerClass = "col-10 justify-content-start"
                   }
                        })
                </div>
                <div class="form-control-wrapper row align-items-start">
                    <span class="form-label col-2">
                        Industry Type
                    </span>
                    @await Component.InvokeAsync(nameof(DualListbox), new
                        {
                            model = new DualListboxModel()
                   {
                       Id = "manufacturer-industry-type-dual-listbox",
                       UnselectedText = _commonLocalizer["Unselected"].Value,
                       SelectedText = _commonLocalizer["Selected"].Value,
                       NotFoundText = _messageLocalizer["NoResult"].Value,
                       ContainerClass = "col-10 justify-content-start"
                   }
                        })
                </div>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" id="searchBtn" type="button" disabled>
                    <span>Search</span>
                </button>
                <button class="btn btn-secondary" id="resetBtn" type="reset">
                    <span>Reset</span>
                </button>
            </div>
        </form>
    </div>
</div>

<div id="result-search-table-container"></div>
<div class="step-pane">
    <div id="result-search-table-wrapper">
        <table id="result-search-table" class="table simple-table display responsive">
            <tr>
                <th></th>
            </tr>
        </table>

    </div>
</div>

<script>
    var localizedTitles = {
        lastOrderDate: "@_commonLocalizer["Last Order Date"]",
        orderBy: "@_commonLocalizer["Order By"]",
        purchaseOrder: "@_commonLocalizer["PO Number"]",
        supplier: "@_commonLocalizer["Supplier"]",
        starRating: "@_commonLocalizer["Star Rating"]",
        type: "@_commonLocalizer["Type"]",
        manufacturerName: "@_commonLocalizer["Manufacturer"]",
        website: "@_commonLocalizer["Website"]",
        email: "@_commonLocalizer["Email"]",
        product: "@_commonLocalizer["Product"]",
        industryType: "@_commonLocalizer["Industry Type"]",
        last12MonthRMA: "@_commonLocalizer["No Of RMAs Last 12 Months"]",
        orderCountInLastOneYear: "@_commonLocalizer["Total Orders in 12 Months"]",
        lastOrderValue: "@_commonLocalizer["Last order Value"]"
    };

    var localizedOptions = {
        productCategory : "@_commonLocalizer["Product Category"]",
        globalCategory : "@_commonLocalizer["Global Category"]",
        product : "@_commonLocalizer["Product"]",
    };
</script>


@section Scripts {
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <link type="text/css" rel="stylesheet" href="~/css/star-rating.css" asp-append-version="true" />
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="~/js/helper/sort-helper.js" asp-append-version="true"></script>
    <script src="~/js/widgets/resize-data-table-extensions.js" asp-append-version="true"></script>
    <script src="~/js/helper/datatables-detail-helper.js" asp-append-version="true"></script>
    <script src="~/js/modules/orders/sourcing/helpers/sourcing-section-box.helper.js" asp-append-version="true"></script>
    <script src="~/js/helper/html-helper.js" asp-append-version="true"></script>
    <script src="~/js/helper/page-url-function-helper.js" asp-append-version="true"></script>
    <script type="module" src="~/js/modules/contact/manufacturers/details/supplier-manufacturer-search/supplier-manufacturer-search.js" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/contact/manufacturers/details/supplier-manufacturer-search/supplier-manufacturer-search.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/contact-supplier-manufacturer-search.bundle.js" asp-append-version="true"></script>
    </environment>
}
