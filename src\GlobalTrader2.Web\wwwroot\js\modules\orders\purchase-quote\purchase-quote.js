﻿import { LiteDatatable } from "../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#"
import { FilterTableSectionBox } from "../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#"
import { SectionBox } from "../../../components/base/section-box.component.js?v=#{BuildVersion}#"
import { inputFilterDefinition } from "./input-filter-definition.js?v=#{BuildVersion}#"
import { PoQuoteTalbeFilterComponent } from "./po-quote-table-filter.js?v=#{BuildVersion}#"

$(async () => {
    
    const state = {
        tabId: 0, // default currentTab
        tabDic: {
            0: "my-tab",
            1: "team-tab",
            2: "division-tab",
            3: "company-tab",
        },
        filterStateType: 31,//Po Quote
        requirementsTable: null,
        filter: null,
        sectionBox: null,
        filterSectionBox: null,
    }

    const preferences = await getPreferences();
    state.preferences = preferences;

    function registerTabEvents() {
        $(document).on('click', '#po-quote-nav-tabs-wrapper button', async (e, data) => {
            const ignoreSave = data?.notResetPageRequired
            setTimeout(function () {
                openningTab($(e.target).attr("tabId"), ignoreSave, !data?.notResetPageRequired);
            }, 100);
        })
    }

    async function initFilterTableSectionBoxAsync() {
        state.requirementsTable = new LiteDatatable('#poQuoteTbl', {
            serverSide: true,
            ajax: {
                url: '/api/orders/purchase-quote/list',
                type: 'POST',
            },
            ordering: true,
            disableSelect: true,
            pageConfig: {
                pageSize: state.preferences.defaultListPageSize
            },
            columns: [ 
                {
                    data: 'purchaseRequestLineId',
                    name: 'purchaseRequestLineId',
                    visible: false
                },
                {
                    title: `${priceRequestTitleLocalize.No}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'purchaseRequestNumber',
                    width: "10%",
                    render: function (data, type, row) {
                        return row.purchaseRequestNumber ?
                            $('<a>').attr('href', GlobalTrader.PageUrlHelper.GET_URL_PurchaseQuote(row.purchaseRequestId)).addClass('dt-hyper-link').text(row.purchaseRequestNumber).prop('outerHTML') : '';
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.PartNo}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'part',
                    width: "12%",
                    render: function (data, type, row) {
                        return row.part ? 
                            $('<span>').text(row.part).prop('outerHTML') : '';
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.BOM}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'bomName',
                    width: "10%",
                    render: function (data, type, row) {
                        return row.bomName ? 
                            $('<a>').attr('href', GlobalTrader.PageUrlHelper.GET_URL_BOM(row.bomNo)).text(row.bomName).addClass('dt-hyper-link').prop('outerHTML') : '';
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.Quantity}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'quantity',
                    width: "8%",
                    render: function (data, type, row) {
                        return $('<span>').text(row.quantity).prop('outerHTML');
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.CompanyName}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'companyName',
                    width: "15%",
                    render: function (data, type, row) {
                        return row.companyName ? 
                            $('<span>').text(row.companyName).prop('outerHTML') : '';
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.Buyer}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'employeeName',
                    width: "12%",
                    render: function (data, type, row) {
                        return row.employeeName ? 
                            $('<span>').text(row.employeeName).prop('outerHTML') : '';
                    }
                },
                {
                    title: `${priceRequestTitleLocalize.Date}`,
                    className: 'text-wrap text-break align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'dateRequested',
                    width: "10%",
                    render: function (data, type, row) {
                        return row.dateRequested ? 
                            $('<span>').text(row.dateRequested).prop('outerHTML') : '';
                    }
                }
            ],
            rowId: 'purchaseRequestLineId'
        })

        let inputs = inputFilterDefinition;



        state.filter = new PoQuoteTalbeFilterComponent("#poQuote-box-content #filter-section-wrapper", "Filter Results", {
            inputConfigs: inputs
            //templateId:'my-template' // template insert input to filter
        });

        state.sectionBox = new SectionBox('#poQuote', {
            loadingContentId: state.requirementsTable.getContainerId() // only table should be hidden while processing api requests
        }, {
            enableFilterButton: false
        });

        state.filterSectionBox = new FilterTableSectionBox(state.requirementsTable, state.filter, state.sectionBox, {
            prepareDataBeforeReload: prepareDataBeforeReload
        });

        state.filterSectionBox.registerHiddenButtonFilterEvents();

        state.sectionBox.setLock(state.preferences.saveDLNState);
        state.filterSectionBox.setFilterStateType(state.filterStateType)
        await state.filterSectionBox.initAsync();
        $(`${state.requirementsTable.state.selector} thead th .dt-column-order`).addClass('dt-column-order-custom');;
        activeTab(currentTab); // default to first tab
    }

    async function getPreferences() {
        const response = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/preferences`, {}, {});
        // handle error?
        return response?.data;
    }

    function activeTab(tabId) {
        state.tabId = tabId;
        let tab = state.tabDic[tabId];

        $(`#${tab}`).trigger('click', {
            notResetPageRequired: true
        });
    }

    function prepareDataBeforeReload(filterData) {
        let requestData = {
            viewLevelList: state.tabId,
            partSearch: !shouldInclude(filterData.Part) ? null : filterData.Part?.value,
            recentOnly: filterData.RecentOnly?.isOn ?? false,
            cmSearch: !shouldInclude(filterData.CMName) ? null : filterData.CMName?.value,
            salesman: filterData.Salesman?.value || null,
            includeClosed: filterData.IncludeClosed?.value || false,
            poQuoteNoLo: !shouldInclude(filterData.POQuoteNo) ? null : filterData.POQuoteNo?.low,
            poQuoteNoHi: !shouldInclude(filterData.POQuoteNo) ? null : filterData.POQuoteNo?.hi,
            datePOQuotedFrom: !shouldInclude(filterData.DatePOQuotedFrom) ? null : filterData.DatePOQuotedFrom?.value,
            datePOQuotedTo: !shouldInclude(filterData.DatePOQuotedTo) ? null : filterData.DatePOQuotedTo?.value,
        }
        function shouldInclude(input) {
            return input?.isOn && input.isShown;
        }
        return requestData;
    }
    async function openningTab(tabId, ignoreSave, resetPageRequired) {
        // this section for example & testing
        //params for testing
        state.tabId = tabId;
        setBuyerSelectState(state.tabId == 0)
        const data = state.filter.getAppliedValues();
        const convertedData = prepareDataBeforeReload(data)
        if (resetPageRequired) {
            state.requirementsTable.resetPage();
        }
        convertedData._ignoredSave = ignoreSave;
        await state.requirementsTable.reloadAsync(convertedData);
    }
    function setBuyerSelectState(isMyTab) {
        const buyerInput = state.filter.getInputElementByName('Salesman');
        isMyTab ? buyerInput.wrapper.hide() : buyerInput.wrapper.show();
    }

    async function initAsync() {
        registerTabEvents();
        await initFilterTableSectionBoxAsync();
    }

    await initAsync();
})