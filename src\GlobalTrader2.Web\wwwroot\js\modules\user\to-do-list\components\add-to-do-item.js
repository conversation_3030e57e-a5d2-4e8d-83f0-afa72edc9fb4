﻿$(async function () {
    const $taskCategoryType = $('#task-category-type-to-do-item');
    const saveChangedMessage = window.localizedStrings.saveChangedMessage;
    const save = window.localizedStrings.save;
    const cancel = window.localizedStrings.cancel;
    const addToDoDialog = $("#add-to-do-dialog");
    const addToDoForm = $("#add-to-do-form");
    let selectedQuoteDetail = null;
    let selectedSalesOrderDetail = null;
    let customerRequirementId;

    $("#add-to-do").button().on("click", async function (event) {
        event.stopPropagation();
        await openToDoDialog();
    });

    const option = {
        minDate: 0,
        dateFormat: "dd/mm/yy",
    };
    $("#DueDate").datepicker2(option);
    $("#ReminderDate").datepicker2(option);

    $("#todo-type-dropdown").dropdown({
        serverside: true, // prevent redundance api call
        endpoint: 'user-account/to-do/task-type',
        valueKey: 'toDoListTypeId',
        textKey: 'toDoListTypeName',
        refreshButton: $("#todo-type-refresh"),
        isCacheApplied: false
    });

    $("#todo-category-dropdown").dropdown({
        serverside: true, // prevent redundance api call
        endpoint: 'user-account/to-do/task-category',
        valueKey: 'toDoCategoryId',
        textKey: 'toDoCategoryName',
        refreshButton: $("#todo-category-refresh"),
        onSelected: () => {
            updateCompanyInputState();
            updateQuoteInputState();
            updateSOInputState();
            updateReminderCheckboxState();
            updateQuoteCompanyNameState();
            updateSOCompanyNameState();
        },
    });

    addToDoDialog.dialog({
        autoOpen: false,
        height: "auto",
        width: "50wv",
        maxHeight: $(window).height(),
        modal: true,
        buttons: [
            {
                text: save,
                class: 'btn btn-primary',
                html: `<img src="/img/icons/save.svg" alt="${save}"/>${save}`,
                click: function () {
                    if (addToDoForm.valid()) {
                        handleSubmitForm();
                    } else {
                        $(".form-error-summary").show();
                    };
                }
            },
            {
                text: cancel,
                class: 'btn btn-danger',
                html: `<img src="/img/icons/slash.svg" id="cancel-btn" alt="${cancel}" />${cancel}`,
                click: function () {
                    addToDoDialog.dialog("close");
                },
            },
        ],
        open: () => {
            $('.ui-dialog-titlebar-close').remove();
            showFieldBasedOnTaskCategory();
        },
        close: function () {
            addToDoForm[0].reset();
            addToDoForm.validate().resetForm();
            $(".form-error-summary").hide();
            $('#ReminderField').hide();

            $(this).find('.is-invalid').removeClass("is-invalid");
            const autoSearchInputs = $('[data-search-input]');
            autoSearchInputs.each((index, element) => {
                if (element.id == "company-auto-search" || element.id == "quote-auto-search" || element.id == "salesorder-auto-search") {
                    resetInput(index);
                }
            });
        },
    });

    $('#ReminderCheckbox').on('change', function () {
        if ($(this).is(':checked')) {
            $('#ReminderField').show();
        } else {
            $('#ReminderField').hide();
            $('#ReminderText').val('');
            $('#ReminderDate').val('');
            $('#ReminderHour').val(9);
            $('#ReminderMinute').val(0);
        }

        updateDailyReminderCheckboxState();
    });

    $("#apply-to-quote-id").on('change', async (event) => {
        if (!event.target.value) {
            selectedQuoteDetail = null;

            updateDailyReminderCheckboxState();
            updateQuoteCompanyNameState();

            return;
        }

        const response = await GlobalTrader.ApiClient.getAsync(`/orders/quotes/${event.target.value}`);

        if (response.success) {
            selectedQuoteDetail = response.data;

            updateDailyReminderCheckboxState();
            updateQuoteCompanyNameState();
        }

        renderQuoteNumberHyperLink();
    });

    $("#quote-auto-search").on("keypress", function (e) {
        if (e.which < 48 || e.which > 57) {
            e.preventDefault();
        }
    }).on("input", function () {
        $(this).val($(this).val().replace(/\D/g, ""));
    });

    $("#apply-to-salesorder-id").on('change', async (event) => {
        if (!event.target.value) {
            selectedSalesOrderDetail = null;
            updateSOCompanyNameState();
            return;
        }

        const response = await GlobalTrader.ApiClient.getAsync(`/orders/sales-orders/${event.target.value}/for-todo`);
        if (response.success) {
            selectedSalesOrderDetail = response.data;
            updateSOCompanyNameState();
        }
        renderSalesOrderHyperLink();
    });

    $("#salesorder-auto-search").on("keypress", function (e) {
        if (e.which < 48 || e.which > 57) {
            e.preventDefault();
        }
    }).on("input", function () {
        $(this).val($(this).val().replace(/\D/g, ""));
    });

    addToDoForm.validate({
        ignore: [],
        rules: {
            Subject: {
                required: true,
                noWhiteSpace: true,
            },
            ToDoText: {
                required: true,
                noWhiteSpace: true,
            },
            DueDate: {
                noWhiteSpace: true,
                futureDate: function () {
                    return {
                        dateSelector: "#DueDate",
                        hourSelector: "#DueHour",
                        minuteSelector: "#DueMinute"
                    };
                },

            },
            DueHour: {
                futureDate: function () {
                    return {
                        dateSelector: "#DueDate",
                        hourSelector: "#DueHour",
                        minuteSelector: "#DueMinute"
                    };
                },
            },
            DueMinute: {
                futureDate: function () {
                    return {
                        dateSelector: "#DueDate",
                        hourSelector: "#DueHour",
                        minuteSelector: "#DueMinute"
                    };
                },
            },
            ReminderText: {
                required: {
                    depends: function () {
                        return $("#ReminderCheckbox").is(":checked");
                    }
                },
                noWhiteSpace: true,
                maxlength: 500,
            },
            ReminderDate: {
                required: {
                    depends: function () {
                        return $("#ReminderCheckbox").is(":checked");
                    }
                },
                noWhiteSpace: true,
                futureDate: function () {
                    return {
                        dateSelector: "#ReminderDate",
                        hourSelector: "#ReminderHour",
                        minuteSelector: "#ReminderMinute"
                    };
                }
            },
            ReminderHour: {
                futureDate: function () {
                    return {
                        dateSelector: "#ReminderDate",
                        hourSelector: "#ReminderHour",
                        minuteSelector: "#ReminderMinute"
                    };
                }
            },
            ReminderMinute: {
                futureDate: function () {
                    return {
                        dateSelector: "#ReminderDate",
                        hourSelector: "#ReminderHour",
                        minuteSelector: "#ReminderMinute"
                    };
                }
            },
            ToDoListCategoryId: {
                required: true,
                min: 1,
            },
            CompanyNo: {
                required: {
                    param: true,
                    depends: function () {
                        return $('#todo-category-dropdown').val() == COMPANY_TASK_CATEGORY.toString()
                    }
                },
            },
            QuoteNumber: {
                required: {
                    param: true,
                    depends: function () {
                        return $('#todo-category-dropdown').val() == QUOTE_TASK_CATEGORY.toString()
                    }
                },
            },
            SalesOrderNo: {
                required: {
                    param: true,
                    depends: function () {
                        return $('#todo-category-dropdown').val() == SALES_ORDER_TASK_CATEGORY.toString()
                    }
                },
            },
        },
        messages: {
            DueMinute: "",
            ReminderMinute: "",
            ToDoListCategoryId: window.localizedStrings.requiredField,
        },
        errorPlacement: function (error, element) {
            // If the element is #myInput, put error inside #myDiv
            const inputName = $(element).attr("name");
            const searchInputNames = ['CompanyNo', 'QuoteNumber', 'SalesOrderNo'];
            const dateNames = ['ReminderDate', 'ReminderHour', 'ReminderMinute', 'DueDate', 'DueHour', 'DueMinute'];
            if (searchInputNames.includes(inputName)) {
                error.appendTo(element.parent());
            } else if (inputName === 'ToDoListCategoryId' || dateNames.includes(inputName)) {
                error.appendTo(element.parent().parent());
            }
            else {
                error.insertAfter(element); // Default placement
            }
        },
        highlight: function (element) {
            const inputName = $(element).attr("name");
            const inputId = $(element).attr("id");
            const searchInputNames = ['CompanyNo', 'QuoteNumber', 'SalesOrderNo'];

            if (searchInputNames.includes(inputName)) {
                displaySearchSelectErrorBorder(inputId);
            }
            else {
                $(element).addClass("is-invalid");
            }
        },
        unhighlight: function (element) {
            const inputName = $(element).attr("name");
            const inputId = $(element).attr("id");
            const searchInputNames = ['CompanyNo', 'QuoteNumber', 'SalesOrderNo'];

            if (searchInputNames.includes(inputName)) {
                removeSearchSelectErrorBorder(inputId);
            }
            else {
                $(element).removeClass("is-invalid");
            }
        },
    });

    function updateCompanyInputState() {
        const selectedTaskCategory = $('#todo-category-dropdown').val();

        if (selectedTaskCategory == COMPANY_TASK_CATEGORY.toString()) {
            $('#company-auto-search-group').removeClass("d-none");
        } else {
            $('#company-auto-search-group').addClass("d-none");
        }
    }

    function updateQuoteInputState() {
        const selectedTaskCategory = $('#todo-category-dropdown').val();

        if (selectedTaskCategory == QUOTE_TASK_CATEGORY.toString()) {
            $('#quote-auto-search-group').removeClass("d-none");
        } else {
            $('#quote-auto-search-group').addClass("d-none");
        }
    }

    function updateQuoteCompanyNameState() {
        const quoteNumberId = $('#apply-to-quote-id').val();

        if (quoteNumberId && selectedQuoteDetail) {
            $('#QuoteCompanyName').text(selectedQuoteDetail.companyName);
        } else {
            $('#QuoteCompanyName').text("-");
        }
    }

    function updateReminderCheckboxState() {
        const selectedTaskCategory = $('#todo-category-dropdown').val();

        if (selectedTaskCategory == QUOTE_TASK_CATEGORY.toString()) {
            $('#ReminderCheckbox').prop('checked', true);
        } else {
            $('#ReminderCheckbox').prop("checked", false);
        }

        $('#ReminderCheckbox').trigger("change");
    }

    function updateDailyReminderCheckboxState() {
        const selectedTaskCategory = $('#todo-category-dropdown').val();
        const isHaveReminder = $('#ReminderCheckbox').is(':checked');

        if (selectedTaskCategory == QUOTE_TASK_CATEGORY.toString()
            && isHaveReminder
            && selectedQuoteDetail
            && (selectedQuoteDetail.quoteStatus == QUOTE_STATUS_OFFERED || selectedQuoteDetail.quoteStatus == QUOTE_STATUS_PARTIALLY_OFFERED)
        ) {
            $('#daily-reminder-group').removeClass('d-none');
            $('#daily-reminder-group input').prop('checked', false);
        } else {
            $('#daily-reminder-group').addClass('d-none').prop('checked', false);
            $('#daily-reminder-group input').prop('checked', false);
        }
    }

    function renderQuoteNumberHyperLink() {
        const quoteNumber = $("#quote-auto-search-group .selected-item").text().trim();
        const quoteNumberLabelHtml = $("#quote-auto-search-group .selected-item").html();

        const quoteNumberCloseButtonHtml = quoteNumberLabelHtml.replace(quoteNumber, "").trim();

        const quoteNumberHyperLink = $("<a>")
            .attr("href", `/orders/quotes/details?qt=${selectedQuoteDetail.quoteId}`)
            .addClass("result-hyper-link")
            .text(quoteNumber);

        $("#quote-auto-search-group .selected-item")
            .html(quoteNumberCloseButtonHtml)
            .prepend(quoteNumberHyperLink);
    }

    function updateSOInputState() {
        const selectedTaskCategory = $('#todo-category-dropdown').val();

        if (selectedTaskCategory == SALES_ORDER_TASK_CATEGORY.toString()) {
            $('#salesorder-auto-search-group').removeClass("d-none");
        } else {
            $('#salesorder-auto-search-group').addClass("d-none");
        }
    }

    function updateSOCompanyNameState() {
        const salesOrderId = $('#apply-to-salesorder-id').val();

        if (salesOrderId && selectedSalesOrderDetail) {
            $('#SalesOrderCompanyName').text(selectedSalesOrderDetail.companyName);
        } else {
            $('#SalesOrderCompanyName').text("-");
        }
    }
    function renderSalesOrderHyperLink() {
        const soNumber = $("#salesorder-auto-search-group .selected-item").text().trim();
        const soNumberLabelHtml = $("#salesorder-auto-search-group .selected-item").html();
        const soNumberCloseButtonHtml = soNumberLabelHtml.replace(soNumber, "").trim();

        const soNumberHyperLink = $("<a>")
            .attr("href", `/orders/salesorders/details?so=${selectedSalesOrderDetail.salesOrderId}`)
            .addClass("result-hyper-link")
            .text(soNumber);

        $("#salesorder-auto-search-group .selected-item")
            .html(soNumberCloseButtonHtml)
            .prepend(soNumberHyperLink);
    }

    function showFieldBasedOnTaskCategory() {
        const type = parseInt($taskCategoryType.val() ?? 0);

        const $noneSpecificTypeWrapper = $('#none-specific-task-category-type-wrapper');
        const $specificTypeWrapper = $('#specific-task-category-type-wrapper');
        const $taskCategorySpan = $('#task-category-label-to-do');

        const $quoteAreaElements = $('.quote-area-to-do');
        const $salesOrderAreaElements = $('.sales-order-area-to-do');
        const $companyAreaElements = $('.company-area-to-do');

        const $quoteNumberCompanyNameSpan = $('#quote-number-company-name-label-to-do');
        const $quoteNumber = $('#quote-number-label-to-do');

        const $salesOrderNumberSpan = $('#sales-order-number-label-to-do');
        const $salesOrderCompanyNameSpan = $('#sales-order-customer-name-label-to-do');

        const $companyNameSpan = $('#company-name-label-to-do');

        if (type != 0) {
            $noneSpecificTypeWrapper.hide();
            $specificTypeWrapper.show();

            // Hide all areas
            $quoteAreaElements.hide();
            $salesOrderAreaElements.hide();
            $companyAreaElements.hide();

            const companyName = GlobalTrader.StringHelper.setCleanTextValue($taskCategoryType.data('companyName'));
            $('#todo-category-dropdown').dropdown('select', type);
            switch (type) {
                case COMPANY_TASK_CATEGORY: {
                    $companyAreaElements.show();
                    const companyId = $taskCategoryType.data('companyId');

                    $taskCategorySpan.find('span').text('Company');
                    $companyNameSpan.find('a')
                        .text(companyName)
                        .attr("href", `/contact/allcompanies/details?cm=${companyId}`);
                    break;
                }
                case QUOTE_TASK_CATEGORY: {
                    $quoteAreaElements.show();
                    const quoteNumber = $taskCategoryType.data('quoteNumber');
                    const quoteStatus = $taskCategoryType.data('quoteStatus');
                    
                    if (!selectedQuoteDetail) {
                        selectedQuoteDetail = {};
                    }

                    selectedQuoteDetail.quoteStatus = quoteStatus;

                    $taskCategorySpan.find('span').text('Quote');
                    $quoteNumberCompanyNameSpan.find('span')
                        .text(companyName)
                    $quoteNumber.find('span')
                        .text(quoteNumber);
                    $quoteNumber.find('a')
                        .remove();
                    break;
                }
                case SALES_ORDER_TASK_CATEGORY: {
                    $salesOrderAreaElements.show();
                    const salesOrderNumber = $taskCategoryType.data('salesOrderNumber');

                    $taskCategorySpan.find('span').text('Sales Order');
                    $salesOrderCompanyNameSpan.find('span')
                        .text(companyName);
                    $salesOrderNumberSpan.find('span')
                        .text(salesOrderNumber);
                    break;
                }
                default:
                    return;
            }
        }
        else {
            $specificTypeWrapper.hide();
            $noneSpecificTypeWrapper.show();
        }
    }

    async function openToDoDialog(categoryNo = 0) {
        addToDoDialog.dialog("open");
        addToDoDialog.dialog("setLoading", true);

        await $("#todo-category-dropdown").dropdown("reload");
        await $("#todo-type-dropdown").dropdown("reload");

        $("#todo-category-dropdown").dropdown("select", categoryNo);
        $("#todo-type-dropdown").dropdown("select", 0);

        addToDoDialog.dialog("setLoading", false);
        $("#todo-category-dropdown").trigger("focus");
    }

    window.openSpecificCategoryToDoDialog = async function (e) {
        e.preventDefault();
        const companyId = $(e.currentTarget).data('company-id');
        const companyName = GlobalTrader.StringHelper.setCleanTextValue($(e.currentTarget).data('company-name') ?? '');
        const categoryType = $(e.currentTarget).data('category-type');

        // Bind required data
        $taskCategoryType.val(categoryType);
        switch (categoryType) {
            case COMPANY_TASK_CATEGORY: {
                $taskCategoryType.data('companyId', companyId);
                $taskCategoryType.data('companyName', companyName);
                $('#apply-to-company-id').val(companyId);
                break;
            }
            case QUOTE_TASK_CATEGORY: {
                let quoteNumber = $(e.currentTarget).data('quote-number');
                let quoteId = $(e.currentTarget).data('quote-id');
                let quoteStatus = $(e.currentTarget).data('quote-status');
                customerRequirementId = $(e.currentTarget).data('requirement-id');
                $taskCategoryType.data('companyId', companyId);
                $taskCategoryType.data('companyName', companyName);
                $taskCategoryType.data('quoteNumber', quoteNumber);
                $taskCategoryType.data('quoteStatus', quoteStatus);
                $('#apply-to-quote-id').val(quoteId);
                break;
            }
            case SALES_ORDER_TASK_CATEGORY: {
                let salesOrderNo = $(e.currentTarget).data('sales-order-no');
                let salesOrderNumber = $(e.currentTarget).data('sales-order-number');
                $taskCategoryType.data('company-name', companyName);
                $taskCategoryType.data('sales-order-number', salesOrderNumber);
                $('#apply-to-salesorder-id').val(salesOrderNo);
                break;
            }
            default:
                return;
        }
        await openToDoDialog(categoryType);
        addToDoForm.find('#Subject').trigger('focus');
    }
    window.openProspectsQualificationToDoDialog = async function (companyId, companyName) {
        $('#company-auto-search').trigger('selectItem', {
            label: companyName,
            value: companyId
        });
        await openToDoDialog();
    }

    function buildDateTime(date, hour, minute) {
        if (!date || !hour || !minute) return null;
        return mergeDateTime(date, hour, minute);
    }

    function processFormData(formData, isReminderChecked, dueDateTimeString, reminderDateTimeString, relatedMailMessageId) {
        // Add date-time strings to form data if they exist
        if (dueDateTimeString) {
            formData.push({ name: "DueDate", value: dueDateTimeString });
        }
        if (reminderDateTimeString) {
            formData.push({ name: "ReminderDate", value: reminderDateTimeString });
        }

        // Convert form data array to object
        const formObject = formData.reduce((obj, field) => {
            obj[field.name] = field.value.trim();
            return obj;
        }, {});

        // Type conversions and null handling
        formObject.HasReview = formObject.HasReview === "true";
        formObject.CompanyNo = +formObject.CompanyNo;
        formObject.ToDoListTypeId = +formObject.ToDoListTypeId;
        formObject.DueDate = formObject.DueDate === "" ? null : formObject.DueDate;
        formObject.ReminderDate = isReminderChecked && formObject.ReminderDate !== "" ? formObject.ReminderDate : null;
        formObject.ReminderText = isReminderChecked ? normalizeString(formObject.ReminderText) : null;
        formObject.RelatedMailMessageNo = relatedMailMessageId === "" ? null : relatedMailMessageId;
        formObject.QuoteNumber = formObject.QuoteNumber || null;
        formObject.SalesOrderNo = formObject.SalesOrderNo || null;

        return formObject;
    }

    function applyCategoryRules(formObject, taskCategoryId) {
        const categoryId = taskCategoryId.toString();

        if (categoryId === COMPANY_TASK_CATEGORY.toString()) {
            formObject.QuoteNumber = null;
            formObject.DailyReminder = null;
            return;
        }

        if (categoryId === QUOTE_TASK_CATEGORY.toString()) {
            formObject.CompanyNo = null;

            if (selectedQuoteDetail && (selectedQuoteDetail.quoteStatus == QUOTE_STATUS_OFFERED || selectedQuoteDetail.quoteStatus == QUOTE_STATUS_PARTIALLY_OFFERED)) {
                formObject.DailyReminder = formObject.DailyReminder ? formObject.DailyReminder == 'true' : null;
            } else {
                formObject.DailyReminder = null;
            }
            return;
        }

        if (categoryId === SALES_ORDER_TASK_CATEGORY.toString()) {
            formObject.CompanyNo = null;
            formObject.QuoteNumber = null;
            formObject.DailyReminder = null;
        }
    }

    async function handleSubmitForm() {
        $(".form-error-summary").hide();
        addToDoDialog.dialog("setLoading", true);
        addToDoDialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);

        // Gather form data
        const formData = $("#add-to-do-form").serializeArray();
        const dueDate = $("#DueDate").val();
        const dueHour = $("#DueHour").val();
        const dueMinute = $("#DueMinute").val();
        const reminderDate = $("#ReminderDate").val();
        const reminderHour = $("#ReminderHour").val();
        const reminderMinute = $("#ReminderMinute").val();
        const isReminderChecked = $("#ReminderCheckbox").prop('checked');
        const relatedMailMessageId = $("#relatedMailMessageId").val();
        const taskCategoryId = $('#todo-category-dropdown').val();

        // Build date-time strings
        const dueDateTimeString = buildDateTime(dueDate, dueHour, dueMinute);
        const reminderDateTimeString = isReminderChecked ?
            buildDateTime(reminderDate, reminderHour, reminderMinute) : null;

        // Process form data into object
        const formObject = processFormData(
            formData,
            isReminderChecked,
            dueDateTimeString,
            reminderDateTimeString,
            relatedMailMessageId
        );

        // Apply category-specific business rules
        applyCategoryRules(formObject, taskCategoryId);

        // Submit form
        const header = {
            "RequestVerificationToken": $('#add-to-do-form input[name="__RequestVerificationToken"]').val()
        };
        const result = await GlobalTrader.ApiClient.postAsync("/user-account/to-do", formObject, header);

        addToDoDialog.dialog("setLoading", false);
        addToDoDialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

        if (!result?.success) {
            showToast("danger", result?.title);
            return;
        }

        // Success handling
        const toDoItemId = result.data;
        addToDoDialog.dialog("close");
        addToDoDialog.trigger("addTodoSuccess", toDoItemId);
        showToast('success', saveChangedMessage);
        if (taskCategoryId == QUOTE_TASK_CATEGORY && window.sourcingResultsSectionBox) {
            await window.sourcingResultsSectionBox.loadSourcingResultsAsync(customerRequirementId);
        }
    }
});
