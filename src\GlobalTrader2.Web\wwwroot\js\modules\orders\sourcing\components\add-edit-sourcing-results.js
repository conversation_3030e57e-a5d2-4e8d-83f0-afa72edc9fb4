﻿import { PageSearchSelectComponent } from '../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#';
import { ManufactureSearchSelectComponent } from '../../requirements/components/search-selects/manufacture-search-select.component.js?v=#{BuildVersion}#';
import { ProductSearchSelectComponent } from '../../requirements/components/search-selects/product-search-select.component.js?v=#{BuildVersion}#';
import { ExternalPartNoSearchSelectComponent } from '../../sourcing/components/search-selects/external-part-no-search-select.component.js?v=#{BuildVersion}#';
import { SupplierSearchSelectComponent } from '../../sourcing/components/search-selects/supplier-search-select.component.js?v=#{BuildVersion}#';
import { ButtonHelper } from '../../../../../js/helper/button-helper.js?v=#{BuildVersion}#';

const save = window.localizedStrings.save;
const cancel = window.localizedStrings.cancel;
const maxLengthMessage = window.localizedStrings.rangelength;
const requiredErrorMessage = window.localizedStrings.requiredField;
const saveChangedMessage = window.localizedStrings.saveChangedMessage;
const defaultIdValue = "0";
const pleaseCheckBelowAndTryAgain = window.localizedStrings.pleaseCheckBelowAndTryAgain;
const thereWereSomeProblemsWithYourForm = window.localizedStrings.thereWereSomeProblemsWithYourForm;
const partNoSearchSelectValueInput = { id: "SourcingResultsPartNo", name: "PartNo" };
const supplierSearchSelectValueInput = { id: "SourcingResultsSupplierId", name: "CompanyNo" };
const dropdownInputNames = ["CurrencyNo"];
const dropdownMslInputNames = ["MslLevelNo"];
const apiEndpoints = {
    currency: "/lists/buy-currencies/",
    rohs: "/lists/rohs-statuses",
    msl: "lists/msls",
    offerStatus: "offer-statuses/dropdown"
};

const maxIntegerValue = window.constants.MaxIntegerValue;

let isSubmitting = false;
let sourcingResultsPackageSearchSelect;
let sourcingResultsProductSearchSelect;
let sourcingResultsSupplierSearchSelect;
let sourcingResultsManufacturerSearchSelect;
let sourcingResultsPartNoSearchSelect;

let companyId = defaultIdValue;

let shouldSelectSupplierDefaultBuyCurrency = false;

let dataDetailRequirement = null;
let customerRequirementId = null;

$(() => {
    $("#addSourcingResult").button().on("click", async function (event) {
        event.stopPropagation();

        $("#add-edit-sourcing-results-dialog").dialog("open");
        const currentDialog = $("#add-edit-sourcing-results-dialog").dialog("instance");
        currentDialog.setLoading(true);

        $("#add-edit-sourcing-results-title").text(sourcingResultsString.addSourcingResults);
        $("#add-edit-sourcing-results-message").text(sourcingResultsString.addSourcingResultsMessage);
        await reloadCurrencyDropdown();

        $("#add-edit-sourcing-results-form #label-supplier-container").addClass("d-none");
        $("#add-edit-sourcing-results-form #supplier-container").removeClass("d-none");
        $("#add-edit-sourcing-results-form #part-watch-wrapper").addClass("d-none");

        fetchDropdownsAsync();

        await fetchAddSourcingResults();

        sourcingResultsPartNoSearchSelect.closeSearchInput();

        $("#sourcing-results-supplier-auto-search").trigger("focus");
    });

    $("#editSourcingResult").button().on("click", async function (event) {
        event.stopPropagation();

        $("#add-edit-sourcing-results-dialog").dialog("open");

        const currentDialog = $("#add-edit-sourcing-results-dialog").dialog("instance");
        currentDialog.setLoading(true);

        $("#add-edit-sourcing-results-title").text(sourcingResultsString.editSourcingResults);
        $("#add-edit-sourcing-results-message").text(sourcingResultsString.editSourcingResultsMessage);

        await fetchDropdownsAsync();

        await fetchSourcingResults();

        focusEdit();
    });

    $('#add-edit-sourcing-results-form').validate({
        onfocusout: false,
        onkeyup: false,
        onclick: false,
        ignore: [],
        rules: {
            SupplierNo: {
                notEmptyOrWhiteSpace: true,
            },
            Quantity: {
                notEmptyOrWhiteSpace: true,
                integerRangeCheck: {
                    min: 0, max: maxIntegerValue
                }
            },
            Price: {
                notEmptyOrWhiteSpace: true,
                decimalRangeCheck: {
                    min: `0.00000`, max: `100000000000000000.00000`, decimalPartLength: 5
                }
            },
            CurrencyNo: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            PartNoKeyword: {
                notEmptyOrWhiteSpace: true,
                maxlength: 30
            },
            DateCode: {
                maxlength: 5,
            },
            Notes: {
                maxlength: 128,
            },
            MslLevelNo: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
        },
        messages: {
            SupplierNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            Quantity: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            Price: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            CurrencyNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            PartNoKeyword: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                maxlength: maxLengthMessage,
            },
            DateCode: {
                maxlength: maxLengthMessage,
            },
            Notes: {
                maxlength: maxLengthMessage,
            },
            MslLevelNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
        },
        invalidHandler: function (event, validator) {
            displayError("");
        },
        errorPlacement: function (error, element) {
            const inputName = element.attr("name");

            if (inputName === partNoSearchSelectValueInput.name
                || inputName === supplierSearchSelectValueInput.name
                || dropdownInputNames.includes(inputName)
                || dropdownMslInputNames.includes(inputName)
                || inputName === "Price") {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
    });

    const dialogAddEditSourcingResultsForm = $("#add-edit-sourcing-results-dialog").dialog({
        autoOpen: false,
        maxHeight: $(window).height(),
        modal: true,
        width: "40vw",
        height: "auto",
        create: function (event, ui) {
            const dialog = $(this).dialog("widget");
            dialog.css("maxWidth", "800px");
        },
        buttons: [
            {
                text: "Save",
                click: async function () {
                    if (isSubmitting) {
                        return;
                    }
                    const currentDialog = $("#add-edit-sourcing-results-dialog").dialog("instance");
                    const isValid = $("#add-edit-sourcing-results-form").valid();

                    if (!isValid) {
                        focusFirstErrorInput();
                        return;
                    }

                    const formObject = getFormData();

                    let idTrigger;
                    try {
                        isSubmitting = true;
                        currentDialog.setLoading(true);

                        const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
                        const sourcingResultsId = $("#SourcingResultsId").val();
                        let response;

                        if (sourcingResultsId == "") {
                            response = await GlobalTrader.ApiClient.postAsync("/orders/customer-requirements/part-detail/sourcing-results", formObject, header);
                            idTrigger = response.data.sourcingResultId;
                            window.lastSelectedRow = response.data.sourcingResultId;
                            console.log(window.lastSelectedRow);
                        }
                        else {
                            response = await GlobalTrader.ApiClient.putAsync(`/orders/customer-requirements/part-detail/sourcing-results/${sourcingResultsId}`, formObject, header);
                            idTrigger = response.data;
                        }

                        if (response.success) {
                            $("#SourcingResultsId").val("")
                            dialogAddEditSourcingResultsForm.dialog("close");
                            showToast('success', saveChangedMessage);
                        }

                        reloadSourcingResultsBox();
                    } catch (error) {
                        if (error.response?.data?.message) {
                            displayError(error.response.data.message);
                        } else {
                            displayError("An unexpected error occurred.");
                        }
                    } finally {
                        isSubmitting = false;
                        currentDialog.setLoading(false);
                        $("#add-edit-sourcing-results-dialog").trigger('saveSuccess', { idTrigger })
                    }
                }
            },
            {
                text: "Cancel",
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        close: function (event, ui) {
            $("#add-edit-sourcing-results-form")[0].reset();
            $("#add-edit-sourcing-results-form").validate().resetForm();
            $("#add-edit-sourcing-results-form-error-summary").hide();

            $("#sourcing-results-currency-code").addClass("d-none");
            companyId = defaultIdValue;

            $("#SourcingResultsSupplierId").removeClass("is-invalid");
            $("#SourcingResultsSupplierId-error").remove();
            $("#SourcingResultsPartNo").removeClass("is-invalid");
            $("#SourcingResultsPartNo-error").remove();

            resetSearchSelects();
            resetDropDowns();

            shouldSelectSupplierDefaultBuyCurrency = false;
        },
        open: function (event, ui) {
            $(this).removeClass('d-none');
            $('.ui-dialog-titlebar-close').css('display', 'none');

            $("#add-edit-sourcing-results-form #currency-dropdown").empty();
            $("#add-edit-sourcing-results-form #currency-dropdown").append(`<option value="0">Select...</option>`)
        },
    });

    initSearchSelects();
    initDropdowns();
    allowPositiveIntegerInput('#add-edit-sourcing-results-form input[id="Quantity"]');
    allowPositiveDecimalInput('#add-edit-sourcing-results-form input[id="Price"]', true, 5);

    $('#add-edit-sourcing-results-form input[id="Price"]').on("focusout", function () {
        if ($(this).val() === "") {
            $(this).val(0);
        }
    });

    $("#sourcing-results-part-no-auto-search").on("input", function () {
        $(this).val($(this).val().toUpperCase());
    });

    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${save}')`).addClass("btn btn-primary fw-normal").html(`<img src="/img/icons/save.svg" alt="${save}"> ${save}`);
    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${cancel}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/slash.svg" alt="${cancel}"> ${cancel}`);

});

function displayError(message) {
    const errorContainer = $("#add-edit-sourcing-results-form-error-summary");
    errorContainer.find("div").html(`<p>${thereWereSomeProblemsWithYourForm}</p><p>${pleaseCheckBelowAndTryAgain}</p><p>${message}</p>`);
    errorContainer.show();
}

function resetDropDowns() {
    $("#add-edit-sourcing-results-form").find("#rohs-dropdown").val(0).trigger("change");
    $("#add-edit-sourcing-results-form").find("#msl-dropdown").val(0).trigger("change");
    $("#add-edit-sourcing-results-form").find("#offer-status-dropdown").val(0).trigger("change");
}

function getFormData() {
    const formData = $("#add-edit-sourcing-results-form").serializeArray();
    let formObject = {};

    formData.forEach(function (field) {
        if (formObject.hasOwnProperty(field.name)) {
            return;
        }

        if (field.name === "MslLevelNo") {
            formObject[field.name] = field.value === defaultIdValue ? null : field.value;
            return;

        }

        if (field.name === "OfferStatusNo") {
            formObject[field.name] = field.value === defaultIdValue ? null : field.value;
            return;
        }

        if (field.name === "Rohs") {
            formObject[field.name] = field.value === "" ? null : field.value;
            return;
        }

        if (field.value.trim() != "") {
            formObject[field.name] = field.value.trim();
        }
    });
    if (dataDetailRequirement) {
        formObject["customerRequirementNo"] = customerRequirementId;
    }
    formObject["pageType"] = currentSourcingPageType;
    return formObject;
}

function initDropdowns() {
    $("#add-edit-sourcing-results-form #currency-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.currency + defaultIdValue,
        valueKey: 'currencyId',
        textKey: 'currencyValue',
        handleResponse: (res) => {
            if (shouldSelectSupplierDefaultBuyCurrency && res.data?.selected) {
                $("#add-edit-sourcing-results-form #currency-dropdown").dropdown("select", res.data.selected ?? 0);
            }

            if (res.data?.selected) {
                $("#sourcing-results-currency-code").text(res.data.data.find(x => x.currencyId === res.data.selected)?.currencyCode);
                $("#sourcing-results-currency-code").removeClass("d-none");
                $("#message-currency").addClass("d-none");

                return res.data.data;

            } else if (res.data?.data && res.data?.data.length > 0) {
                $("#sourcing-results-currency-code").text("");
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#message-currency").removeClass("d-none");

                if (!res.data?.selected) {
                    $("#add-edit-sourcing-results-form").find("#currency-dropdown").val(0).trigger("change");
                }

                return res.data.data;

            } else {
                $("#sourcing-results-currency-code").text("");
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#message-currency").addClass("d-none");

                return [];
            }
        },
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-form #CurrencyNo-refresh-btn")
    });

    $("#add-edit-sourcing-results-form #offer-status-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.offerStatus,
        valueKey: 'offerStatusId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-form #OfferStatusNo-dropdown-refresh-btn")
    });

    $("#add-edit-sourcing-results-form #rohs-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.rohs,
        valueKey: 'rohsStatusId',
        textKey: 'description',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-form #Rohs-refresh-btn")
    });

    $("#add-edit-sourcing-results-form #CurrencyNo-refresh-button").on("click", () => {
        if (companyId === defaultIdValue) {
            return;
        }

        shouldSelectSupplierDefaultBuyCurrency = false;

        $("#add-edit-sourcing-results-form #currency-dropdown").dropdown("refresh");
    });

    $("#add-edit-sourcing-results-form #msl-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.msl,
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-form #MslLevelNo-refresh-btn")
    });

    setupDropdownRefresh("#add-edit-sourcing-results-form #OfferStatusNo-refresh-button", "#add-edit-sourcing-results-form #offer-status-dropdown", apiEndpoints.offerStatus, { refreshData: true }, "offerStatusId", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-form #Rohs-refresh-button", "#add-edit-sourcing-results-form #rohs-dropdown", apiEndpoints.rohs, { refreshData: true }, "rohsStatusId", "description");
    setupDropdownRefresh("#add-edit-sourcing-results-form #MslLevelNo-refresh-button", "#add-edit-sourcing-results-form #msl-dropdown", apiEndpoints.msl, { refreshData: true }, "id", "name");
}

async function fetchDropdownsAsync() {
    const rohsPromise = $("#add-edit-sourcing-results-form #rohs-dropdown").dropdown("reload", apiEndpoints.rohs, { refreshData: false });
    const mslDropdownPromise = $("#add-edit-sourcing-results-form #msl-dropdown").dropdown("reload", apiEndpoints.msl, { refreshData: false });
    const offerStatusDropdownPromise = $("#add-edit-sourcing-results-form #offer-status-dropdown").dropdown("reload", apiEndpoints.offerStatus, { refreshData: false });

    await Promise.all([
        rohsPromise,
        mslDropdownPromise,
        offerStatusDropdownPromise
    ]);

    //NOTE: this is needed because rohs-dropdown options has value 0, also default "Select..." has value 0
    $("#add-edit-sourcing-results-form #rohs-dropdown option:first").attr("value", "");
}

async function reloadCurrencyDropdown() {
    if (companyId === defaultIdValue) {
        return;
    }

    const currencuPromise = $("#add-edit-sourcing-results-form #currency-dropdown").dropdown("reload", apiEndpoints.currency + companyId, { refreshData: true });

    await Promise.all([currencuPromise]);
    
}

function initSearchSelects() {
    sourcingResultsPackageSearchSelect = new PageSearchSelectComponent('sourcing-results-package-auto-search', 'SourcingResultsPackageId', 'single', 'keyword', '/packages/auto-search');
    sourcingResultsProductSearchSelect = new ProductSearchSelectComponent('sourcing-results-product-auto-search', 'SourcingResultsProductId', 'single', 'keyword', '/products/auto-search');
    sourcingResultsManufacturerSearchSelect = new ManufactureSearchSelectComponent('sourcing-results-manufacturer-auto-search', 'SourcingResultsManufacturerId', 'single', 'keyword', '/manufacturers/auto-search');
    sourcingResultsPartNoSearchSelect = new ExternalPartNoSearchSelectComponent('sourcing-results-part-no-auto-search', 'SourcingResultsPartNo', 'single', 'keyword', '/auto-search/part-no', 3);
    sourcingResultsSupplierSearchSelect = new SupplierSearchSelectComponent('sourcing-results-supplier-auto-search', 'SourcingResultsSupplierId', 'single', 'keyword', '/companies/auto-search');
    sourcingResultsSupplierSearchSelect.on(
        "change", async ({ _, hiddenValue }) => {
            const currentCompanyId = companyId;
            companyId = hiddenValue || companyId;

            if (hiddenValue == '') {
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#sourcing-results-currency-code").text("");
                $("#message-currency").addClass("d-none");
            }
            else if (hiddenValue != '' && companyId == currentCompanyId) {
                await reloadCurrencyDropdown();
            }

            if (companyId != currentCompanyId && companyId != defaultIdValue) {
                shouldSelectSupplierDefaultBuyCurrency = true;

                await reloadCurrencyDropdown();
            }
        });
}

function resetSearchSelects() {
    sourcingResultsPackageSearchSelect.resetSearchSelect();
    sourcingResultsProductSearchSelect.resetSearchSelect();
    sourcingResultsManufacturerSearchSelect.resetSearchSelect();
    sourcingResultsPartNoSearchSelect.resetSearchSelect();
    sourcingResultsSupplierSearchSelect.resetSearchSelect();
}

async function fetchSourcingResults() {
    const selectedRowSourcingResults = $("#customer-requirement-sourcing-results-table")
        .DataTable()
        .row({ selected: true })
        .data();
    if (!selectedRowSourcingResults) return;

    const response = await GlobalTrader.ApiClient.getAsync(
        `/orders/customer-requirements/part-detail/sourcing-results/${selectedRowSourcingResults.id}`
    );
    if (!response?.success) {
        $("#add-edit-sourcing-results-dialog").dialog("close");
        return;
    }

    $("#SourcingResultsId").val(selectedRowSourcingResults.id);

    await handleSupplierInfo(response.data);
    await selectSourcingResultsPart(response.data.part);
    handleManufacturerInfo(response.data);
    handleProductInfo(response.data);
    handlePackageInfo(response.data);

    $("#add-edit-sourcing-results-form #rohs-dropdown").dropdown("select", response.data.rohs);
    $("#add-edit-sourcing-results-form #offer-status-dropdown").dropdown("select", response.data.offerStatusNo);
    $("#add-edit-sourcing-results-form #msl-dropdown").dropdown("select", response.data.mslLevelNo);

    $("#add-edit-sourcing-results-form #DateCode").val(response.data.dateCode);
    $("#add-edit-sourcing-results-form #Quantity").val(response.data.quantity);
    $("#add-edit-sourcing-results-form #Price").val(response.data.price).trigger("focusout");
    $("#add-edit-sourcing-results-form #Notes").val(response.data.notes);

    togglePartWatch(response.data.partWatchMatch);

    $("#add-edit-sourcing-results-dialog").dialog("instance").setLoading(false);
}

async function handleSupplierInfo(data) {
    if (data.supplierNo && data.supplierName) {
        sourcingResultsSupplierSearchSelect.selectItem({
            value: data.supplierNo,
            label: data.supplierName
        });
        const advisoryNote = (await GlobalTrader.ApiClient.getAsync(`companies/${data.supplierNo}/advisory-note`)).data;
        $("#add-edit-sourcing-results-form #label-supplier-container #SupplierName").html(`${data.supplierName}
                ${advisoryNote ? ButtonHelper.createAdvisoryNotesIconLabel(advisoryNote) : ""}`);
        $("#add-edit-sourcing-results-form").find("#label-supplier-container").removeClass("d-none");
        $("#add-edit-sourcing-results-form").find("#supplier-container").addClass("d-none");

        companyId = `${data.supplierNo}`;
        await reloadCurrencyDropdown();
        $("#add-edit-sourcing-results-form #currency-dropdown").dropdown("select", data.currencyNo);
    }
}

function handleManufacturerInfo(data) {
    if (data.manufacturerNo && data.manufacturerName) {
        sourcingResultsManufacturerSearchSelect.selectItem({
            value: data.manufacturerNo,
            label: data.manufacturerName
        });
    }
}

function handleProductInfo(data) {
    if (data.productNo && data.productDescription) {
        sourcingResultsProductSearchSelect.selectItem({
            value: data.productNo,
            label: data.productDescription
        });
    }
}

function handlePackageInfo(data) {
    if (data.packageNo && data.packageDescription) {
        sourcingResultsPackageSearchSelect.selectItem({
            value: data.packageNo,
            label: data.packageDescription
        });
    }
}

function togglePartWatch(isMatch) {
    if (isMatch) {
        $("#add-edit-sourcing-results-form #part-watch-wrapper").removeClass("d-none");
    }
    else {
        $("#add-edit-sourcing-results-form #part-watch-wrapper").addClass("d-none");
    }
}

async function fetchAddSourcingResults() {
    let selectedRows = null;

    if (currentSourcingPageType === sourcingPageTypeMap.RequirementDetails) {
        selectedRows = $("#customer-requirement-details-items")
            .DataTable()
            .rows({ selected: true })
            .data()
            .toArray();

        dataDetailRequirement = selectedRows.length === 1 ? selectedRows[0] : null;
    }
    else if (currentSourcingPageType === sourcingPageTypeMap.HUBRFQDetails) {

        dataDetailRequirement = window.sourcingResultsDetail.data ? window.sourcingResultsDetail.data : null;

    }

    customerRequirementId = dataDetailRequirement.id ?? dataDetailRequirement.customerRequirementId;

    const $select = $("#add-edit-sourcing-results-form #msl-dropdown");

    await selectSourcingResultsPart(dataDetailRequirement.part);

    if (dataDetailRequirement.manufacturerNo != null && dataDetailRequirement.manufacturerName != null) {
        sourcingResultsManufacturerSearchSelect.selectItem({
            value: dataDetailRequirement.manufacturerNo,
            label: dataDetailRequirement.manufacturerName
        });
    }

    if (dataDetailRequirement.productNo != null && dataDetailRequirement.productDescription != null) {
        sourcingResultsProductSearchSelect.selectItem({
            value: dataDetailRequirement.productNo,
            label: dataDetailRequirement.productDescription
        });
    }

    if (dataDetailRequirement.packageNo != null && dataDetailRequirement.packageDescription != null) {
        sourcingResultsPackageSearchSelect.selectItem({
            value: dataDetailRequirement.packageNo,
            label: dataDetailRequirement.packageDescription
        });
    }

    $("#add-edit-sourcing-results-form #rohs-dropdown").dropdown("select", dataDetailRequirement.rohs);

    let elapsed = 0;
    const interval = 100; 
    const timeout = 5000; 
    const waitForOptions = setInterval(() => {
        elapsed += interval;
        const matchedValue = $select.find("option").filter(function () {
            return $(this).text().trim() === dataDetailRequirement.msl;
        }).val();

        if (matchedValue !== undefined) {                       
            clearInterval(waitForOptions);
            $select.val(matchedValue).trigger("change");
        }

        if (elapsed >= timeout) {
            clearInterval(waitForOptions);
        }
    }, 100);

    $("#add-edit-sourcing-results-form #DateCode").val(dataDetailRequirement.dc);
    $("#add-edit-sourcing-results-form #Quantity").val(dataDetailRequirement.quantity);
    $("#add-edit-sourcing-results-form #Price").trigger("focusout");

    const currentDialog = $("#add-edit-sourcing-results-dialog").dialog("instance");
    currentDialog.setLoading(false);
}

function focusEdit() {
    $(sourcingResultsPartNoSearchSelect.searchInput).trigger("focus");
}

function focusFirstErrorInput() {
    const $form = $("#add-edit-sourcing-results-form");
    let $firstErrorField = $form.find(".is-invalid").first();

    if ($firstErrorField.is(":hidden")) {
        const fieldId = $firstErrorField.attr("id");
        const $visibleField = $form.find(`[data-input-value-id='${fieldId}']`);
        if ($visibleField.length) {
            $firstErrorField = $visibleField;
        }
    }

    if ($firstErrorField.length > 0) {
        $firstErrorField.trigger("focus");
    }
}

async function selectSourcingResultsPart(part) {
    if (!part) return;

    sourcingResultsPartNoSearchSelect.shoudHandleSearchInput = false;

    await sourcingResultsPartNoSearchSelect.selectItem({
        value: part,
        label: part
    });
}

function reloadSourcingResultsBox() {
    if (currentSourcingPageType === sourcingPageTypeMap.RequirementDetails) {
        $("#part-detail-sourcing-results-box .section-box-refesh-button").trigger("click");
    }
    else if (currentSourcingPageType === sourcingPageTypeMap.HUBRFQDetails) {
        $("#customer-requrement-sourcing-results-box .section-box-refesh-button").trigger("click");
    }
}