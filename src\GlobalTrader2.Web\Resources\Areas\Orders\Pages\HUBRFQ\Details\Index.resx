﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="NoData" xml:space="preserve">
    <value>No communication note</value>
  </data>
  <data name="NoDataHUBRFQItems" xml:space="preserve">
    <value>No HUBRFQ Items</value>
  </data>
  <data name="ExpediteNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ReqNo" xml:space="preserve">
    <value>Req No</value>
  </data>
  <data name="DLUP" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="CCUserId" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="SendToGroup" xml:space="preserve">
    <value>Send To Group</value>
  </data>
  <data name="ExportToPurchaseHUB" xml:space="preserve">
    <value>Send to Purchase Hub</value>
  </data>
  <data name="HUBRFQ" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="CloseMessage" xml:space="preserve">
    <value>Are you sure you would like to close this </value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="CloseTitle" xml:space="preserve">
    <value>Close HUBRFQ</value>
  </data>
  <data name="ViewTree" xml:space="preserve">
    <value>View Tree</value>
  </data>
  <data name="LastUpdated" xml:space="preserve">
    <value>Last updated:</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>Inhouse AS6081 Part Included?</value>
  </data>
  <data name="AddNewHUBRFQ" xml:space="preserve">
    <value>Add New HUBRFQ</value>
    <comment>Add New HUBRFQ</comment>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Delete Customer Requirement</value>
  </data>
  <data name="DeleteMessage" xml:space="preserve">
    <value>Are you sure you would like to Delete the selected item?</value>
  </data>
  <data name="BOMItem" xml:space="preserve">
    <value>BOM Item</value>
  </data>
  <data name="ApplyPartWatchForHUBIPO" xml:space="preserve">
    <value>Apply Part Watch For HUBIPO</value>
  </data>
  <data name="ApplyPartWatchConfirmMessage" xml:space="preserve">
    <value>Are you sure you would like to</value>
  </data>
  <data name="ApplyPartWatch" xml:space="preserve">
    <value>Apply Partwatch</value>
  </data>
  <data name="PartWatch applied successfully" xml:space="preserve">
    <value>PartWatch applied successfully</value>
  </data>
  <data name="ReleaseAll" xml:space="preserve">
    <value>Release All</value>
  </data>
  <data name="UnReleaseConfirmationTitle" xml:space="preserve">
    <value>Revoke HUBRFQ Item</value>
  </data>
  <data name="UnReleaseConfirmationMessage" xml:space="preserve">
    <value>Are you sure you would like to Revoke the selected item?</value>
  </data>
  <data name="Add/Edit" xml:space="preserve">
    <value>Add/Edit</value>
    <comment>Add/Edit</comment>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
    <comment>Delete</comment>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
    <comment>View</comment>
  </data>
  <data name="View_BOMQualification" xml:space="preserve">
    <value>PPV/ BOM Qualification View</value>
    <comment>PPV/ BOM Qualification View</comment>
  </data>
  <data name="NoQuestionPPVData" xml:space="preserve">
    <value>For visibility of Edit button. Please Add PPV/ BOM Qualification  Question,s from company Setting in Setup</value>
  </data>
  <data name="NoDataWasFound" xml:space="preserve">
    <value>Sorry, no data was found.</value>
  </data>
  <data name="errorDelDateAndProduct" xml:space="preserve">
    <value>Delivery date and product is required for all attached sourcing results</value>
  </data>
  <data name="errorMerginCanRelease" xml:space="preserve">
    <value>Some of the Sourcing Results having price issue kindly check and verify.</value>
  </data>
  <data name="Add_BOMQualification" xml:space="preserve">
    <value>PVV / BOM Qualification Add / Edit
</value>
  </data>
  <data name="Add_BOMQualification_Msg" xml:space="preserve">
    <value>Enter the details of the PVV / BOM Qualification and press
</value>
  </data>
  <data name="BomQualificationDelete" xml:space="preserve">
    <value>Delete PPV/ BOM Qualification</value>
  </data>
  <data name="BomQualificationDeleteDescription" xml:space="preserve">
    <value>Are you sure you would like to Delete this PPV/ BOM Qualification Question &amp; Answer?</value>
  </data>
  <data name="NoBidAll" xml:space="preserve">
    <value>No-Bid All</value>
  </data>
  <data name="Read more" xml:space="preserve">
    <value>Read more</value>
  </data>
  <data name="Read less" xml:space="preserve">
    <value>Read less</value>
  </data>
  <data name="No Quote found" xml:space="preserve">
    <value>No Quote found</value>
  </data>
  <data name="Quote Number" xml:space="preserve">
    <value>Quote Number</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="KubQuotePrice" xml:space="preserve">
    <value>Offer Amount (Quote's Unit Price &amp; Original HUB Offer)</value>
  </data>
  <data name="Converted to SO" xml:space="preserve">
    <value>Converted to SO</value>
  </data>
  <data name="No purchase found" xml:space="preserve">
    <value>No purchase found</value>
  </data>
  <data name="PO/IPO" xml:space="preserve">
    <value>PO/IPO</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="HUBRFQ Number" xml:space="preserve">
    <value>HUBRFQ Number</value>
  </data>
  <data name="Customer Target Price" xml:space="preserve">
    <value>Customer Target Price</value>
  </data>
  <data name="kubRequestQuote" xml:space="preserve">
    <value>Quote?</value>
  </data>
  <data name="No Offer found" xml:space="preserve">
    <value>No Offer found</value>
  </data>
  <data name="Date Added" xml:space="preserve">
    <value>Date Added</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="Stock ID" xml:space="preserve">
    <value>Stock ID</value>
  </data>
  <data name="No Stock found" xml:space="preserve">
    <value>No Stock found</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Error Edit Message" xml:space="preserve">
    <value>Please enter a value</value>
  </data>
  <data name="EditHUBRFQtitle" xml:space="preserve">
    <value>Edit Main HUBRFQ Information Details</value>
  </data>
  <data name="EditHUBRFQMessage" xml:space="preserve">
    <value>Enter the changed details for the HUBRFQ and press </value>
  </data>
  <data name="hubrfqDuplicateName" xml:space="preserve">
    <value>HUBRFQ with the same name already exists.</value>
  </data>
  <data name="Validate_Release_Message" xml:space="preserve">
    <value>Delivery date and product is required for the selected sourcing result(s)</value>
  </data>
  <data name="SendToSupplier" xml:space="preserve">
    <value>Send To Supplier</value>
  </data>
  <data name="SendToSupplierBody" xml:space="preserve">
    <value>Kindly find the attached HUBRFQ regarding our requirements.\nPlease update the price column of the sheet &amp; send us back for further processing.</value>
  </data>
  <data name="Add more suppliers" xml:space="preserve">
    <value>Add more suppliers</value>
  </data>
  <data name="Notification" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Recall No-Bid" xml:space="preserve">
    <value>Recall No-Bid</value>
  </data>
  <data name="Main Information" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Log" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="Uploaded Documents" xml:space="preserve">
    <value>Uploaded Documents</value>
  </data>
  <data name="HUBRFQ Items" xml:space="preserve">
    <value>HUBRFQ Items</value>
  </data>
  <data name="PPV/ BOM Qualification" xml:space="preserve">
    <value>PPV/ BOM Qualification</value>
  </data>
  <data name="Communication Note" xml:space="preserve">
    <value>Communication Note</value>
  </data>
  <data name="Notify others of this HUBRFQ" xml:space="preserve">
    <value>Notify others of this HUBRFQ</value>
  </data>
	<data name="Add New HUBRFQ Item" xml:space="preserve">
    <value>Add New HUBRFQ Item</value>
  </data>
	<data name="Select a requirement" xml:space="preserve">
    <value>Select a Requirement to add to this HUBRFQ</value>
  </data>
	<data name="customerRequirementNumber" xml:space="preserve">
  <value>Req</value>
</data>
	<data name="companyName" xml:space="preserve">
  <value>Company</value>
</data>
	<data name="PartNo" xml:space="preserve">
  <value>Part No</value>
</data>
	<data name="receivedDateText" xml:space="preserve">
  <value>Date</value>
</data>
	<data name="formatedPrice" xml:space="preserve">
  <value>Unit Price</value>
</data>
	<data name="bomName" xml:space="preserve">
  <value>Bom Name</value>
</data>
</root>