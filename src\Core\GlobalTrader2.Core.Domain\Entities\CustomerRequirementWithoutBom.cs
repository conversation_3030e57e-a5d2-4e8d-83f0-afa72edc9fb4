﻿using System.ComponentModel.DataAnnotations;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class CustomerRequirementWithoutBom
    {
        [Key]
        public int CustomerRequirementId { get; set; }
        public int CustomerRequirementNumber { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string Part { get; set; } = string.Empty;
        public byte? ROHS { get; set; }
        public DateTime? ReceivedDate { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public string? CurrencyCode { get; set; }
        public int RowCnt { get; set; }
        public int? BOMNo { get; set; }
        public string? BOMName { get; set; }
        public long RowNum { get; set; }
    }
}
