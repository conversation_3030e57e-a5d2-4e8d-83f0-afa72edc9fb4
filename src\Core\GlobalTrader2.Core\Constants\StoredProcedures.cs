namespace GlobalTrader2.Core.StoreName
{
    public static class StoredProcedures
    {
        public const string Login_byMasterLogin = "[dbo].[usp_login_byMasterLogin]";
        public const string Select_Login_by_Name = "[dbo].[usp_select_Login_by_Name]";
        public const string Select_LoginPreference_by_Login = "[dbo].[usp_select_LoginPreference_by_Login]";
        public const string SelectAll_RecentlyViewed_for_User = "[dbo].[usp_selectAll_RecentlyViewed_for_User]";
        public const string Select_Login_by_MasterLoginNo = "[dbo].[usp_select_Login_by_MasterLoginNo]";
        public const string Select_ClientByMaster = "[dbo].[usp_select_ClientByMaster]";
        public const string SelectAll_Client_Active = "[dbo].[usp_selectAll_Client_Active]";

        #region Security Manager
        public const string SelectAll_SecurityFunction_Permissions_By_Login_And_SiteSection = "[dbo].[usp_selectAll_SecurityFunction_Permissions_by_Login_and_SiteSection]";
        public const string SelectAll_SecurityFunction_GlobalPermissions_By_Login_And_SiteSection = "[dbo].[usp_selectAll_SecurityFunction_GlobalPermissions_by_Login_and_SiteSection]";
        public const string SelectAll_SecurityFunction_General_Permissions_By_Login = "[dbo].[usp_selectAll_SecurityFunction_General_Permissions_by_Login]";
        public const string SelectAll_SecurityFunction_Section_Permissions_By_Login = "[dbo].[usp_selectAll_SecurityFunction_Section_Permissions_by_Login]";
        public const string SelectAll_SecurityFunction_Page_Permissions_By_Page_And_Login = "[dbo].[usp_selectAll_SecurityFunction_Page_Permissions_by_Page_and_Login]";
        public const string SelectAll_GlobalSecurityFunction_Page_Permissions_By_Page_And_Login = "[dbo].[usp_selectAll_GlobalSecurityFunction_Page_Permissions_by_Page_and_Login]";
        public const string Check_Admin_Permissions_By_Login = "usp_check_admin_Permissions_by_Login";
        #endregion

        public const string Select_SecurityFunction_Permission_By_Login = "[dbo].[usp_select_SecurityFunction_Permission_by_Login]";
        public const string Logout_Login = "[dbo].usp_logout_Login";
        public const string SelectAll_Login_for_Client_including_Disabled = "[dbo].usp_selectAll_Login_for_Client_including_Disabled";
        public const string Update_Login_Inactive = "[dbo].usp_update_Login_Inactive";
        public const string SelectAll_Login_for_Client = "[dbo].usp_selectAll_Login_for_Client";
        public const string Add_SecurityUser = "[dbo].[usp_insert_Login]";
        public const string Edit_SecurityUser = "[dbo].[usp_update_Login]";
        public const string Delete_SecurityUser = "[dbo].[usp_delete_Login]";
        public const string Update_Password_SecurityUser = "[dbo].[usp_update_Login_Password]";
        public const string Dropdown_Divisions_By_ClientNo = "[dbo].[usp_dropdown_Division_for_Client]";
        public const string Dropdown_Teams_By_ClientNo = "[dbo].[usp_dropdown_Team_for_Client]";
        public const string Dropdown_Printers_By_ClientNo = "[dbo].[usp_dropdown_PrinterAll]";
        public const string Select_Login_Name = "[dbo].[usp_select_Login_Name]";
        public const string SecurityGroup_For_Login = "[dbo].[usp_selectAll_SecurityGroup_for_Login]";
        public const string Check_LoginName_Exists = "[dbo].[usp_Login_Exists]";
        public const string InsertRecentlyViewed = "[dbo].usp_insert_RecentlyViewed";
        public const string Dropdown_Login_For_Client = "[dbo].[usp_dropdown_Login_for_Client]";
        public const string UpdateCompanyTransferAccounts = "[dbo].[usp_update_Company_TransferAccounts]";
        public const string SelectAllSecurityGroupForClient = "[dbo].[usp_selectAll_SecurityGroup_for_Client]";
        public const string Dropdown_Login_For_PurchaseHubClient = "[dbo].[usp_dropdown_Login_for_PurchaseHubClient]";

        public const string Get_All_Terms_From_Client = "[dbo].[usp_selectAll_Terms_for_Client]";
        public const string Insert_Term = "[dbo].[usp_insert_Terms]";
        public const string Update_Term = "[dbo].[usp_update_Terms]";
        public const string Get_Term = "[dbo].[usp_select_Terms]";
        public const string Get_Term_Warning_Message = "[dbo].[usp_select_TermsWarningmessage]";

        public const string SelectAll_Teams_By_ClientNo = "[dbo].[usp_selectAll_Team_for_Client]";
        public const string SelectAll_Login_For_Team = "[dbo].[usp_selectAll_Login_for_Team]";
        public const string Insert_Team = "[dbo].usp_insert_Team";
        public const string Update_Team = "[dbo].usp_update_Team";
        public const string Select_Team = "[dbo].usp_select_Team";
        public const string Delete_Team = "[dbo].usp_delete_Team";

        public const string SelectAll_Setting_Values = "[dbo].[usp_selectAll_Setting_values]";
        public const string Update_Client_OwnDataVisibleToOthers = "[dbo].usp_update_Client_OwnDataVisibleToOthers";
        public const string Update_SalesOrder_AuthoriseAllUnauthorisedOrders = "[dbo].usp_update_SalesOrder_AuthoriseAllUnauthorisedOrders";
        public const string Update_Client_UpdateAutoInvoiceExport = "[dbo].usp_update_Client_UpdateAutoInvoiceExport";

        public const string SelectAll_Printer_for_Client = "[dbo].[usp_selectAll_Printer_for_Client]";
        public const string Insert_Printer = "[dbo].[usp_insert_Printer]";
        public const string Update_Printer = "[dbo].[usp_update_Printer]";

        public const string SelectAll_RestrictedManufacturer_for_Client = "[dbo].[usp_selectAll_RestrictedManufacturer_for_Client]";
        public const string Get_RestrictedManufacturersByMfrIds = "[dbo].[usp_get_RestrictedManufacturerByMfrIds]";
        public const string Insert_MultipleRestrictedManufacturers = "[dbo].[usp_insert_MultipleRestrictedManufacturers]";
        public const string Update_ActivateMultipleRestrictedManufacturer = "[dbo].[usp_update_ActivateMultipleRestrictedManufacturer]";
        public const string Update_MultipleRestrictedManufacturers = "[dbo].[usp_update_MultipleRestrictedManufacturer]";

        public const string Delete_MailGroup = "[dbo].[usp_delete_MailGroup]";
        public const string Delete_CurrencyRate = "[dbo].[usp_delete_CurrencyRate]";

        public const string Insert_SecurityGroup = "[dbo].[usp_insert_SecurityGroup]";
        public const string Insert_SecurityGroupSecurityFunctionPermission_StandardPermissions = "[dbo].[usp_insert_SecurityGroupSecurityFunctionPermission_StandardPermissions]";
        public const string Update_SecurityGroup = "[dbo].[usp_update_SecurityGroup]";
        public const string Delete_SecurityGroup = "[dbo].[usp_delete_SecurityGroup]";
        public const string Insert_SecurityGroup_Clone = "[dbo].[usp_insert_SecurityGroup_Clone]";

        public const string Insert_SecurityGroupLogin = "[dbo].[usp_insert_SecurityGroupLogin]";
        public const string Delete_SecurityGroupLogin = "[dbo].[usp_delete_SecurityGroupLogin]";
        public const string SelectAll_SecurityFunction_General_Permissions_by_SecurityGroup = "[dbo].[usp_selectAll_SecurityFunction_General_Permissions_by_SecurityGroup]";
        public const string SelectAll_SecurityLevelFunction_General_Permissions_by_SecurityGroup = "[dbo].[usp_selectAll_SecurityLevelFunction_General_Permissions_by_SecurityGroup]";
        public const string SelectAll_SecurityFunction_General = "[dbo].[usp_selectAll_SecurityFunction_General]";
        public const string SelectAll_SecurityFunction_Section_Permissions_by_SecurityGroup = "[dbo].[usp_selectAll_SecurityFunction_Section_Permissions_by_SecurityGroup]";
        public const string SelectAll_SecurityFunction_Section = "[dbo].[usp_selectAll_SecurityFunction_Section]";
        public const string SelectAll_SitePage_having_SecurityFunction_by_SiteSection = "[dbo].[usp_selectAll_SitePage_having_SecurityFunction_by_SiteSection]";
        public const string SelectAll_SecurityFunction_Page_Permissions_by_SecurityGroup_and_Page = "[dbo].[usp_selectAll_SecurityFunction_Page_Permissions_by_SecurityGroup_and_Page]";
        public const string SelectAll_SecurityFunction_by_SitePage = "[dbo].[usp_selectAll_SecurityFunction_by_SitePage]";
        public const string SelectAllSecurityGroupLoginForSecurityGroup = "[dbo].[usp_selectAll_SecurityGroupLogin_for_SecurityGroup]";

        public const string SelectAll_ExchangeRate_For_LocalCurrency = "[dbo].[usp_selectAll_ExchangeRate_for_LocalCurrency]";

        public const string Select_Login = "[dbo].[usp_select_Login]";
        public const string Update_Setting = "[dbo].[usp_update_Setting]";
        public const string Insert_BulkSecurityGroupSecurityFunctionPermission = "[dbo].[usp_insert_BulkSecurityGroupSecurityFunctionPermission]";
        public const string SelectAllSecurityFunctionReport = "[dbo].[usp_selectAll_SecurityFunction_Report]";
        public const string SelectAllSecurityFunctionReportPermissionsBySecurityGroup = "[dbo].[usp_selectAll_SecurityFunction_Report_Permissions_by_SecurityGroup]";
        public const string Select_Warehouse = "[dbo].[usp_select_Warehouse]";
        public const string SelectAll_TabSecurityFunction_General_Permissions_by_SecurityGroup = "[dbo].[usp_selectAll_TabSecurityFunction_General_Permissions_by_SecurityGroup]";
        public const string Get_Dropdown_ExchangeRate_GlobalCurrencyList = "[dbo].[usp_dropdown_GlobalCurrencyList]";
        public const string Insert_LocalCurrency = "[dbo].[usp_insert_LocalCurrency]";
        public const string Update_LocalCurrency = "[dbo].[usp_update_LocalCurrency]";
        public const string Update_Address = "[dbo].[usp_update_Address]";
        public const string Delete_Address = "[dbo].[usp_delete_Address]";
        public const string Select_Address = "[dbo].[usp_select_Address]";
        public const string Add_Address = "[dbo].[usp_insert_Address]";
        public const string SelectAll_Warehouse_for_Client = "[dbo].[usp_selectAll_Warehouse_for_Client]";
        public const string Update_Warehouse = "[dbo].[usp_update_Warehouse]";
        public const string Add_Warehouse = "[dbo].[usp_insert_Warehouse]";
        public const string Get_Default_Warehouse_By_Client = "[dbo].[usp_GetDefaultWarehouseByClientId]";
        public const string Clear_Default_Warehouse_By_Client = "[dbo].[usp_update_Warehouse_ClearDefaults]";
        public const string Update_Warehouse_SetDefault = "[dbo].[usp_update_Warehouse_SetDefault]";
        public const string Dropdown_Countries_By_ClientNo = "[dbo].[usp_dropdown_Country_for_Client]";
        public const string Dropdown_LocalCurrencies_By_ClientNo = "[dbo].[usp_fetchLocalCurrency]";
        public const string Dropdown_BuyCurrency_By_ClientNo = "[dbo].[usp_dropdown_Currency_Buy_For_Client_And_GlobalCurrencyNo]";
        public const string Dropdown_SellCurrency_By_ClientNo = "[dbo].[usp_dropdown_Currency_Sell_For_Client_And_GlobalCurrencyNo]";
        public const string Insert_GlobalCountryList = "[dbo].[usp_insert_GlobalCountryList]";
        public const string Insert_Incoterm = "[dbo].[usp_insert_Incoterm]";
        public const string Insert_Reason = "[dbo].[usp_insert_Reason]";
        public const string Insert_Cretificate = "[dbo].[usp_insert_Certificate]";
        public const string Select_Detail_ShipVia = "[dbo].[usp_select_ShipVia]";

        public const string SelectAll_Sequencer_for_Client = "[dbo].[usp_selectAll_Sequencer_for_Client]";
        public const string Select_EmailComposerByClientNo = "[dbo].[usp_select_EmailComposerByClientNo]";
        public const string Insert_EmailComposer = "[dbo].[usp_insert_EmailComposer]";
        public const string Update_EmailComposer = "[dbo].[usp_update_EmailComposer]";
        public const string Insert_IndustryType = "[dbo].[usp_insert_IndustryType]";
        public const string Insert_CurrencyRate = "[dbo].[usp_insert_CurrencyRate]";

        public const string Get_Client_Invoice_Header_Details = "[dbo].[usp_GetClientInvoiceHeaderDetails]";
        public const string SelectAll_Division_for_Client = "[dbo].[usp_selectAll_Division_for_Client]";
        public const string Select_Address_for_Division = "[dbo].[usp_select_Address_for_Division]";

        public const string SelectAll_Product_for_Client = "[dbo].[usp_selectAll_Product_for_Client]";

        public const string SelectAll_SystemDocumentFooter_for_Client = "[dbo].[usp_selectAll_SystemDocumentFooter_for_Client]";
        public const string Select_Client = "[dbo].[usp_select_Client]";

        public const string Update_SystemDocumentFooter = "[dbo].[usp_update_SystemDocumentFooter]";
        public const string Select_Login_For_All_Client = "[dbo].[usp_dropdown_Login_for_AllClient]";
        public const string Dropdown_Currency_Buy_For_Client = "[dbo].[usp_dropdown_Currency_Buy_For_Client]";


        #region Global Security Groups
        public const string SelectAll_SecurityFunction_Section_Permissions_by_GlobalSecurityGroup = "[dbo].[usp_selectAll_SecurityFunction_Section_Permissions_by_GlobalSecurityGroup]";
        public const string SelectAll_SecurityFunction_Page_Permissions_by_GlobalSecurityGroup_and_Page = "[dbo].[usp_selectAll_SecurityFunction_Page_Permissions_by_GlobalSecurityGroup_and_Page]";
        public const string SelectAll_GlobalSecurityFunction_by_SitePage = "[dbo].[usp_selectAll_GlobalSecurityFunction_by_SitePage]";
        #endregion

        public const string SelectAll_SystemWarningMessage_for_Client = "[dbo].[usp_selectAll_SystemWarningMessage_for_Client]";
        public const string Select_SystemWarningMessage = "[dbo].[usp_select_SystemWarningMessage]";
        public const string Update_SystemWarningMessage = "[dbo].[usp_update_SystemWarningMessage]";
        public const string AutoSearch_Company = "[dbo].[usp_autosearch_Company]";
        public const string AutoSearch_Product = "[dbo].[usp_autosearch_Product]";
        public const string AutoSearch_Manufacturer = "[dbo].[usp_autosearch_Manufacturer]";
        public const string AutoSearch_LyticaManufacturer = "[dbo].[usp_autosearch_LyticaManufacturer]";
        public const string AutoSearch_Country = "[dbo].[usp_autosearch_Country]";
        public const string CheckRestricted_Manufacturer = "[dbo].[usp_Select_Search_for_RestrictedManufacture]";
        public const string Insert_SystemWarningMessage = "[dbo].[usp_insert_SystemWarningMessage]";
        public const string CheckDuplicate_SystemWarningMessage = "[dbo].[usp_CheckDuplicate_SystemWarningMessage]";

        public const string Insert_Division = "[dbo].[usp_insert_Division]";
        public const string Delete_Division = "[dbo].[usp_delete_Division]";
        public const string SelectAll_Login_for_Division = "[dbo].[usp_selectAll_Login_for_Division]";

        public const string SelectAllMasterLoginSetup = "[dbo].[usp_select_MasterLoginSetup_All]";
        public const string UpdateMasterLogin = "[dbo].[usp_update_MasterLoginDetail]";
        public const string Insert_Status_Reason = "[dbo].[usp_insert_LabelSetupItem]";
        public const string Update_Status_Reason = "[dbo].[usp_update_LabelSetupItem]";
        public const string SelectAll_ClientSetup = "[dbo].[usp_select_ClientSetup_All]";
        public const string Update_ClientSetup = "[dbo].[usp_update_ClientDetail]";

        public const string InsertGlobalProduct = "[dbo].[usp_insert_GlobalProduct]";
        public const string UpdateGlobalProduct = "[dbo].[usp_update_GlobalProduct]";

        public const string Insert_GlobalDutyRate = "[dbo].[usp_insert_GlobalDutyRate]";
        public const string Insert_ManufacturerLink = "[dbo].[usp_insert_ManufacturerLink_All_Client]";
        public const string Insert_AS6081_InsertRiskOfSupplier = "[dbo].[usp_AS6081_InsertRiskOfSupplier]";

        public const string Insert_AS6081_Type_Of_Supplier = "[dbo].[usp_AS6081_InsertTypeOfSupplier]";
        public const string Insert_EntertainmentType = "[dbo].[usp_insert_EntertainmentType]";
        public const string Insert_AS6081_ReasonForChosenSupplier = "[dbo].[usp_AS6081_InsertReasonForChosenSupplier]";

        #region Customer Requirements
        public const string SelectAll_CustomerRequirement = "[dbo].[usp_datalistnugget_CustomerRequirement]";
        public const string SelectAll_CustomerRequirementDetail = "[dbo].[usp_selectAll_CustomerRequirement_for_CustomerRequirement]";
        public const string Get_Multiple_AdvisoryNotes = "[dbo].[usp_get_multiple_AdvisoryNotes]";
        public const string Get_CurrentAtDate = "[dbo].[usp_select_CurrencyRate_Current_at_Date]";
        public const string Get_LyticaAPIData_ByKey = "[dbo].[usp_Get_LyticaAPIData_ByKey]";
        public const string Select_CustomerRequirement_For_Page = "[dbo].[usp_select_CustomerRequirement_for_Page]";
        public const string Select_CustomerRequirement = "[dbo].[usp_select_CustomerRequirement]";
        public const string Get_ProductMessage = "[dbo].[usp_GetProductMessage]";
        public const string Get_HazardousNote = "[dbo].[usp_select_SystemDocumentFooter_for_Client_and_Document]";
        public const string SelectAll_LastGetCustPartDetail = "[dbo].[usp_LastGetCustPartDetail]";
        public const string Save_Ihs_Part_Detail = "[dbo].[usp_insert_IHSApiXML_Or_select]";
        public const string Itemsearch_CustomerRequirement = "[dbo].[usp_itemsearch_CustomerRequirement]";
        public const string Select_For_MappedPartWithECCNCode = "[dbo].[usp_Search_for_MappedPartWithECCNCode]";
        public const string Kub_Start_CacheProcessForAddRequirement = "[dbo].[usp_KubStartCacheProcessForAddRequirement]";
        public const string Kub_Start_CacheProcessForBrowseRequirement = "[dbo].[usp_KubStartCacheProcessForBrowseRequirement]";
        public const string Kub_Get_AveragePriceDetails = "[dbo].[usp_KubGetAveragePriceDetails]";
        public const string Kub_Get_TotalLineInvoicedDetails = "[dbo].[usp_KubGetTotalLineInvoicedDetails]";
        public const string Kub_Get_Last10QuoteDetails = "[dbo].[usp_KubGetLast10QuoteDetails]";
        public const string Kub_Get_MainProductGroupsDetails = "[dbo].[usp_KubGetMainProductGroupsDetails]";
        public const string Kub_Get_CountryWiseSalesDetails = "[dbo].[usp_KubGetCountryWiseSalesDetails]";
        public const string Kub_Get_Top3BuyPriceDetails = "[dbo].[usp_KubGetTop3BuyPriceDetails]";
        public const string Kub_Get_LastTop10QuoteForBom = "[dbo].[usp_select_KubAssistanceLastTop10QuoteForBOM]";
        public const string Kub_Get_LastTop20CusRqForBom = "[dbo].[usp_select_KubAssistanceLastTop20CusRqForBOM]";
        public const string Kub_Get_Top3BuyPriceForBom = "[dbo].[usp_get_Top3BuyPrice_BOM_KUB]";
        public const string Kub_Get_StockDetailsForBOMPart = "[dbo].[usp_select_StockDetailsForBOMPart]";

        public const string Get_Manufacturer_Advisory = "[dbo].[usp_select_ManufacturerAdvisoryNotes]";
        public const string Insert_Customer_Requirements = "[dbo].[usp_insert_CustomerRequirement]";
        public const string Insert_Customer_Requirements_as_AllAlternate = "[dbo].[usp_insert_CustomerRequirement_as_AllAlternate]";
        public const string Insert_Ihs_Part_List = "[dbo].[usp_insert_IHSApiXML]";
        public const string Search_Ihs_Part_List = "[dbo].[usp_itemsearch_GRIDBindFrom_IHSAPI]";
        public const string Search_Ihs_Part_Detail = "[dbo].[usp_Search_for_IHSPartDetails]";
        public const string Partwatch_Matching_Offer = "[dbo].[usp_partwatch_matching_Offer]";
        public const string Partwatch_HUBIPO_Matching_Offer = "[dbo].[usp_partwatch_HUBIPO_matching_Offer]";
        public const string Update_CustomerRequirement_Close = "[dbo].[usp_update_CustomerRequirement_Close]";
        public const string Update_CustomerRequirementAlternate_Status = "[dbo].[usp_update_CustReqAlternate_Status]";
        public const string Insert_AlternateCustomerRequirement = "[dbo].[usp_insert_CustomerRequirement_as_Alternate]";
        public const string Update_CustomerRequirement = "[dbo].[usp_update_CustomerRequirement]";
        public const string Update_CustomerRequirement_NoBid = "[dbo].[usp_update_CustomerRequirement_NoBid]";
        public const string Update_CustomerRequirement_RecallNoBid = "[dbo].[usp_update_CustomerRequirement_RecallNoBid]";
        public const string Update_CustRequirementByBomID = "[dbo].[usp_update_CustRequirementByBomID]";
        public const string UnRelease_CustomerRequirement_Bom = "[dbo].[usp_UnRelease_CustomerRequirement_Bom]";
        public const string Get_RLPart = "[dbo].[usp_select_PartFoundInReverseLogistics]";
        public const string Get_PartLinesRL = "[dbo].[usp_Notifiy_RLVendor]";
        public const string SelectAll_SourcingResult_for_CustomerRequirement = "[dbo].[usp_selectAll_SourcingResult_for_CustomerRequirement]";
        public const string SelectAll_SourcingResult_for_BOMCustomerRequirement = "[dbo].[usp_selectAll_SourcingResult_for_BOMCustomerRequirement]";
        public const string Add_SourcingResult = "[dbo].[usp_insert_SourcingResult]";
        public const string UpsertLyticaAPI = "[dbo].[usp_UpsertLyticaAPI]";
        public const string Get_LyticaDataOnHUBRFQ_ByPartMfr = "[dbo].[usp_GetLyticaDataOnHUBRFQ_ByPartMfr]";
        public const string Select_Company = "[dbo].[usp_select_Company]";
        public const string Insert_CloneRequirementDataHUBRFQ = "[dbo].[usp_insert_CloneRequirementDataHUBRFQ]";
        public const string Insert_CloneRequirementDataHUB = "[dbo].[usp_insert_CloneRequirementDataHUB]";
        public const string Update_SourcingResult = "[dbo].[usp_update_SourcingResult]";
        public const string Select_SourcingResult = "[dbo].[usp_select_SourcingResult]";
        public const string GetAll_SaleCompany = "[dbo].[usp_autosearch_SaleCompany]";
        public const string Partwatch_Matching_Requirement = "[dbo].[usp_partwatch_matching_Requirement]";
        public const string Delete_HubPartWatchMatch = "[dbo].[usp_Delete_HubPartWatchMatch]";

        #endregion

        #region HubRFQ
        public const string Insert_BOM = "[dbo].[usp_insert_BOM]";
        public const string Get_BOM = "[dbo].[usp_select_BOM]";
        public const string CheckBOMHasImportFile = "[dbo].[usp_check_BOMHasImportFile]";
        public const string Get_EmailListByGroupName = "[dbo].[usp_selectAll_MailGroupMember_by_GroupName]";
        public const string Get_EmailListByGroup = "[dbo].[usp_selectAll_MailGroupMember_by_Group]";
        public const string Get_ReverseLogistics_MailGroup = "[dbo].[usp_Get_ReverseLogistics_MailGroup]";
        public const string GetAll_HUBRFQForMail = "[dbo].[usp_selectCusReqForMail]";
        public const string GetAll_HUBRFQHasRLStock = "[dbo].[usp_Get_CusReq_Having_Available_RLStock]";
        public const string Select_BOM = "[dbo].[usp_select_BOM]";
        public const string Select_BOM_For_Page = "[dbo].[usp_select_BOM_for_Page]";
        public const string Select_DataListNugget_BOM = "[dbo].[usp_datalistnugget_BOM]";
        public const string Select_DataListNugget_PHBOM = "[dbo].[usp_datalistnugget_PHBOM]";
        public const string Select_DataListNugget_PHBOMAssign = "[dbo].[usp_datalistnugget_PHBOMAssign]";
        public const string Select_DataListNugget_CustomerRequirementForHUBRFQ = "[dbo].[usp_datalistnugget_CustomerRequirementForHUBRFQ_v2]";
        public const string Select_AS6081_CustomerRequirementAssignforAS6081Tab = "[dbo].[usp_AS6081_CustomerRequirementAssignforAS6081Tab]";
        public const string Select_AS6081_DataListNugget_PHBOMAssign = "[dbo].[usp_AS6081_datalistnugget_PHBOMAssign]";
        public const string Select_AS6081_DataListNugget_CustomerRequirementAssign = "[dbo].[usp_AS6081_datalistnugget_CustomerRequirementAssign]";
        public const string GetAll_HUBRFQCommunicationNote = "[dbo].[usp_select_HUBRFQ_Expedite]";
        public const string Update_BOM_NoBid = "[dbo].[usp_update_BOM_NoBid]";
        public const string Release_All_BOM = "[dbo].[usp_update_BOM_Release]";
        public const string Delete_CustomerRequirement_Bom = "[dbo].[usp_delete_CustomerRequirement_Bom]";
        public const string SelectAll_CustomerRequirement_for_BOM = "[dbo].[usp_selectAll_CustomerRequirement_for_BOM]";
        public const string GetAll_DataTemp = "[dbo].[GetAll_DataTemp]";
        public const string GetBOMExcelStockHeaderColumnFrom = "[dbo].[usp_GetBOMExcelStockHeaderColumnFrom]";
        public const string GetExcelUpload_Error_BomImport = "[dbo].[usp_ExcelUpload_Error_BomImport]";
        public const string Select_ImportBOMData = "[dbo].[usp_Select_ImportBOMData]";
        public const string InsertUpdate_Temp_PVVAnswer = "[dbo].[usp_InsertUpdate_Temp_PVVAnswer]";
        public const string Update_BOM_POHubQuote = "[dbo].[usp_update_BOM_POHubQuote]";
        public const string Autosearch_Company_for_POApproveSuppliers = "[dbo].[usp_autosearch_Company_for_POApproveSuppliers]";
        public const string Create_SaveMailLog = "[dbo].[usp_create_SaveMailLog]";
        public const string Insert_CSVExportLog = "[dbo].[usp_insert_CSVExportLog]";
        public const string GetAll_PVVBOM = "[dbo].[usp_selectAll_PVVBOM]";
        public const string SelectAll_PVVBOM_Question = "[dbo].[Usp_SelectAll_PVVBOM_Question]";
        public const string GetAll_TempPVVBOMQuestion = "[dbo].[usp_selectAll_Temp_PVVBOM_Question]";
        public const string Update_PVVBOM = "[dbo].[usp_InsertUpdate_PVVAnswer]";
        public const string Select_CustomerRequirements_for_BOM = "[dbo].[usp_select_CustomerRequirements_for_BOM]";
        public const string Select_PurchaseRequestLineDetails = "[dbo].[usp_select_PurchaseRequestLineDetails]";
        public const string Update_BOM = "[dbo].[usp_update_BOM]";
        public const string Get_BOMImportedSourcingResults = "[dbo].[usp_get_BOMRawData_ForCorrection_v2]";
        public const string Correct_HubSourcingTempData = "[dbo].[usp_correct_HubSourcingTempData_v2]";
        public const string GetAll_Active_HUBRFQ_Clients = "[dbo].[usp_GSA_selectAll_Client_HubRfq]";
        public const string Insert_HUBRFQMainInfo_ExpediteNote = "[dbo].[Usp_Insert_HUBRFQMainInfo_ExpediteNote]";
        public const string Get_BOMSourcingResultForRelease = "[dbo].[usp_selectAll_SourcingResult_for_AllRelease]";
        public const string Get_ListForBOMSourcingResult = "[dbo].[usp_selectAll_SourcingResult_for_BOME]";
        public const string Get_SourcingLog = "[dbo].[usp_select_SourcingResultLogDetail]";
        public const string Select_CustomerRequirementBOM = "[dbo].[usp_select_CustomerRequirementBOM]";
        public const string Select_KubAssistanceForBOMManager = "[dbo].[usp_select_KubAssistanceForBOMManager]";
        public const string Check_KubEnableForBOMPart = "[dbo].[usp_check_KubEnableForBOMPart]";
        public const string Itemsearch_CustRequirementWithoutBOM = "[dbo].[usp_itemsearch_CustRequirementWithoutBOM]";

        #endregion
        #region Price Request
        public const string DataListNugget_PurchaseRequestLine = "[dbo].[usp_datalistnugget_PurchaseRequetLine]";
        public const string Select_PurchaseRequest = "[dbo].[usp_select_PurchaseRequest]";
        public const string SelectAll_PurchaseRequestLine = "[dbo].[usp_selectAll_PurchaseRequestLine]";
        public const string Select_PurchaseRequest_For_Page = "[dbo].[usp_select_PurchaseRequest_for_Page]";
        public const string Select_PurchaseRequestLineDetail = "[dbo].[usp_selectAll_PurchaseRequestLineDetail]";
        public const string Select_CsvImportLog = "[dbo].[usp_CsvImportLog]";
        public const string Select_CsvBomImportLog = "[dbo].[usp_CsvBomImportLog]";
        #endregion
        #region System Setup
        public const string Insert_Client = "[dbo].[usp_insert_Client]";
        public const string Insert_MailGroup = "[dbo].[usp_insert_MailGroup]";
        public const string Delete_Client = "[dbo].[usp_delete_Client]";
        public const string Delete_Currency = "[dbo].[usp_delete_Currency]";
        public const string SelectAllClient = "[dbo].[usp_selectAll_Client]";
        public const string UpdateClientInActive = "[dbo].[usp_update_Client_Inactive]";
        public const string Delete_Session = "[dbo].[usp_delete_Session]";
        public const string Select_Setting_Value = "[dbo].[usp_select_Setting_Value]";

        #endregion

        #region To Do List
        public const string Select_All_ToDoListTask = "[dbo].[usp_selectall_toDoListTask]";
        public const string DropDown_ToDoListType = "[dbo].[usp_dropdown_ToDoListType]";
        public const string Insert_ToDoItem = "[dbo].[usp_insert_ToDo]";
        public const string Update_DataListNuggetState_for_DLN_and_Login = "[dbo].[usp_update_DataListNuggetState_for_DLN_and_Login]";
        public const string Select_DataListNuggetState_for_DLN_and_Login = "[dbo].[usp_select_DataListNuggetState_for_DLN_and_Login]";
        public const string Select_ToDoItem = "[dbo].[usp_select_ToDo]";
        public const string Update_ToDoItem = "[dbo].[usp_update_ToDo]";
        public const string SelectAll_ToDo_Alert_for_Login = "[dbo].[usp_selectAll_ToDo_Alert_for_Login]";
        public const string Update_ToDo_Dismiss = "[dbo].[usp_update_ToDo_Dismiss]";
        public const string Update_ToDo_Snooze = "[dbo].[usp_update_ToDo_Snooze]";
        #endregion

        #region Mail Messages
        public const string SelectAll_MailMessage_For_Recipient_By_Folder = "[dbo].[usp_selectAll_MailMessage_for_Recipient_by_Folder]";
        public const string SelectAll_MailMessage_For_Recipient_By_Folder_Archive = "[dbo].[usp_selectAll_MailMessage_for_Recipient_by_Folder_Archive]";
        public const string Count_MailMessage_New_For_Recipient_By_Folder = "[dbo].[usp_count_MailMessage_New_for_Recipient_by_Folder]";
        public const string Count_MailMessage_New_For_Recipient_By_Folder_Archive = "[dbo].[usp_count_MailMessage_New_for_Recipient_by_Folder_Archive]";
        public const string Delete_Mail_MessageFolder = "[dbo].[usp_delete_MailMessageFolder]";
        public const string Select_MailMessage = "[dbo].[usp_select_MailMessage]";
        public const string SelectAll_ToDo_for_MailMessage = "[dbo].[usp_selectAll_ToDo_for_MailMessage]";
        public const string Autosearch_Login_For_Mail = "[dbo].[usp_autosearch_Login_for_Mail]";
        public const string SelectAll_MailMessage_for_Recipient = "[dbo].[usp_selectAll_MailMessage_for_Recipient]";
        public const string SelectAll_MailMessage_New_and_Unnotified_for_Recipient = "[dbo].[usp_selectAll_MailMessage_New_and_Unnotified_for_Recipient]";
        public const string Select_ECCN_Restricted_Mail_Group = "[dbo].[usp_get_ECCNRestrictedUseNotifyMailGroupNo]";
        #endregion

        #region
        public const string Update_GlobalProductGroup = "[dbo].[usp_update_GlobalProductName]";
        #endregion

        #region Quotes
        public const string Select_Quote = "[dbo].[usp_select_Quote]";
        public const string Itemsearch_QuoteLine = "[dbo].[usp_itemsearch_QuoteLine]";
        #endregion


        #region Contact
        public const string SelectAll_Contact_for_Company = "[dbo].[usp_selectAll_Contact_for_Company]";
        public const string Select_TabSecurityFunction_Permission_by_Login = "[dbo].[usp_select_TabSecurityFunction_Permission_by_Login]";
        public const string Select_TabSecurityFunction_CompanyPermission_by_Login = "[dbo].[usp_select_TabSecurityFunction_CompanyPermission_by_Login]";
        public const string Select_Contact_for_Page = "[dbo].[usp_select_Contact_for_Page]";
        public const string Select_Company_for_Page = "[dbo].[usp_select_Company_for_Page]";
        public const string Datalistnugget_Contact = "[dbo].[usp_datalistnugget_Contact]";
        public const string MakeDefautSOContact = "[dbo].[usp_update_Company_DefaultSOContact]";
        public const string MakeDefaultPOContact = "[dbo].[usp_update_Company_DefaultPOContact]";
        public const string MakeDefaultPOLedgerContact = "[dbo].[usp_update_Company_DefaultPOLedgerContact]";
        public const string MakeDefaultSOLedgerContact = "[dbo].[usp_update_Company_DefaultSOLedgerContact]";
        public const string Update_Contact = "[dbo].[usp_update_Contact]";
        public const string Insert_Contact = "[dbo].[usp_insert_Contact]";
        public const string Datalistnugget_CommunicationLog = "[dbo].[usp_datalistnugget_CommunicationLog]";
        public const string Autosearch_Company_for_Prospects = "[dbo].[usp_autosearch_Company_for_Prospects]";
        public const string Autosearch_Contact = "[dbo].[usp_autosearch_Contact]";
        public const string Autosearch_Company_for_Customers = "[dbo].[usp_autosearch_Company_for_Customers]";
        public const string Activate_Inactivate_Customer_Group_code = "[dbo].[usp_Inactive_GroupCode_by_Id]";
        public const string Insert_GroupCode_ContactGroup = "[dbo].[usp_GroupCodeContactGroup_Insert]";
        public const string Edit_GroupCode_ContactGroup = "[dbo].[usp_GroupCodeContactGroup_Edit]";
        #endregion

        public const string SelectAllCompanyItemSearch = "[dbo].[usp_itemsearch_Company]";
        public const string Get_Company_SalesInfo = "[dbo].[usp_select_Company_SalesInfo]";

        #region Sourcing
        public const string Select_Source_Stock = "[dbo].[usp_source_Stock]";
        public const string Select_Multiple_Source_Stock = "[dbo].[usp_source_Stock_MultipleParts]";
        public const string Select_Source_StockInfo = "[dbo].[usp_source_StockInfo]";
        public const string Select_Source_StockInfo_MultipleParts = "[dbo].[usp_source_StockInfo_MultipleParts]";
        public const string Select_StockInfo = "[dbo].[usp_select_StockInfo]";
        public const string Select_Source_AltParts_MultipleParts = "[dbo].[usp_source_AltParts_MultipleParts]";
        public const string Select_Source_AltPartsPH_MultipleParts = "[dbo].[usp_source_AltPartsPH_MultipleParts]";
        public const string Select_AltPart = "[dbo].[usp_select_AltPart]";
        public const string Update_AltPart = "[dbo].[usp_update_AltPart]";
        public const string Update_AltPartStatus = "[dbo].[usp_update_AltPartStatus]";
        public const string Auto_Search_Part = "[dbo].[usp_autosearch_Part]";
        public const string Add_Sourcing_Info = "[dbo].[usp_insert_StockInfo]";
        public const string Edit_Sourcing_Info = "[dbo].[usp_update_StockInfo]";
        public const string Select_Excess = "[dbo].[usp_select_Excess]";
        public const string Select_Excess_with_Archived = "[dbo].[usp_select_Excess_with_Archived]";
        public const string Insert_Excess = "[dbo].[usp_insert_ExcessNew]";
        public const string Insert_Excess_PH = "[dbo].[usp_insert_ExcessNewPH]";
        public const string Ipobom_Insert_Excess = "[dbo].[usp_ipobom_insert_Excess]";
        public const string Update_Excess = "[dbo].[usp_update_Excess]";
        public const string Update_Excess_PH = "[dbo].[usp_update_ExcessPH]";
        public const string Update_Excess_OfferStatus = "[dbo].[usp_update_Excess_OfferStatus]";
        public const string Ipobom_Update_Excess = "[dbo].[usp_ipobom_update_Excess]";
        public const string Ipobom_Update_Excess_with_Archived = "[dbo].[usp_ipobom_update_Excess_with_Archived]";
        public const string Select_Sourcing_Excess_MultipleParts = "[dbo].[usp_source_Excess_MultipleParts]";
        public const string Select_Sourcing_ExcessPH_MultipleParts = "[dbo].[usp_source_ExcessPH_MultipleParts]";
        public const string Insert_ExcessClone = "[dbo].[usp_insert_ExcessClone]";
        public const string Offer_Clone_AddToRequirement = "[dbo].[usp_offer_clone_AddToRequirement]";

        public const string Get_Supplier_API_Data = "[dbo].[Usp_GetSupplierAPIData]";
        public const string Get_DigiKey_API_Data = "[dbo].[Usp_GetDigiKeyAPIData]";
        public const string Get_Supplier_API_Live = "[dbo].[usp_HandleMultileApiJson]";

        public const string Insert_SourcingResult_From_AltPart = "[dbo].[usp_insert_SourcingResult_From_AltPart]";
        public const string Insert_SourcingResult_From_AltPartPH = "[dbo].[usp_insert_SourcingResult_From_AltPartPH]";

        public const string Insert_SourcingResult_From_Trusted = "[dbo].[usp_insert_SourcingResult_From_Trusted]";
        public const string Insert_SourcingResult_From_TrustedPH = "[dbo].[usp_insert_SourcingResult_From_TrustedPH]";

        public const string Insert_SourcingResult_From_Offers = "[dbo].[usp_insert_SourcingResult_From_Offer]";
        public const string Insert_SourcingResult_From_OffersPH = "[dbo].[usp_insert_SourcingResult_From_OfferPH]";

        public const string Insert_SourcingResult_From_Epo = "[dbo].[usp_insert_SourcingResult_From_Epo]";
        public const string Insert_SourcingResult_From_EpoPH = "[dbo].[usp_insert_SourcingResult_From_EpoPH]";

        public const string Insert_SourcingResult_From_ReverseLogistic = "[dbo].[usp_insert_SourcingResult_From_ReverseLogistic]";
        public const string Insert_SourcingResult_From_ReverseLogisticPH = "[dbo].[usp_insert_SourcingResult_From_ReverseLogisticPH]";

        public const string Insert_SourcingResult_From_History = "[dbo].[usp_insert_SourcingResult_From_History]";

        public const string Insert_SourcingResult_From_Stock = "[dbo].[usp_insert_SourcingResult_From_StockPH]";

        public const string Select_Sourcing_Offer_MultipleParts = "[dbo].[usp_source_Offer_MultipleParts]";
        public const string Select_Sourcing_OfferPH_MultipleParts = "[dbo].[usp_source_OfferPH_MultipleParts]";

        public const string Select_Sourcing_Epo_MultipleParts = "[dbo].[usp_ipobom_source_Epo_MultipleParts]";
        public const string Select_Sourcing_SourceReverseLogistics_MultipleParts = "[dbo].[usp_ipobom_source_ReverseLogistic_MultipleParts]";
        public const string Select_Sourcing_PreviousHubrfqOffer_MultipleParts = "[dbo].[usp_Select_SourcingResult_Epo_MultipleParts]";
        public const string Select_Sourcing_QuoteLine_MultipleParts = "[dbo].[usp_source_QuoteLine_MultipleParts]";
        public const string Select_Sourcing_PurchaseOrderLine_MultipleParts = "[dbo].[usp_source_PurchaseOrderLine_MultipleParts]";
        public const string Select_Sourcing_PurchaseOrderLineClient_MultipleParts = "[dbo].[usp_source_PurchaseOrderLineClient_MultipleParts]";

        public const string Select_Offer = "[dbo].[usp_select_Offer]";
        public const string Select_Offer_with_Archived = "[dbo].[usp_select_Offer_with_Archived]";
        public const string Ipobom_Update_Offer = "[dbo].[usp_ipobom_update_Offer]";
        public const string Ipobom_Update_Offer_with_Archived = "[dbo].[usp_ipobom_update_Offer_with_Archived]";
        public const string Insert_IPOOffer = "[dbo].[usp_insert_IPOOffer]";
        public const string Update_Offer_OfferStatus = "[dbo].[usp_update_Offer_OfferStatus]";
        public const string Select_StrategicOffer = "[dbo].[usp_select_EditEpo]";
        public const string Edit_StrategicOffer = "[dbo].[usp_ipobom_update_Epo]";
        public const string Edit_BulkStrategicOffer = "[dbo].[usp_update_POHub_StrategicLogisticBulk]";
        public const string Update_StrategicOfferStatus = "[dbo].[usp_update_Strategic_StrategicStatus]";
        public const string Edit_BulkReverseLogistic = "[dbo].[usp_ipobom_update_ReverseLogisticBulk]";

        public const string Get_BulkEditLog_Epo = "[dbo].[usp_get_BulkEditLog_Epo]";
        public const string Get_BulkEditLog_RL = "[dbo].[usp_get_BulkEditLog_RL]";

        public const string Select_EditReverseLogistic = "[dbo].[usp_select_EditReverseLogistic]";
        public const string Ipobom_Update_ReverseLogistic = "[dbo].[usp_ipobom_update_ReverseLogistic]";
        public const string Update_ReverseLogisticsStatus = "[dbo].[usp_update_ReverseLT_ReverseLTStatus]";
        public const string SelectAll_SourcingResultQuotes = "[dbo].[usp_selectAll_Quote_for_SourcingResult]";
        public const string Select_Sourcing_CustomerRequirements_MultipleParts = "[dbo].[usp_source_CustomerRequirement_MultipleParts]";

        public const string Select_Source_History_MultipleParts = "[dbo].[usp_source_History_MultipleParts]";
        public const string Select_Source_HistoryPH_MultipleParts = "[dbo].[usp_source_HistoryPH_MultipleParts]";
        public const string Select_History = "[dbo].[usp_select_History]";
        public const string Select_History_with_Archived = "[dbo].[usp_select_History_with_Archived]";
        public const string Update_History_OfferStatus = "[dbo].[usp_update_History_OfferStatus]";

        public const string Select_source_PurchaseRequestLineDetails_MultipleParts = "[dbo].[usp_source_PurchaseRequestLineDetails_MultipleParts]";

        public const string Select_Source_SalesOrderLine_Multiple_Parts = "[dbo].[usp_source_SalesOrderLine_Multiple_Parts]";
        public const string Update_Sourcing_Release = "[dbo].[usp_update_Sourcing_Release]";
        public const string SourcingResult_for_CustomerRequirement = "[dbo].[usp_SourcingResult_for_CustomerRequirement]";
        

        #endregion

        #region auto sourcing

        public const string Get_Auto_Sourcing_HUBRFQ_Item = "[dbo].[usp_get_POHub_AutoSource_V2]";
        public const string Insert_From_StockPH = "[dbo].[usp_insert_SourcingResult_From_StockPH]";
        public const string Insert_From_EpoPH = "[dbo].[usp_insert_SourcingResult_From_EpoPH]";
        public const string Insert_From_ReverseLogisticPH = "[dbo].[usp_insert_SourcingResult_From_ReverseLogisticPH]";
        public const string Insert_From_Offer = "[dbo].[usp_insert_SourcingResult_From_Offer]";
        public const string Insert_From_OfferPH = "[dbo].[usp_insert_SourcingResult_From_OfferPH]";
        public const string Delete_Sourcing_Result = "[dbo].[usp_delete_SourcingResult]";

        #endregion

        #region Check supplier manufacturer
        public const string Get_Supplier_Type = "[dbo].[USP_GETSUPPLIERTYPE]";
        public const string Get_Company_Industry_Type = "[dbo].[usp_all_CompanyIndustryType]";
        public const string Get_Supplier_Type_By_MfrGroup = "[dbo].[usp_GetSupplierType_By_MfrGroup]";
        public const string Get_Sourcing_Data_Search = "[dbo].[USP_GetSourcingData]";
        #endregion

        #region Manufacturers
        public const string SelectAll_ManufacturerLink_for_Manufacturer = "[dbo].[usp_selectAll_ManufacturerLink_for_Manufacturer]";
        public const string Datalistnugget_Manufacturer = "[dbo].[usp_datalistnugget_Manufacturer]";
        public const string Insert_Manufacturer = "[dbo].[usp_insert_ManufacturerLink]";
        public const string AutoSearch_Company_for_Suppliers = "[dbo].[usp_autosearch_Company_for_Suppliers]";

        public const string Select_Manufacturer = "[dbo].[usp_select_Manufacturer]";
        public const string Update_Company_ManufacturerNo = "[dbo].[usp_update_Company_ManufacturerNo]";
        public const string Update_ManufacturerGroupByManufacturer = "[dbo].[usp_Update_ManufacturerGroupByManufacturer]";
        public const string Add_Manufacturer = "[dbo].[usp_insert_Manufacturer]";
        public const string Check_Manufacturer = "[dbo].[usp_check_Manufacturer]";
        public const string Insert_ContactGroup = "[dbo].[usp_ContactGroup_Insert]";

        public const string Insert_ManufacturerPDF = "[dbo].[usp_insert_ManufacturerPDF]";
        public const string Delete_ManufacturerPdf = "[dbo].[usp_delete_ManufacturerPDF]";
        public const string Insert_ManufacturerExcel = "[dbo].[usp_insert_ManufacturerExcel]";
        public const string Delete_ManufacturerExcel = "[dbo].[usp_delete_ManufacturerExcel]";
        public const string SelectAll_ManufacturerLink_For_Supplier = "[dbo].[usp_selectAll_ManufacturerLink_for_Supplier]";
        #endregion
        #region Sales Orders
        public const string Select_SalesOrder_for_TodoTask = "[dbo].[usp_select_SalesOrder_for_TodoTask]";
        public const string Select_SalesOrder_OpenLineSummaryValues = "[dbo].[usp_select_SalesOrder_OpenLineSummaryValues]";
        public const string Get_InvoiceNotExported = "[dbo].[usp_Get_InvoiceNotExported]";
        public const string Select_SalesOrder_DetailsForLineCalculations = "[dbo].[usp_select_SalesOrder_DetailsForLineCalculations]";
        public const string Select_Export_Approval_SalesOrderLine = "[dbo].[usp_selectExportApprovalData]";
        public const string Datalistnugget_SalesOrderLine = "[dbo].[usp_datalistnugget_SalesOrderLine]";
        public const string Select_Company_DefaultSalesInfo = "[dbo].[usp_select_Company_DefaultSalesInfo]";
        public const string Select_CurrencyRate_Current_at_Date = "[dbo].[usp_select_CurrencyRate_Current_at_Date]";
        public const string Select_Address_DefaultShipping_for_Company = "[dbo].[usp_select_Address_DefaultShipping_for_Company]";
        public const string Insert_SalesOrder = "[dbo].[usp_insert_SalesOrder]";
        public const string Insert_SalesOrderLine_from_QuoteLine = "[dbo].[usp_insert_SalesOrderLine_from_QuoteLine]";
        public const string SelectAll_QuoteLine_Open_for_Quote = "[dbo].[usp_selectAll_QuoteLine_Open_for_Quote]";
        public const string Select_SalesOrder = "[dbo].[usp_select_SalesOrder]";
        public const string Get_Export_Approval_EditData = "[dbo].[usp_GetExportApprovalEditDataId]";
        public const string Update_Or_Insert_ExportApprovalDetails = "[dbo].[usp_ExportApproveDetailsInsert]";
        public const string Update_Or_Insert_AllExportApprovalDetails = "[dbo].[usp_ExportApproveDetailsInsertAll]";
        public const string Get_SOsurchargeValue = "[dbo].[usp_get_SOsurchargeValue]";
        public const string Select_SalesOrder_for_Page = "[dbo].[usp_select_SalesOrder_for_Page]";
        public const string Get_Export_ApprovalDataByID = "[dbo].[usp_GetExportApprovalDataById]";
        public const string Approve_Or_Reject_ExportApproval = "[dbo].[usp_ExportApproveRejectById]";
        public const string Insert_SalesOrderPDF = "[dbo].[usp_insert_SalesOrderPDF]";
        public const string Delete_SalesOrderPDF = "[dbo].[usp_delete_SalesOrderPDF]";
        public const string Save_SO_PaymentInfo = "[dbo].[usp_Save_SO_PaymentInfo]";
        public const string Insert_SalesOrderExcel = "[dbo].[usp_insert_SalesOrderExcel]";
        public const string Delete_SalesOrderExcel = "[dbo].[usp_delete_SalesOrderExcel]";
        public const string Update_SalesOrder = "[dbo].[usp_update_SalesOrder]";
        public const string Select_SOAddVancePaymentnotification = "[dbo].[usp_select_SOAddVancePaymentnotification]";
        public const string Get_OGEL_Select_SONotifier = "[dbo].[usp_OGEL_select_SONotifyer]";
        public const string Get_OGEL_ECCN_GroupMember_Email = "[dbo].[usp_GetLoginIdsEmail]";
        public const string Get_PowerApp_TokenInfo = "[dbo].[usp_PowerAppGenerateToken]";
        public const string Close_SalesOrder = "[dbo].[usp_update_SalesOrder_Close]";
        public const string Confirm_SalesOrder_Sent = "[dbo].[usp_SentOrder]";
        public const string Itemsearch_SalesOrderLine = "[dbo].[usp_itemsearch_SalesOrderLine]";
        public const string Insert_SOLineEUUPDF = "[dbo].[usp_insert_SOLineEUUPDF]";
        public const string Delete_EUUSOLinePDF = "[dbo].[usp_delete_EUUSOLinePDF]";
        #endregion

        #region SOLine
        public const string Select_SO_for_MappedPartWithECCNCode = "[dbo].[usp_SO_for_MappedPartWithECCNCode]";
        public const string SelectAll_SalesOrderLine_for_SalesOrder = "[dbo].[usp_selectAll_SalesOrderLine_for_SalesOrder]";
        public const string SelectAll_SalesOrderLine_open_for_SalesOrder = "[dbo].[usp_selectAll_SalesOrderLine_open_for_SalesOrder]";
        public const string SelectAll_SalesOrderLine_closed_for_SalesOrder = "[dbo].[usp_selectAll_SalesOrderLine_closed_for_SalesOrder]";
        public const string SelectAll_SalesOrderLine_for_EUUSalesOrderOGEL = "[dbo].[usp_selectAll_SalesOrderLine_for_EUUSalesOrderOGEL]";
        public const string SelectAll_PDF_for_EUUSOLine = "[dbo].[usp_selectAll_PDF_for_EUUSOLine]";
        public const string Get_SoLine_PromiseLog = "[dbo].[usp_Get_SOLine_PromiseLog]";
        public const string Close_SalesOrderLine = "[dbo].[usp_update_SalesOrderLine_Close]";
        public const string Confirm_SalesOrderLine = "[dbo].[usp_update_SalesOrderLineConfirmation]";
        public const string Post_or_Unpost_SalesOrderLine = "[dbo].[usp_update_SalesOrderLine_Post_or_Unpost]";
        public const string Delete_SalesOrderLine = "[dbo].[usp_delete_SalesOrderLine]";
        public const string Update_SalesOrderLine = "[dbo].[usp_update_SalesOrderLine]";

        #endregion

        #region Purchase Orders
        public const string Select_PurchaseOrder = "[dbo].[usp_select_PurchaseOrder]";
        #endregion


        #region POLine
        public const string Get_PoLine_WarningMessage = "[dbo].[usp_SanctionedPOWarningMessage_Country_for_Client]";
        #endregion
        #region All Companies
        public const string Get_DataListNugget_Company = "[dbo].[usp_datalistnugget_Company]";
        public const string Get_DataListNugget_Company_As_Customers = "[dbo].[usp_datalistnugget_Company_as_Customers]";
        public const string Get_DataListNugget_Company_As_Suppliers = "[dbo].[usp_datalistnugget_Company_as_Suppliers]";
        public const string Get_DataListNugget_Company_As_Prospects = "[dbo].[usp_datalistnugget_Company_as_Prospects]";
        public const string Insert_Company = "[dbo].[usp_insert_Company]";
        #endregion

        #region Company Details
        public const string Select_Address_DefaultBilling_for_Company = "[dbo].[usp_select_Address_DefaultBilling_for_Company]";
        public const string Select_Company_MainInfo = "[dbo].[usp_select_Company_MainInfo]";
        public const string Insert_CompanyPDF = "[dbo].[usp_insert_CompanyPDF]";
        public const string Delete_CompanyPDF = "[dbo].[usp_delete_CompanyPDF]";
        public const string Select_All_Address_for_Company = "[dbo].[usp_selectAll_Address_for_Company]";
        public const string Select_Address_for_Company = "[dbo].[usp_select_Address_for_Company]";
        public const string Insert_CompanyAddress = "[dbo].[usp_insert_CompanyAddress]";
        public const string Update_CompanyAddress = "[dbo].[usp_update_CompanyAddress]";
        public const string Update_Company_MainInfo_Active = "[dbo].[usp_Update_Company_MainInfo_Active]";
        public const string Update_CompanyAddress_CeaseDate = "[dbo].[usp_update_CompanyAddress_CeaseDate]";
        public const string Get_Linked_Companies = "[dbo].[usp_select_LinkedCompanies]";
        public const string Get_Linked_Accounts_Combined_Info = "[dbo].[usp_GetLinkedAccountsCombinedInfo]";
        public const string Get_CertificateByCompany = "[dbo].[usp_get_CertificateByCompany]";
        public const string Insert_CompanyCIPPDF = "[dbo].[usp_insert_CompanyCIPPDF]";
        public const string Delete_CompanyInsurancePDF = "[dbo].[usp_delete_CompanyInsurancePDF]";
        public const string Select_All_SalesOrder_Open_for_Company = "[dbo].[usp_selectAll_SalesOrder_open_for_Company]";
        public const string Select_SalesOrderSumaryValues = "[dbo].[usp_select_SalesOrder_SummaryValues]";
        public const string Select_All_SalesOrder_Overdue_for_Company = "[dbo].[usp_selectAll_SalesOrder_overdue_for_Company]";
        public const string Get_Summarise_ThisYear_SalesOrderValue = "[dbo].[usp_summarise_Company_ThisYear_SalesOrderValue]";
        public const string Get_Summarise_LastYear_SalesOrderValue = "[dbo].[usp_summarise_Company_LastYear_SalesOrderValue]";
        public const string Get_Invoice_Not_Exported = "[dbo].[usp_Get_InvoiceNotExported]";
        public const string Select_SalesOrder_OpenLineSumaryValues = "[dbo].[usp_select_SalesOrder_OpenLineSummaryValues]";
        public const string Select_Currency_ConvertedBetweenTwoCurrencies = "[dbo].[usp_select_Currency_ConvertedValueBetweenTwoCurrencies]";
        public const string Select_Summarise_ThisYear_LastYear_Value = "[dbo].[usp_summarise_Company_ThisYear_LastYear_SalesValue]";
        public const string Update_Company_MainInfo = "[dbo].[usp_update_Company_MainInfo]";
        public const string Select_Company_PurchaseInfo = "[dbo].[usp_select_Company_PurchaseInfo]";
        public const string Summarise_Company_ThisYear_PurchaseOrderValue = "[dbo].[usp_summarise_Company_ThisYear_PurchaseOrderValue]";
        public const string Summarise_Company_LastYear_PurchaseOrderValue = "[dbo].[usp_summarise_Company_LastYear_PurchaseOrderValue]";
        public const string SelectAll_PurchaseOrder_Open_For_Company = "[dbo].[usp_selectAll_PurchaseOrder_open_for_Company]";
        public const string SelectAll_PurchaseOrder_Overdue_For_Company = "[dbo].[usp_selectAll_PurchaseOrder_overdue_for_Company]";
        public const string Update_Company_SalesInfo = "[dbo].[usp_update_Company_SalesInfo]";
        public const string Update_Company_PurchaseInfo = "[dbo].[usp_update_Company_PurchaseInfo]";
        public const string IsIPOAlreadyCreated = "[dbo].[usp_IsIPOAlreadyCreated]";
        public const string Get_Supplier_Status_Message = "[dbo].[usp_GetSupplierMessage]";

        #endregion

        #region CreditLimit
        public const string GetCreditLimitLogs = "[dbo].[usp_GetCreditLimitLogs]";
        public const string Insert_CreditLimitApplication = "[dbo].[usp_insert_CrediLimitApplication]";
        public const string Approve_Reject_CreditLimitApplicaiton = "[dbo].[usp_Approve_Reject_CreditLimitApplicaiton]";
        public const string Update_CreditLimitApplication = "[dbo].[usp_UpdateCreditLimitApplication]";
        #endregion

        #region Company Prospects
        public const string Get_CRMProspects_for_Company = "[dbo].[usp_get_CRMProspects_for_Company]";
        public const string Update_CRMProspects_for_Company = "[dbo].[usp_update_CRMProspects_for_Company]";
        public const string Get_Dropdown_CRMProspects_for_Company = "[dbo].[usp_dropdown_CRMProspects_for_Company]";
        #endregion

        #region Global Sales Access
        public const string GetGlobalSalesPerson = "[dbo].[usp_GSA_GetGlobalSalesPerson]";

        #endregion
        #region Currency
        public const string Get_Converted_Value_Between_Two_Currencies = "[dbo].[usp_select_Currency_ConvertedValueBetweenTwoCurrencies]";
        #endregion
        #region Finance
        public const string Summarise_Company_LastYear_SalesOrderValue = "[dbo].[usp_summarise_Company_LastYear_SalesOrderValue]";
        public const string Summarise_Company_ThisYear_SalesOrderValue = "[dbo].[usp_summarise_Company_ThisYear_SalesOrderValue]";
        public const string Summarise_Company_ThisYear_LastYear_SalesValue = "[dbo].[usp_summarise_Company_ThisYear_LastYear_SalesValue]";
        public const string Update_LinkedCompanies = "[dbo].[usp_Update_LinkedCompanies]";
        #endregion

        #region Quote
        public const string Get_Quote_Id_By_Number = "[dbo].[usp_select_Quote_ID_from_Number]";
        public const string Select_QuoteLine = "[dbo].[usp_select_QuoteLine]";
        #endregion

        #region Product
        public const string GetProductMessage = "[dbo].[usp_GetProductMessage]";
        #endregion

        #region Counts
        public const string Get_Count_CustomerRequirement_for_Company = "[dbo].[usp_count_CustomerRequirement_for_Company]";
        public const string Get_Count_BOMManager_for_Company = "[dbo].[usp_count_BOMManager_for_Company]";
        public const string Get_Count_Quote_for_Company = "[dbo].[usp_count_Quote_for_Company]";
        public const string Get_Count_SalesOrder_for_Company = "[dbo].[usp_count_SalesOrder_for_Company]";
        public const string Get_Count_Invoice_for_Company = "[dbo].[usp_count_Invoice_for_Company]";
        public const string Get_Count_PurchaseOrder_for_Company = "[dbo].[usp_count_PurchaseOrder_for_Company]";
        public const string Get_Count_SupplierRMA_for_Company = "[dbo].[usp_count_SupplierRMA_for_Company]";
        public const string Get_Count_CustomerRMA_for_Company = "[dbo].[usp_count_CustomerRMA_for_Company]";
        public const string Get_Count_Credit_for_Company = "[dbo].[usp_count_Credit_for_Company]";
        public const string Get_Count_Debit_for_Company = "[dbo].[usp_count_Debit_for_Company]";
        #endregion
        #region Transactions
        public const string Get_All_BOMManager_for_Company = "[dbo].[usp_selectAll_BOMManager_for_Company]";
        #endregion
        #region Purchase Requisitions
        public const string Get_Purchase_Requisition = "[dbo].[usp_datalistnugget_SalesOrderLine_as_PurchaseRequisition]";
        public const string Get_SalesOrderLine = "[dbo].[usp_select_SalesOrderLine]";
        public const string Get_SalesOrderLine_as_PurchaseRequisition_for_Page = "[dbo].[usp_select_SalesOrderLine_as_PurchaseRequisition_for_Page]";
        #endregion

        #region Warehouse
        public const string IsSoLineExistingSourcingResult = "[dbo].[usp_issolineexistinsourcingresult]";
        
        #endregion
        #region Authorisation

        public const string Get_SalesOrder_ValueSummary = "[dbo].[usp_select_SalesOrder_ValueSummary]";
        public const string Get_All_Audit_Authorisation_For_Sales_Order = "[dbo].[usp_selectAll_Audit_authorisation_for_SalesOrder]";
        public const string Allow_Ready_To_Ship = "[dbo].[usp_SalesOrder_AllowReadyToShip]";
        #endregion
        #region AS6081
        public const string AS6081_SelectAlertMessageByOperationType = "[dbo].[usp_AS6081_SelectAlertMessageByOperationType]";
        public const string AS6081_GetApproverAssignmentLog = "[dbo].[usp_AS6081_GetApproverAssignmentLog]";
        #endregion
        #region RequestApproval
        public const string AutoSearch_Login_For_PowerAppApprover = "[dbo].[usp_select_Login_For_PowerAppApprover]";
        public const string Get_SONotifyer = "[dbo].[usp_select_SONotifyer]";
        public const string Get_SOR_Detail_For_PowerApp = "[dbo].[usp_GET_SOR_Details_For_PowerApp]";
        #endregion

        #region Stock
        public const string Itemsearch_Stock = "[dbo].[usp_itemsearch_Stock]";
        public const string Select_Stock = "[dbo].[usp_select_Stock]";
        
        #endregion

        #region Service
        public const string Itemsearch_Service = "[dbo].[usp_itemsearch_Service]";
        public const string Select_Service = "[dbo].[usp_select_Service]";
        
        #endregion

    }
}
