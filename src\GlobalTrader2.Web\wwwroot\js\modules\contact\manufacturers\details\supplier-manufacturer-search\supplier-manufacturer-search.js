﻿import '../../../../../widgets/dual-listbox.js?v=#{BuildVersion}#';
import { PageSearchSelectComponent } from '../../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#';
import { ButtonHelper } from '../../../../../helper/button-helper.js?v=#{BuildVersion}#';

const $form = $('#manufacturer-supplier-sourcing');

let $manufacturerSupplierDualListbox = $('#manufacturer-supplier-dual-listbox');
let $manufacturerIndustryTypeDualListbox = $('#manufacturer-industry-type-dual-listbox');
let manufacturerSearchSelect, manufacturerGroupSearchSelect;
let $productTypeSelection = $('#ProductTypeSelection');
let $globalCategorySelection = $('#GlobalCategorySelection');
let $productSelection = $('#ProductSelection');
const apiEndpoints = {
    supplierType: "/orders/check-supplier-manufacturer/supplier-type/",
    companyIndustry: "/orders/check-supplier-manufacturer/company-industry",
    manufacturerGroup: "/orders/check-supplier-manufacturer/manufacturer-groups",
    manufacturerGroupByMfr: "/api/orders/check-supplier-manufacturer/manufacturer-group/",
    supplierTypeByMfrGroup: "/api/orders/check-supplier-manufacturer/supplier-type-by-mfr/",
    productCategory: "/api/orders/check-supplier-manufacturer/product-category",
    globalCategory: "/api/orders/check-supplier-manufacturer/global-category/",
    product: "/api/orders/check-supplier-manufacturer/product/",
    search: "/api/orders/check-supplier-manufacturer/search",
}
window.supplierList = undefined;
let isChangeFromMfr;

$(() => {
    initDualListBox();
    initSearchSelect();
    setupEventListener();
    getProductCategory();

    $('#ManufacturerSearch').on('focus', function () {
        $('#manufacturerSearchCheckbox').prop('checked', true);
        handleEnableSearchButton();
    });

    $('#ManufacturerGroupSearch').on('focus', function () {
        $('#manufacturerGroupSearchCheckbox').prop('checked', true);
        handleEnableSearchButton();
    });

    $('#manufacturerSearchCheckbox').on('change', function () {
        $('#manufacturerSearchCheckbox').val($(this).is(':checked'));
        handleEnableSearchButton();
    });

    $('#manufacturerGroupSearchCheckbox').on('change', function () {
        $('#manufacturerGroupSearchCheckbox').val($(this).is(':checked'));
        handleEnableSearchButton();
    });

})


async function initDualListBox() {
    let MfrId = 0;
    $manufacturerSupplierDualListbox.dual_listbox({
        isLoadDataOnCreate: true,
        loadSelected: async () => {
            return [];
        },

        loadUnselected: async (element) => {
            if (window.supplierList != undefined) {
                return window.supplierList?.map(supplier => ({
                    label: supplier.name,
                    value: supplier.id
                })) ?? [];
            }
            else {
                const suppliers = await GlobalTrader.ApiClient.getAsync(`${apiEndpoints.supplierType}${MfrId}`, {}, {});
                return suppliers?.data?.map(supplier => ({
                    label: supplier.name,
                    value: supplier.id
                })) ?? [];
            }
        },

        processLoadedData: function (selected, unSelected) {
            return { selected, unSelected }
        },

    });

    $manufacturerIndustryTypeDualListbox.dual_listbox({
        isLoadDataOnCreate: false,
        loadSelected: async () => {
            return [];
        },
        loadUnselected: async () => {
            const industries = await GlobalTrader.ApiClient.getAsync(`${apiEndpoints.companyIndustry}`, {}, {});
            return industries?.data?.map(industry => ({
                label: industry.name,
                value: industry.id
            })) ?? [];
        },

        processLoadedData: function (selected, unSelected) {
            return { selected, unSelected }
        },

    });
    await $manufacturerSupplierDualListbox.dual_listbox("loadDataAndRenderItem");
    await $manufacturerIndustryTypeDualListbox.dual_listbox("loadDataAndRenderItem");
}

function initSearchSelect() {
    manufacturerSearchSelect = new PageSearchSelectComponent(
        'ManufacturerSearch',
        'Manufacturer',
        'single',
        'keyword',
        'auto-search/manufacturer',
        2
    );

    manufacturerGroupSearchSelect = new PageSearchSelectComponent(
        'ManufacturerGroupSearch',
        'ManufacturerGroup',
        'single',
        'keyword',
        `${apiEndpoints.manufacturerGroup}?grouptype=manufacturer`,
        2
    );

    manufacturerSearchSelect.on('change', async ({ searchValue, hiddenValue }) => {
        if (hiddenValue) {
            $('#manufacturerSearchCheckbox').prop('checked', true);
            isChangeFromMfr = true;
            await getSupplierType(hiddenValue);
            await getManufacturerGroupByMfr(hiddenValue);
        }
        else {
            manufacturerGroupSearchSelect.resetSearchSelect(false);
            $('#manufacturerSearchCheckbox').prop('checked', false);
        }
        handleEnableSearchButton();
    });

    manufacturerGroupSearchSelect.on('change', async ({ searchValue, hiddenValue }) => {
        if (hiddenValue) {
            $('#manufacturerGroupSearchCheckbox').prop('checked', true);
            if (!isChangeFromMfr) {
                await getSupplierTypeByMfrGroup(hiddenValue);
            }
            isChangeFromMfr = false;
        }
        else {
            manufacturerSearchSelect.resetSearchSelect(false);
            $('#manufacturerGroupSearchCheckbox').prop('checked', false);
        }
        handleEnableSearchButton();
    })
}

function handleEnableSearchButton() {
    if ($('#manufacturerGroupSearchCheckbox').is(':checked') || $('#manufacturerSearchCheckbox').is(':checked')) {
        $("#searchBtn").attr("disabled", false);
    }
    else {
        $("#searchBtn").attr("disabled", true);
    }
}

async function resetSearchInput() {
    manufacturerSearchSelect.resetSearchSelect(false);
    manufacturerGroupSearchSelect.resetSearchSelect(false);

    window.supplierList = undefined;
    await $manufacturerSupplierDualListbox.dual_listbox("loadDataAndRenderItem");
    await $manufacturerIndustryTypeDualListbox.dual_listbox("loadDataAndRenderItem");

    $productTypeSelection.empty();
    $productTypeSelection.append($('<option>', {
        value: 0,
        text: `--${localizedOptions.productCategory}--`
    }));

    $globalCategorySelection.empty();
    $globalCategorySelection.append($('<option>', {
        value: 0,
        text: `--${localizedOptions.globalCategory}--`
    }));

    $productSelection.empty();
    $productSelection.append($('<option>', {
        value: 0,
        text: `--${localizedOptions.product}--`
    }));

    $('#OrderDate option[value="0"]').attr("selected", "selected");
    $("#SupplierTypeAll").prop('checked', true);
}

function setupEventListener() {

    $('#resetBtn').button().on('click', async () => {
        resetSearchInput();

        const tableId = '#result-search-table';

        if ($.fn.DataTable.isDataTable(tableId)) {
            $(tableId).DataTable().clear().destroy();
            $(tableId).empty();
        }
    })

    $('#searchBtn').button().on('click', async () => {
        let formData = {};
        $form.serializeArray().forEach(function (item) {
            formData[item.name] = item.value;
        });

        let dualBoxIndustryData = await $manufacturerIndustryTypeDualListbox.dual_listbox("getData");
        let dualBoxSupplierData = await $manufacturerSupplierDualListbox.dual_listbox("getData");

        let industrySelected = dualBoxIndustryData.changedSelecteds;
        let supplierSelected = dualBoxSupplierData.changedSelecteds;

        let mappedData = {
            ProductTypeId: formData.ProductTypeSelection ?? 0,
            Franchised: formData.SupplierType ?? 0,
            GlobalProductId: formData.GlobalCategorySelection ?? 0,
            ProductId: formData.ProductSelection ?? 0,
            IndustryType: industrySelected.length > 0 ? industrySelected?.join() : '0',
            SupplierType: supplierSelected.length > 0 ? supplierSelected?.join() : '0'
        };
        if ($('#manufacturerSearchCheckbox').is(':checked')) {
            mappedData.MfrId = formData.Manufacturer && formData.Manufacturer !== '' ? formData.Manufacturer : 0;
        }

        if ($('#manufacturerGroupSearchCheckbox').is(':checked')) {
            mappedData.MfrGroupId = formData.ManufacturerGroup && formData.ManufacturerGroup !== '' ? formData.ManufacturerGroup : 0;
        }
        await setupDataTable(mappedData);
        $("#result-search-box").removeClass('d-none');
    })

    $productTypeSelection.on('change', () => {
        getGlobalCategory($productTypeSelection.val());
    })

    $globalCategorySelection.on('change', () => {
        getProduct($globalCategorySelection.val());
    })
}

async function getSupplierType(mfrId) {
    if (mfrId == null || mfrId == undefined || mfrId == '') {
        mfrId = 0;
    }

    $.ajax({
        url: `/api${apiEndpoints.supplierType}${mfrId}`,
        type: 'GET',
        contentType: 'application/json',
        success: function (result) {
            window.supplierList = result.data;
            setTimeout(() => {
                $manufacturerSupplierDualListbox.dual_listbox("loadDataAndRenderItem");
            }, 300);
            if (result.data.length == 0) {
                setTimeout(() => {
                    $manufacturerSupplierDualListbox.find('#search-unselected-notfound').show();
                }, 300);
            }
        }
    });
}

async function getManufacturerGroupByMfr(mfrId) {
    if (mfrId == null || mfrId == undefined) {
        return;
    }

    $.ajax({
        url: `${apiEndpoints.manufacturerGroupByMfr}${mfrId}`,
        type: 'GET',
        contentType: 'application/json',
        success: function (result) {
            if (result.data.length != 0)
                handleSelectManufacturerGroup(result.data[0]);
        }
    });
}
function handleSelectManufacturerGroup(name) {
    //logic V1 just set name, ignore value
    manufacturerGroupSearchSelect.selectItem({
        value: 0,
        label: name
    });
}

async function getSupplierTypeByMfrGroup(mfrGroupId) {
    if (mfrGroupId == null || mfrGroupId == undefined || mfrGroupId == '') {
        mfrGroupId = 0;
    }

    $.ajax({
        url: `${apiEndpoints.supplierTypeByMfrGroup}${mfrGroupId}`,
        type: 'GET',
        contentType: 'application/json',
        success: function (result) {
            window.supplierList = result.data;
            setTimeout(() => {
                $manufacturerSupplierDualListbox.dual_listbox("loadDataAndRenderItem");
            }, 300);
            if (result.data.length == 0) {
                setTimeout(() => {
                    $manufacturerSupplierDualListbox.find('#search-unselected-notfound').show();
                }, 300);
            }
        }
    });
}

async function getProductCategory() {
    $.ajax({
        url: `${apiEndpoints.productCategory}`,
        method: 'GET',
        success: function (response) {
            $productTypeSelection.empty();

            $productTypeSelection.append($('<option>', {
                value: 0,
                text: '--Product Category--'
            }));

            const data = response.data;
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    $productTypeSelection.append($('<option>', {
                        value: key,
                        text: data[key]
                    }));
                }
            }
        },
        error: function (err) {
            console.error('Failed to load product categories:', err);
        }
    });
}

async function getGlobalCategory(productId) {
    if (!productId) return;

    $.ajax({
        url: `${apiEndpoints.globalCategory}${productId}`,
        method: 'GET',
        success: function (response) {
            $globalCategorySelection.empty();

            $globalCategorySelection.append($('<option>', {
                value: 0,
                text: `--${localizedOptions.globalCategory}--`
            }));

            const data = response.data;
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    $globalCategorySelection.append($('<option>', {
                        value: key,
                        text: data[key]
                    }));
                }
            }
        },
        error: function (err) {
            console.error('Failed to load product categories:', err);
        }
    });
}

async function getProduct(categoryId) {
    if (!categoryId) return;

    $.ajax({
        url: `${apiEndpoints.product}${categoryId}`,
        method: 'GET',
        success: function (response) {
            $productSelection.empty();

            $productSelection.append($('<option>', {
                value: 0,
                text: `--${localizedOptions.product}--`
            }));

            const data = response.data;
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    $productSelection.append($('<option>', {
                        value: key,
                        text: data[key]
                    }));
                }
            }
        },
        error: function (err) {
            console.error('Failed to load product categories:', err);
        }
    });
}

async function setupDataTable(data) {
    const tableId = '#result-search-table';

    if ($.fn.DataTable.isDataTable(tableId)) {
        $(tableId).DataTable().clear().destroy();
        $(tableId).empty();
    }

    const _ = new DataTable(tableId, {
        colReorder: true,
        autoWidth: true,
        scrollCollapse: true,
        paging: true,
        scrollY: '400px',
        dataSrc: 'data',
        columnDefs: [
            { type: 'string', targets: '_all' },
        ],
        serverSide: false,
        lengthMenu: [5, 10, 25, 50],
        pageLength: 10,
        ajax: {
            url: `${apiEndpoints.search}`,
            type: 'POST',
            contentType: 'application/json',
            data: function (d) {
                return JSON.stringify({ ...d, ...data });
            },
        },
        info: true,
        responsive: true,
        select: {
            style: 'single',
            items: 'cell',
        },
        ordering: false,
        processing: true,
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
            infoFiltered: "",
            lengthMenu: "_MENU_ per page",
            loadingRecords: "",
        },
        dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
            '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
            '<"dt-layout-cell dt-layout-end" p >><"clear">',
        rowId: "manufacturerId",
        columns: [
            {
                ...createDataTableDefaultColumns('lastOrderDate', localizedTitles.lastOrderDate, localizedTitles.orderBy),
                width: "7%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.lastOrderDate)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.salesmanName)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('purchaseOrder', localizedTitles.purchaseOrder),
                width: "8%",
                className: "text-break align-baseline position-relative",
                render: function (_data, _type, row) {
                    return `${getUrlDisplayTextOpenInNewTab(row?.purchaseOrder, row?.purchaseOrderDetailUrl)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('supplier', localizedTitles.supplier, localizedTitles.type),
                width: "10%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(ButtonHelper.nubButton_CompanyOpenInNewTab(row.supplierId, row.supplier, null, null, null))} ${GlobalTrader.HtmlHelper.createStarRatingHtml({ starRatingValue: row.starRating })} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.type)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('manufacturerName', localizedTitles.manufacturerName),
                width: "9%",
                className: "text-break align-baseline position-relative",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(getMfrContentOpenInNewTab(row.manufacturerId, row.manufacturerName, null))}`;
                }
            },
            {
                ...createDataTableDefaultColumns('product', localizedTitles.product, localizedTitles.industryType),
                width: "12%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.product)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.industryType)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('website', localizedTitles.website, localizedTitles.email),
                width: "12%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.HtmlHelper.createHyperLinkOpenInNewTabHtml({ url: row.website, title: row.website })} <br> <a class="dt-hyper-link" href="mailto:${row.email}">${row.email}</a>`;
                }
            },
            {
                ...createDataTableDefaultColumns('last12MonthRMA', localizedTitles.last12MonthRMA),
                width: "7%",
                className: "text-break align-baseline position-relative",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.last12MonthRMA)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('orderCountInLastOneYear', localizedTitles.orderCountInLastOneYear),
                width: "7%",
                className: "text-break align-baseline position-relative",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.orderCountInLastOneYear)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('lastOrderValue', localizedTitles.lastOrderValue),
                width: "7%",
                className: "text-break align-baseline position-relative",
                render: function (_data, _type, row) {
                    return renderLastOrderValue(row.lastOrderValue, row.currencyCode);
                }
            },
        ]
    });
}

function renderLastOrderValue(lastOrderValue, currencyCode) {
    let value = GlobalTrader.StringHelper.setCleanTextValue(lastOrderValue);
    let currency = currencyCode ? " (" + GlobalTrader.StringHelper.setCleanTextValue(currencyCode) + ")" : "";
    return value + currency;
}

function createDataTableDefaultColumns(name, ...title) {
    return {
        title: renderTitle(...title),
        className: 'text-wrap text-break header-custom',
        data: name,
        name: name,
    }
}

function renderTitle(...title) {
    if (title.length < 1)
        return '';
    if (title.length == 1)
        return title[0];
    return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
}

