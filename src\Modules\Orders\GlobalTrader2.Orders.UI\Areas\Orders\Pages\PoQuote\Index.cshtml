@page
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.IndexModel
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LeftNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.SourcingSectionBox
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Services
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Models
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Http
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

@section LeftSidebar {
    @await Component.InvokeAsync(
                nameof(LeftNugget),
                new LeftNuggetProps()
        {
            IsInitiallyExpanded = true,
            Item = SideBar.Selection,
            ChildComponent = nameof(SelectionMenu),
        })
}

@{
    ViewData["Title"] = "Price Request";
}

<style>
    .filter-form {
        justify-content: end;
    }
</style>



<div id="po-quote-container" class="page-content-container">
    <div class="d-flex justify-content-between align-items-end border-bottom mt-2">
        <h2 class="page-primary-title">@_localizer["Price Request"]</h2>
        <div id="right-controls" class="d-flex flex-column gap-2">
            <div id="po-quote-nav-tabs-wrapper" role="tablist">
                <ul class="nav nav-tabs border-0 justify-content-end" id="hurfq-tab">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="my-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable" type="button"
                                role="tab" aria-controls="datatable"
                                aria-selected="true" tabId="0">
                            @_localizer["My"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="team-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button"
                                role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="1">
                            @_localizer["Team"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="division-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button" role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="2">
                            @_localizer["Division"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="company-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button" role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="3">
                            @_localizer["Company"]
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="tab-content mt-3">
        <div class="tab-pane fade" id="datatable" role="tabpanel" aria-labelledby="my-tab">
            @{
                var sectionModel = new FilterTableSectionBoxModel("poQuote", "Price Request",
                typeof(LiteDatatable), new LiteDatatableModel("poQuoteTbl"),
                null,
                null, null)
            {
                ContentClasses = contentClasses,
                HeaderClasses = headerClasses,
                SectionBoxClasses = sectionBoxClasses
            };
            }

            @await Html.PartialAsync("Partials/_FilterTableSectionBox", sectionModel)

        </div>

    </div>
</div>

@section Scripts {
        <script>
        var currentTab = "@Model.CurrentTab";
        var commonLocalize = {
            Apply: "@_commonLocalizer["Apply"]",
            Off: "@_commonLocalizer["Off"]",
            Hide: "@_commonLocalizer["Hide"]",
            Reset: "@_commonLocalizer["Reset"]",
            Cancel: "@_commonLocalizer["Cancel"]",
            Show: "@_commonLocalizer["Show"]",
        }
        var priceRequestTitleLocalize = {
            No: "@_localizer["No"]",
            PartNo: "@_localizer["Part No"]",
            BOM: "@_localizer["BOM"]",
            Quantity: "@_localizer["Quantity"]",
            CompanyName: "@_localizer["CompanyName"]",
            Buyer: "@_localizer["Buyer"]",
            Date: "@_localizer["Date"]",
        }
        </script>

    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="~/js/helper/string-helper.js" asp-append-version="true"></script>
    <script src="~/js/widgets/loading-spinner.js" asp-append-version="true"></script>
    <script src="~/js/widgets/dialog-custom-events.js" asp-append-version="true"></script>
    <script src="~/js/widgets/common-table.js" asp-append-version="true"></script>
    <script src="~/js/helper/datetime-helper.js" asp-append-version="true"></script>
    <script src="~/js/helper/dropdown-helper.js" asp-append-version="true"></script>
    <script src="~/js/helper/form-helper.js" asp-append-version="true"></script>
    <script src="~/js/widgets/drop-down.js" asp-append-version="true"></script>
    <script src="~/js/widgets/resize-data-table-extensions.js" asp-append-version="true"></script>
    <script src="~/js/helper/page-url-function-helper.js" asp-append-version="true"></script>
    <script src="~/js/directives/custom-input-directive.js" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/purchase-quote/purchase-quote.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
		<script type="module" src="/dist/js/orders-purchase-quote.bundle.js" asp-append-version="true"></script>
    </environment>
    <script src="~/js/widgets/custom-date-picker.js" asp-append-version="true"></script>
}