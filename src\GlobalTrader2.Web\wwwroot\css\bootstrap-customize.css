﻿/* Custom color for checkbox */
.form-control {
    border-color: var(--checkbox-border);
}
    .form-control:focus {
        border-color: var(--checkbox-border);
        box-shadow: 0 0 0 0.2rem var(--input-shadow) !important; /* Optional: focus shadow */
    }

.form-check {
    display: flex;
    align-items: center;
    margin: 0;
    gap: 2px;
}
.form-check-input {
    margin: 0;
    cursor: pointer;
    border-color: var(--checkbox-border);
    width: var(--form-control-height) !important;
    height: var(--form-control-height) !important;
    background: var(--input-background);
}
    .form-check-input.check-md {
        width: var(--form-control-height-md) !important;
        height: var(--form-control-height-md) !important;
    }
    .form-check-input.check-sm {
        width: 14px !important;
        height: 14px !important;
    }

    .form-check-input:checked {
        background-color: var(--primary); /* Change this to your preferred color */
        border-color: var(--checkbox-border); /* Border color */
    }

        .form-check-input:checked:focus {
            border-color: var(--checkbox-border);
            box-shadow: 0 0 0 0.2rem var(--input-shadow); /* Optional: focus shadow */
        }

    .form-check-input:focus:not(:checked) {
        border-color: var(--checkbox-border);
        box-shadow: none;
    }
.form-check-label {
    font-size: 12px;
}

.form-input {
    height: var(--form-control-height);
    border: 1px solid var(--border);
    background: var(--input-background);
    border-radius: 0px;
}
    .form-input.input-md {
        height: var(--form-control-height-md) !important;
    }

    .form-input:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem var(--input-shadow) !important;
    }

.form-textarea {
    border: 1px solid var(--border);
    background: var(--input-background);
    border-radius: 0px;
}

    .form-textarea:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem var(--input-shadow) !important;
    }

.form-select {
    height: var(--form-control-height); /* Adjust this value for the desired height */
    padding: 2px 2rem 2px 12px;
    font-size: 12px !important;
    border-radius: 0px;
    border: 1px solid var(--border);
    background: var(--input-background);
    background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="12" height="7" viewBox="0 0 12 7"%3E%3Cpath fill="%23212529" d="M6 7 0 0h12z"/%3E%3C/svg%3E'); /* Caret icon */
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 10px 6px;
}
    .form-select.select-md {
        height: var(--form-control-height-md) !important;
        padding: 0px 2rem 0px 12px !important;
    }
    /* Custom border color for focused select */
    .form-select:focus {
        border-color: var(--primary) !important; /* Change this to your preferred color */
        box-shadow: 0 0 0 0.2rem var(--input-shadow) !important; /* Optional: focus shadow */
    }


.form-control {
    font-size: 12px !important;
}

.border-primary {
    border-color: var(--checkbox-border) !important;
}

.form-label {
    font-size: 12px;
    color: black;
}

.lh-default {
    line-height: 14.48px;
}

.invalid-feedback {
    font-size: 12px
}
.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus, .form-select.is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, .25) !important;
}

.toast-success {
    color: var(--primary);
    border: 1px solid var(--primary) !important;
}
    .toast-success img {
        filter: invert(65%) sepia(59%) saturate(5181%) hue-rotate(59deg) brightness(88%) contrast(101%);
    }

.toast-danger {
    color: rgba(205, 92, 10, 1);
    border: 1px solid rgba(205, 92, 10, 1) !important;
}
    .toast-danger img {
        filter: invert(44%) sepia(59%) saturate(4883%) hue-rotate(11deg) brightness(92%) contrast(92%);
    }

.toast-information {
    color: rgba(26, 188, 254, 1);
    border: 1px solid rgba(26, 188, 254, 1) !important;
}
    .toast-information img {
        filter: invert(61%) sepia(43%) saturate(2921%) hue-rotate(165deg) brightness(101%) contrast(99%);
    }

.toast-body {
    display: flex;
    align-items: center;
    gap: 5px;
}
    .toast-body img {
        height: 15px;
    }

    .toast-body a {
        text-decoration: none;
    }

    .toast-body a:hover {
        text-decoration: underline;
    }

.bg-toast {
    --bs-bg-opacity: 1;
    background-color: rgba(255, 237, 173, 1) !important;
}

.text-primary {
    color: var(--checkbox-border) !important;
}

.form-check-input:disabled ~ .form-check-label, .form-check-input[disabled] ~ .form-check-label {
    opacity: 1;
}

.nav-tabs .nav-link.active {
    color: var(--primary);
}

.nav-link, .nav-link:hover {
    color: #495057;
}

.text-preserve {
    white-space-collapse: preserve;
}

.bg-disable {
    background-color: #e6e9ec !important;
}

.text-primary-bold {
    color: var(--primary-bold) !important;
}

.bg-yellow {
    background-color: yellow !important;
}

.bg-info-hover:hover {
    background: var(--info-hover) !important;
}

.bg-red {
    background-color: red !important;
}

.bg-gray {
    background-color: #808080 !important;
}

.text-red {
    color: red !important;
}

.text-gray {
    color: #808080 !important;
}

.progress-bar {
    background-color: var(--primary) !important;
}