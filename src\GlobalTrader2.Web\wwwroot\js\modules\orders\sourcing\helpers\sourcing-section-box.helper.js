const sourcingBox = $("#sourcing-box");
const sourcingDataTableOptions = {
    dataSrc: "data",
    info: false,
    responsive: true,
    select: {
        toggleable: true,
        info: false,
    },
    scrollY: 320,
    scroller: true,
    deferRender: true,
    paging: true,
    ordering: false,
    searching: false,
    scrollCollapse: true,
    autoWidth: false,
    columnDefs: [
        { type: 'string', targets: '_all' }
    ],
};
const invalidClassName = "is-invalid";

$(() => {
    //register this event to use across souring section tables 
    window.onSourcingTableRowClickEvent = onSourcingTableRowClickEvent;
});

GlobalTrader.SourcingHelper = (function () {
    return {
        setupSourcingBox: function () {
            sourcingBox.section_box({
                loading: false,
                onRefreshClick: async (event, ui) => { }
            });

            sourcingBox.find(".section-box-refesh-button").remove();

            // Check for 'pn' parameter in URL and append to .section-box-title
            const params = new URLSearchParams(window.location.search);
            const pnValue = params.get('pn');
            if (pnValue) {
                const titleElem = sourcingBox[0].querySelector('span.section-box-title');
                if (titleElem) {
                    titleElem.appendChild(document.createTextNode(' - '));
                    const pnSpan = document.createElement('span');
                    pnSpan.textContent = pnValue;
                    titleElem.appendChild(pnSpan);
                }
            }
        },
        changeSourcingContentVisibility: function (showSourcingContent) {
            $("#sourcing-filter-footer-seperator").prop("hidden", !showSourcingContent);
            $("#sourcing-filter-footer").toggleClass("d-none", !showSourcingContent);
            $("#sourcing-content").toggleClass("d-none", !showSourcingContent);

            if (!showSourcingContent) {
                setSourcingBoxLoading(false);
                changeSearchPartNumberButton(true);
            }
        },
        collapseAllSections: function () {
            const $button = $("#collapse-expand-all-btn");
            $button.attr("data-is-collaped", "false").trigger("click");
        },
        getSourcingSectionByLegendId: function (legendId) {
            if (sourcingSections) {
                return sourcingSections[legendId];
            }

            return {};
        }
    };
})();

function setUpSourcingDataTable(selector, overridingOptions, events) {
    return $(selector)
        .on('preXhr.dt', events.onPreXhr)
        .on('draw.dt', events.onDraw)
        .DataTable({
            ...sourcingDataTableOptions,
            ...overridingOptions,
            drawCallback: function (settings) {
                const api = this.api();
                const isEmpty = api.rows({ page: 'current' }).data().length === 0;

                if (isEmpty) {
                    const visibleColumns = api.columns(':visible').count();
                    const $emptyCell = $(api.table().body()).find('td.dt-empty');
                    $emptyCell.attr('colspan', visibleColumns);
                }
            }
        });
}

function isPoHub() {
    return $("#is-po-hub").val() === 'true';
}

function isCurrentClient(clientNo) {
    return $("#current-client-id").val() === clientNo;
}

function onSourcingTableRowClickEvent(tableId, id) {
    const table = $(`#${tableId}`).DataTable();

    table.draw(false);
    table.row('#' + id).select();
}

function changeSearchPartNumberButton(isStopped) {
    const searchBtn = $("#search-part-number-btn");

    const btnImg = searchBtn.find("img")[0];
    const btnText = searchBtn.find("span")[0];

    if (isStopped) {
        searchBtn.removeClass("btn-danger");
        searchBtn.addClass("btn-primary");
        btnText.textContent = sourcingButtonLocalizedString.go;
        btnImg.src = '/img/icons/arrow-right-circle.svg';
        btnImg.alt = sourcingButtonLocalizedString.go;

        searchBtn.attr("data-searching", false);
    } else {
        searchBtn.removeClass("btn-primary");
        searchBtn.addClass("btn-danger");
        btnText.textContent = sourcingButtonLocalizedString.cancel;
        btnImg.src = '/img/icons/xmark.svg';
        btnImg.alt = sourcingButtonLocalizedString.cancel;

        searchBtn.attr("data-searching", true);
    }
};

function getSearchingState() {
    return $("#search-part-number-btn").attr("data-searching");
};

function setSourcingBoxLoading(isLoading) {
    const soucingBox = $("#sourcing-box");
    const soucingBoxTitle = soucingBox.find(".section-box-title")[0];

    if (isLoading) {
        if (soucingBox.find(".spinner-loader")[0]) {
            return;
        }

        $(`<div class="spinner-loader ms-1"></div>`).insertAfter(soucingBoxTitle);
    } else {
        soucingBox.find(".spinner-loader").remove();
    }
};

function changeSearchCancelledMessageVisibility(show) {
    if (show) {
        $("#search-cancel-message").removeClass("d-none");
    } else {
        $("#search-cancel-message").addClass("d-none");
    }
};

function syncAndAdjustTableLayout(tableId) {
    const $wrapper = $(`#${tableId}_wrapper`);

    // Reset column widths in both scroll head and body
    $wrapper.find('.dt-scroll-head colgroup col').css('width', '0px');
    $wrapper.find('.dt-scroll-body colgroup col').css('width', '0px');

    // Reset the table width before syncing
    $wrapper.find('.dataTable').css('width', '');

    const $headTable = $wrapper.find('.dt-scroll-head table');
    const $scrollBody = $wrapper.find('.dt-scroll-body');
    const $bodyTable = $wrapper.find('.dt-scroll-body table');
    const actualTableHeight = $bodyTable.outerHeight(true);
    const $belowDiv = $scrollBody.children('div').first();

    // Set the height of that div
    $belowDiv.height(actualTableHeight);

    // Only sync header width if scrollbar is visible
    if ($scrollBody[0].scrollHeight > $scrollBody[0].clientHeight) {
        $headTable.css('width', $bodyTable.width());
    }
}

const ROHS_STATUSES = {
    "1": {
        className: "rohsCompliant", tooltip: window.localizedStrings.ROHSCompliant
    },
    "2": {
        className: "rohsNonCompliant", tooltip: window.localizedStrings.ROHSNonCompliant
    },
    "3": {
        className: "rohsExempt", tooltip: window.localizedStrings.ROHSExempt
    },
    "5": {
        className: "rohsROHS2", tooltip: window.localizedStrings.ROHS2
    },
    "6": {
        className: "rohsROHS56", tooltip: window.localizedStrings.ROHS56
    },
    "7": {
        className: "rohsROHS66", tooltip: window.localizedStrings.ROHS66
    },
}

function getMfrContent(manufacturerNo, manufacturerCode, isRestrictedManufacturer) {
    if (!manufacturerCode) return '';

    let mfrDisplayText = manufacturerCode;

    if (manufacturerNo > 0) {
        mfrDisplayText = GlobalTrader.HtmlHelper.createHyperLinkHtml({
            url: GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(manufacturerNo),
            title: manufacturerCode ?? "",
        })
    }

    const additionalMessage = window.localizedStrings.RestrictedManufacturerWarning;
    if (isRestrictedManufacturer) {
        mfrDisplayText =
            `<div class="d-flex align-items-center">
            ${mfrDisplayText}
            <img class="ms-1" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom"
                src="/img/icons/circle-exclamation-red.svg" title="${additionalMessage}" alt="Restricted Manufacturer" />
        </div>`;
    }

    return mfrDisplayText;
}

function getMfrContentOpenInNewTab(manufacturerNo, manufacturerCode, isRestrictedManufacturer) {
    if (!manufacturerCode) return '';

    let mfrDisplayText = manufacturerCode;

    if (manufacturerNo > 0) {
        mfrDisplayText = GlobalTrader.HtmlHelper.createHyperLinkOpenInNewTabHtml({
            url: GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(manufacturerNo),
            title: manufacturerCode ?? "",
        })
    }

    const additionalMessage = window.localizedStrings.RestrictedManufacturerWarning;
    if (isRestrictedManufacturer) {
        mfrDisplayText =
            `<div class="d-flex align-items-center">
            ${mfrDisplayText}
            <img class="ms-1" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom"
                src="/img/icons/circle-exclamation-red.svg" title="${additionalMessage}" alt="Restricted Manufacturer" />
        </div>`;
    }

    return mfrDisplayText;
}

function getMfrSupplierLiveDataFeedContent(manufacturerNo, manufacturerCode, manufacturerName, isRestrictedManufacturer) {
    const url = GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(manufacturerNo);
    const additionalMessage = window.localizedStrings.RestrictedManufacturerWarning;
    let mfrDisplayText;

    if (manufacturerNo == null) {
        mfrDisplayText = manufacturerName ?? manufacturerCode;
    }
    else {
        mfrDisplayText = manufacturerCode || manufacturerName
            ? GlobalTrader.HtmlHelper.createHyperLinkHtml({
                url: url,
                title: manufacturerCode ?? manufacturerName ?? "",
            })
            : "";
    }

    if (isRestrictedManufacturer) {
        mfrDisplayText =
            `<div class="d-flex align-items-center">
            ${mfrDisplayText}
            <img class="ms-1" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom"
                src="/img/icons/circle-exclamation-red.svg" title="${additionalMessage}" alt="Restricted Manufacturer" />
        </div>`;
    }

    return mfrDisplayText;
}

function getSupplierNameWithDecoration(supplierName, supplierMessage) {
    if (supplierName == null) {
        return null;
    }

    if (supplierMessage != null && supplierMessage.length > 0) {
        const $result = $("<span>")
            .addClass(`hazardousone hazardous`)
            .attr("title", getCleanTextSupplierMessage(supplierMessage))
            .text(supplierName)
            .prop("outerHTML");
        return $result;
    }
    else {
        return supplierName;
    }
}

function getSuplierMessage(row, isPurchaseHubMember) {

    const url = GlobalTrader.PageUrlHelper.Get_URL_Company(row.supplierNo);
    const clientName = row.clientName || null;
    const clientCode = row.clientCode ? `(${row.clientCode})` : "";
    const supplierName = getSupplierNameWithDecoration(row.supplierName, row.supplierMessage);

    // Current client does not match supplier's client
    if (!row.currentClient) {
        //Case 1: Current user is a member of the "Purchase Hub" group or 
        //Case 2: "Is data visible to other companies?" is enabled
        if (isPurchaseHubMember || row.clientDataVisibleToOthers) {
            return `${supplierName || ""} ${clientCode}`.trim();
        }
        //Case 3: Otherwise
        let clientNameFormated = getSupplierNameWithDecoration(clientName, row.supplierMessage)
        return `${clientNameFormated || ""}`;
    }

    // Case 4: Current client does own supplier
    return getUrlDisplayText(supplierName, url);
}

function getUrlDisplayText(text, url) {
    if (!text) {
        return ""
    };
    return url
        ? GlobalTrader.HtmlHelper.createHyperLinkHtml({
            url: url,
            title: `${text}`,
        })
        : text;
}

function getUrlDisplayTextOpenInNewTab(text, url) {
    if (!text) {
        return ""
    };
    return url
        ? GlobalTrader.HtmlHelper.createHyperLinkOpenInNewTabHtml({
            url: url,
            title: `${text}`,
        })
        : text;
}


function getSuplierMessageEpoAndIpo(row, isPurchaseHubMember) {
    const url = GlobalTrader.PageUrlHelper.Get_URL_Company(row.supplierNo);
    const supplierName = getSupplierNameWithDecoration(row.supplierName, row.supplierMessage);

    if (!isPurchaseHubMember) {
        return `${supplierName}`;
    }

    return getUrlDisplayText(supplierName, url);
}

function getPartNoAndRohsStatus(strPart, status) {
    strPart = GlobalTrader.StringHelper.setCleanTextValue(strPart);
    const rohsEntry = ROHS_STATUSES[status];

    if (!rohsEntry) {
        return strPart;
    }

    const $rohsElement = $("<div>").addClass(`rohs-sourcing-dt ${rohsEntry.className}`).attr("title", rohsEntry.tooltip);

    return $rohsElement.text(strPart).prop("outerHTML");
}

function getCleanTextSupplierMessage(input) {
    if (!input || typeof input !== 'string') return '';

    const parts = input.split('&#013;');
    return parts.length > 1 ? parts[1] : '';
}

function setButtonState(selector, enabled) {
    const $btn = $(selector).button();
    $btn.prop("disabled", !enabled);
    if (enabled) {
        $btn.removeClass("ui-state-disabled ui-button-disabled");
    }
}

function initializeTable({ tableId, columns, rowId, onSelectDeselect, data = [] }) {
    const isPoHub = $("#is-po-hub").val() === "true";

    const table = setUpSourcingDataTable(`#${tableId}`, {
        columns,
        rowId,
        data: data,
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
        }
    }, {
        onPreXhr: () => { },
        onDraw: () => { },
    });

    table.on("select deselect", () => onSelectDeselect(table, isPoHub));
    return table;
}

function getSupplierRMA(row) {
    if (!row.currentClient || !row.supplierRMANumber) {
        return row.supplierRMANumber || "";
    }

    return GlobalTrader.HtmlHelper.createHyperLinkHtml({
        url: row.supplierRMADetailUrl,
        title: row.supplierRMANumber,
    });
}

function showYellowText(text) {
    if (!text || text == '') return "";

    let str = '<span style="background-color: rgb(255, 255, 0);">' + text + '</span> '

    return str;
}

function getUrlCustDisplayText(text, id, supplierMessage) {
    if (!text) {
        return ""
    };

    const url = GlobalTrader.PageUrlHelper.Get_URL_Company(id);
    if (supplierMessage != null) {
        text = getSupplierNameWithDecoration(text, supplierMessage);
    }

    return url
        ? GlobalTrader.HtmlHelper.createHyperLinkHtml({
            url: url,
            title: `${text}`,
        })
        : text;
}

function getPriceDisplayTextForStrategicOffersAndReverseLogistics(row) {
    if (isPoHub()) {
        return GlobalTrader.DataTablesHelper.createStackedCell([
            formatCurrencyRate(row.price, row.currencyCode),
            formatCurrencyRate(row.upliftPrice, row.currencyCode)]);
    }

    const displayPrice =
        (row.upliftPrice === 0 || row.upliftPrice == null)
            ? row.price
            : row.upliftPrice;
    return formatCurrencyRate(displayPrice, row.currencyCode);
}

function isRequirementPage() {
    return currentSourcingPageType === sourcingPageTypeMap.RequirementDetails || currentSourcingPageType === sourcingPageTypeMap.HUBRFQDetails;
}

function isHUBRFQDetails() {
    return currentSourcingPageType === sourcingPageTypeMap.HUBRFQDetails;
}

function removeSearchSelectErrorBorder(valueInputId) {
    $(`input[data-input-value-id="${valueInputId}"]`).removeClass(invalidClassName);
}

function resetInput(index, hiddenInputId) {
    const searchInput = document.querySelectorAll("[data-search-input]")[index]
    searchInput.value = '';
    searchInput.classList.remove('d-none');
    $(`#searchSection-${index}`).hide();
    $(`#selectedItems-${index}`).html('');
    $(`#message-${index}`).text('');
    $(`#results-${index}`).html('');
    $(`#result-wrapper-${index}`).hide();
    $(`#${hiddenInputId}`).val('').trigger('change');
}

function displayError(message) {
    const errorContainer = $(".form-error-summary");
    errorContainer.find("div").html(`<p>${thereWereSomeProblemsWithYourForm}</p><p>${pleaseCheckBelowAndTryAgain}</p><p>${message}</p>`);
    errorContainer.show();
}