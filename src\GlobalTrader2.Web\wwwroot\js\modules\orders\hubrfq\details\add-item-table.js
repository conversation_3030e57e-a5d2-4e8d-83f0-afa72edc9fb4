import { LiteDatatable } from "../../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#";

export class AddItemTable {
  constructor(options = {}) {
    this.table = null;
    this.selectedItems = [];
    this.onUpdateDialogButtons = options.onUpdateDialogButtons || null;
  }

    init() {
    this.table = new LiteDatatable("#add-hubrfq-item-table", {
      serverSide: true,
      autoWidth: true,
      paging: true,
      ordering: true,
      pageConfig: {
        pageSize: 10,
      },
      order: [[1, "desc"]],
      ajax: {
        url: "/api/orders/bom/customer-requirement-without-bom",
        type: "POST",
      },
      selectOverride: {
        toggleable: true,
        info: false,
      },
      columns: [
        {
          data: null,
          visible: false,
          defaultContent: "",
        },
        {
          data: "customerRequirementNumber",
          title: localizedTitles.customerRequirementNumber,
          orderSequence: ["desc", "asc"],
          className: "text-wrap text-break",
          defaultContent: "",
          width: "14%",
        },
        {
          data: "companyName",
          title: localizedTitles.companyName,
          orderSequence: ["desc", "asc"],
          className: "text-wrap text-break",
          width: "26%",
          defaultContent: "",
          render: function (data, type, row) {
            return GlobalTrader.StringHelper.setCleanTextValue(data);
          },
        },
        {
          data: null,
          title: localizedTitles.partNo,
          render: (row) =>
            `${GlobalTrader.StringHelper.getPartNoAndRohsStatus(
              row.part,
              row.rohs
            )}`,
          className: "text-wrap text-break",
          orderSequence: ["desc", "asc"],
          defaultContent: "",
          width: "18%",
        },
        {
          data: "receivedDate",
          title: localizedTitles.receivedDateText,
          className: "text-wrap text-break",
          orderSequence: ["desc", "asc"],
          defaultContent: "",
          width: "12%",
        },
        {
          data: "quantity",
          title: localizedTitles.quantity,
          className: "text-wrap text-break",
          orderSequence: ["desc", "asc"],
          defaultContent: "",
          width: "10%",
        },
        {
          data: "formatedPrice",
          title: localizedTitles.formatedPrice,
          orderSequence: ["desc", "asc"],
          className: "text-wrap text-break",
          defaultContent: "",
          width: "10%",
        },
        {
          data: "bomName",
          title: localizedTitles.bomName,
          orderSequence: ["desc", "asc"],
          className: "text-wrap text-break",
          defaultContent: "",
          width: "10%",
        },
      ],
      rowId: "customerRequirementId",
    });

    this.table.init();
    this._registerEvents();
  }

    _registerEvents() {
      this.table.state.table.on("select deselect", (e, dt, type, indexes) => {
      this.updateSelectedItems();
      this.updateDialogButtons();
    });
  }

    updateSelectedItems() {
    if (this.table && this.table.state.table) {
      this.selectedItems = this.table.state.table
        .rows(".selected")
        .data()
        .toArray()
        .map((row) => ({
          customerRequirementId: row.customerRequirementId,
          customerRequirementNumber: row.customerRequirementNumber,
          part: row.part,
          companyName: row.companyName,
        }));
        }
  }

  updateDialogButtons() {
    if (this.onUpdateDialogButtons) {
        this.onUpdateDialogButtons();
    }
  }

  async reload(filterObj) {
    if (this.table) {
      await this.table.reloadAsync(filterObj);
    }
  }

  clearTable() {
    if (this.table && this.table.state.table) {
      this.table.state.table.clear().draw();
    }
  }

    getSelectedItems() {
    return this.selectedItems;
    }

    async search(filterValues) {
        let filterObj = {
            customerRequirementNoLo: filterValues.requirementSearch?.isOn
                ? filterValues.requirementSearch.low
                : null,
            customerRequirementNoHi: filterValues.requirementSearch?.isOn
                ? filterValues.requirementSearch.hi
                : null,
            partSearch: filterValues.partSearch?.isOn
                ? filterValues.partSearch.value
                : null,
            bomName: filterValues.bomNameSearch?.isOn
                ? filterValues.bomNameSearch.value
                : null,
            receivedDateFrom: filterValues.receivedDateFrom?.isOn
                ? filterValues.receivedDateFrom.value
                : null,
            receivedDateTo: filterValues.receivedDateTo?.isOn
                ? filterValues.receivedDateTo.value
                : null,
            bomId: window.stateValue.id,
            _ignoredSave: false
        };
        await this.reload(filterObj);

        $("#add-hubrfq-item-table-wrapper").removeClass("d-none");
    }
}
