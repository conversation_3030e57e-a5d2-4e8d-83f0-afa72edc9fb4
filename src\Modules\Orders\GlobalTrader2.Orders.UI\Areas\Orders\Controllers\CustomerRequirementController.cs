﻿using GlobalTrader2.Aggregator.UseCases.Orders.Lytica.Queries;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyECCN;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifySaleperson;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.Commands.PurchaseHub;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetCompanyAdvisoryNotes;
using GlobalTrader2.Dto.CustomerRequirement;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Orders.UI.ViewModel.CustomerRequirement;
using GlobalTrader2.Orders.UI.ViewModel.Requirements.Request;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.AltStatus.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Close.Close.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Commands.Create;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Commands.UpsertLyticaData;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.SalesInfo.SalesInfo.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DataList.PartDetail.PartDetail.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DataList.PartECCNSearch.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DataList.SalesPersion.AutoSearchSalesPersion.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DeleteAlternatePart.AlternatePart.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DeletePartWatch.Command;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.IHSEccn.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartDetail.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartMatch.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartWatch.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries.GetCompanyById;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.RestrictedManufacturer.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Commands.AddSourcingResult;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Commands.EditSourcingResult;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Queries.GetCustomerRequirementSourcingResults;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Queries.GetSourcingResult;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetCurrencyRateCurrentAtDate;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingResultForCustomerReq;
using GlobalTrader2.Settings.UseCases.CompanySettings.PrintedDocuments.Queries;
using GlobalTrader2.Settings.UseCases.CompanySettings.Products.Queries;
using GlobalTrader2.Settings.UseCases.Manufacturers.Queries;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries.GetNameByLoginId;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/orders/customer-requirements")]
    public class CustomerRequirementController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SessionManager _sessionManager;
        private readonly IMapper _mapper;
        private readonly SecurityManager _securityManager;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        private readonly IStringLocalizer<SharedUI.OfferStatus> _offerStatusLocalizer;
        private readonly IStringLocalizer<MessageResources> _messageLocalizer;
        private readonly ILogger<CustomerRequirementController> _logger;

        public CustomerRequirementController(IMediator mediator, SessionManager sessionManager, IMapper mapper, SecurityManager securityManager, IStringLocalizer<Misc> miscLocalizer, IStringLocalizer<SharedUI.OfferStatus> offerStatusLocalizer, IStringLocalizer<MessageResources> messageLocalizer, ILogger<CustomerRequirementController> logger)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;
            _mapper = mapper;
            _securityManager = securityManager;
            _miscLocalizer = miscLocalizer;
            _messageLocalizer = messageLocalizer;
            _offerStatusLocalizer = offerStatusLocalizer;
            _logger = logger;
        }

        [HttpPost("list")]
        public async Task<IActionResult> GetCustomerRequirement(GetCustomerRequirementRequest request, CancellationToken cancellationToken)
        {
            request.ClientId = ClientId;
            request.LoginId = (request.ViewLevelList == (int)ViewLevelList.My) ? UserId : null;
            request.TeamId = (request.ViewLevelList == (int)ViewLevelList.Team) ? _sessionManager.GetInt32(SessionKey.LoginTeamID) : null;
            request.DivisionId = (request.ViewLevelList == (int)ViewLevelList.Division) ? _sessionManager.GetInt32(SessionKey.LoginDivisionID) : null;

            var query = new GetCustomerRequirementQuery
            {
                Index = request.Index,
                Size = request.Size,
                OrderBy = request.OrderBy,
                SortDir = request.SortDir,
                ClientId = request.ClientId,
                LoginId = request.LoginId,
                TeamId = request.TeamId,
                DivisionId = request.DivisionId,
                PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
                PartWatch = request.PartWatch,
                CMSearch = request.CMSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CMSearch) : null,
                AS6081 = request.AS6081,
                BomCode = request.BomCode != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.BomCode) : null,
                BOMId = request.BOMId,
                BOMNameSearch = request.BOMNameSearch != null ? request.BOMNameSearch.RemovePunctuation() : null,
                ContactSearch = request.ContactSearch != null ? StringHelper.RemoveNonAlphanumericExceptSpacesAndRetainPercent(request.ContactSearch) : null,
                CustomerRequirementNoHi = request.CustomerRequirementNoHi,
                CustomerRequirementNoLo = request.CustomerRequirementNoLo,
                DatePromisedFrom = request.DatePromisedFrom,
                DatePromisedTo = request.DatePromisedTo,
                IncludeClosed = request.IncludeClosed,
                IndustryName = request.IndustryName,
                ReceivedDateFrom = request.ReceivedDateFrom,
                ReceivedDateTo = request.ReceivedDateTo,
                RecentOnly = request.RecentOnly,
                REQStatus = request.REQStatus,
                Salesman = request.Salesman,
                SelectedClientNo = _sessionManager.IsGSA ? request.SelectedClientNo : null,
                SelectedLoginNo = UserId,
                TotalHi = request.TotalHi,
                TotalLo = request.TotalLo
            };

            var result = await _mediator.Send(query, cancellationToken);
            var resultView = _mapper.Map<BaseResponse<IEnumerable<CustomerRequirementViewModel>>>(result);

            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var response = new DatatableResponse<IEnumerable<CustomerRequirementViewModel>>()
            {
                Success = resultView.Success,
                Data = resultView.Data,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
                Draw = request.Draw
            };

            return Ok(response);
        }

        [HttpGet("{id}")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
        public async Task<IActionResult> GetCustomerRequirement(int id)
        {
            return Ok(await _mediator.Send(new GetCustomerRequirementDetailItemQuery()
            {
                CustomerRequirementNo = id,
                ClientId = ClientId,
                IsGSA = _sessionManager.GetBool(SessionKey.IsGSA),
                ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
                ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
            }));
        }

        [HttpPost("search-company-items")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_Add)]
        public async Task<IActionResult> SearchCompanyItemss([FromBody] SearchComSearchCompanyItemRequest request)
        {
            bool allowCheckedCompanyOnStop = await _securityManager.CheckFunctionPermissions(UserId, false, [SecurityFunction.Orders_SalesOrder_AllowCheckedCompanyOnStop]);

            var name = StringHelper.RemovePunctuationRetainingPercentSigns(request!.Name ?? string.Empty);

            var result = await _mediator.Send(new GetAllCompanyQuery
            {
                ClientId = ClientId,
                Name = name,
                POApproved = request.POApproved,
                SOApproved = request.SOApproved,
                SupplierStop = request.SupplierStop ?? false,
                AllowCheckedCompanyOnStop = allowCheckedCompanyOnStop,
                Index = request.Index,
                OrderBy = request.OrderBy ?? 1,
                SortDir = request.SortDir ?? 1,
                Size = request.Size,
                PoNoHi = null,
                PoNoLo = null,
            });

            var totalRecords = result?.Data?.FirstOrDefault()?.RowCnt;

            var response = new DatatableResponse<IEnumerable<CompanySearchItemDto>>()
            {
                Success = result!.Success,
                Data = result.Data,
                RecordsTotal = totalRecords ?? 0,
                RecordsFiltered = totalRecords ?? 0,
                Draw = request.Draw
            };

            return Ok(response);
        }

        [HttpPost("auto-search-part-eccn")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoSearchPartECCN([FromBody] AutoSearchPartECCNRequest request)
        {
            var response = await _mediator.Send(new AutoSearchPartECCNSearchQuery
            {
                NameSearch = request.Keyword?.Trim(),
            });

            return new JsonResult(response);
        }

        [HttpPost("auto-search-sale-persion")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoSearchSalePersion([FromBody] AutoSearchSalePersionRequest request)
        {
            var response = await _mediator.Send(new AutoSearchSalesPersionQuery
            {
                NameSearch = request.Keyword?.Trim(),
                ClientID = ClientId,
                ExcludeCurrentUser = request.ExcludeCurrentUser ?? false,
                GlobalLoginClientNo = request.GlobalLoginClientNo,
                LimitToCurrentUsersDivision = request.LimitToCurrentUsersDivision ?? false,
                LimitToCurrentUsersTeam = request.LimitToCurrentUsersTeam ?? false,
                LoginDivisionID = LoginDivisionID,
                LoginID = UserId,
                LoginTeamID = LoginTeamID
            });

            return new JsonResult(response);
        }

        [HttpGet("get-company-sales-info/{id}")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
        public async Task<IActionResult> GetCompanySalesInfo(int id)
        {
            return new JsonResult(await _mediator.Send(new GetSalesInfoQuery() { Id = id }));
        }

        [HttpGet("customer-requirement-header-info/{customerRequirementId}")]
        public async Task<IActionResult> GetCustomerRequirementDetailInfo(int customerRequirementId)
        {
            return new JsonResult(await _mediator.Send(new GetCustomerRequirementDetailQuery() { CustomerRequirementId = customerRequirementId }));
        }

        [HttpGet("part-detail/{customerRequirementId}")]
        public async Task<IActionResult> GetPartDetailInfo(int customerRequirementId)
        {
            var partDetail = await _mediator.Send(new GetPartDetailQuery()
            {
                CustomerRequirementId = customerRequirementId,
                ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
                ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
            });
            var pdCompanyAdvisory = await _mediator.Send(new GetCompanyAdvisoryNotesQuery(partDetail.Data!.CompanyNo));
            partDetail.Data.CompanyAdvisoryNote = pdCompanyAdvisory.Data!.AdvisoryNotes;
            var pdManuFacturerAdvisory = await _mediator.Send(new GetManufacturerAdvisoryNoteQuery() { ManufacturerId = partDetail.Data.ManufacturerNo ?? 0, ClientId = ClientId });
            partDetail.Data.ManufacturerAdvisoryNote = pdManuFacturerAdvisory.Data.FirstOrDefault().AdvisoryNotes;
            var statusMsg = await _mediator.Send(new GetProductStatusMessageQuery()
            {
                ClientNo = partDetail.Data.ClientNo,
                ProductId = partDetail.Data.ProductNo,
                IsHazardous = partDetail.Data.IsHazardous,
                IsOrderViaIPOonly = partDetail.Data.IsOrderViaIPOonly,
                IsRestrictedProduct = partDetail.Data.IsRestrictedProduct
            });
            partDetail.Data.HazardousMsg = statusMsg.Data?.HazardousMsg;
            partDetail.Data.IpoMsg = statusMsg.Data?.IOPMsg;
            partDetail.Data.RestrictedMsg = statusMsg.Data?.RestrictedMsg;
            if (partDetail.Data.LifeCycleStage is not null)
            {
                var footer = await _mediator.Send(new GetHazardousNoteQuery
                {
                    ClientNo = partDetail.Data.ClientNo,
                    LifeCycleStage = partDetail.Data.LifeCycleStage
                });

                partDetail.Data.IHSStatusDefination = footer.Data?.FooterText ?? string.Empty;
            }
            partDetail.Data.IHSECCNCodeDefination = Functions.ReplaceLineBreaks(partDetail.Data.IHSECCNCodeDefination.EmptyIfNull());
            // co the la SessionKey.Culture
            var ci = new CultureInfo(HttpContext.Session.GetString(SessionKey.Culture) ?? GlobalLanguage.GetLanguageCode(GlobalLanguage.List.English));
            string strPrice = Core.Helpers.Functions.FormatCurrency(partDetail.Data.Price, ci, partDetail.Data.CurrencyCode.EmptyIfNull(), 5, false);
            if (partDetail.Data.CurrencyNo != _sessionManager.GetInt32(SessionKey.ClientCurrencyID)) strPrice += String.Format(" ({0})", Core.Helpers.Functions.FormatCurrency(await ConvertValueToBaseCurrencyAsync(partDetail.Data.Price, (int)partDetail.Data.CurrencyNo!), ci, _sessionManager.GetString(SessionKey.ClientCurrencyCode).EmptyIfNull(), 5, false));
            partDetail.Data.StrPrice = strPrice;
            partDetail.Data.PriceRaw = Core.Helpers.Functions.FormatCurrency(partDetail.Data.Price, ci, string.Empty, 5, false);
            partDetail.Data.Currency = Core.Helpers.Functions.FormatCurrencyDescription(partDetail.Data.CurrencyDescription.EmptyIfNull(), partDetail.Data.CurrencyCode.EmptyIfNull());
            partDetail.Data.Notes = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.Notes.EmptyIfNull());
            partDetail.Data.Instructions = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.Instructions.EmptyIfNull());
            partDetail.Data.StrDLUP = await GetFormatDLUP(partDetail.Data.UpdatedBy, partDetail.Data.DLUP);
            string strReason = (partDetail.Data.ReasonNo > 0) ? string.Format(" ({0})", partDetail.Data.ClosedReason) : string.Empty;
            partDetail.Data.DisplayStatus = string.Format($"{partDetail.Data.DisplayStatus} {strReason}");
            partDetail.Data.HidTargetSellPrice = Core.Helpers.Functions.FormatCurrency(partDetail.Data.TargetSellPrice.ZeroIfNull(), ci, "", 5, false);
            partDetail.Data.HidCompetitorBestOffer = Core.Helpers.Functions.FormatCurrency(partDetail.Data.CompetitorBestOffer.ZeroIfNull(), ci, "", 5, false);
            partDetail.Data.StrTargetSellPrice = Core.Helpers.Functions.FormatCurrency(partDetail.Data.TargetSellPrice.ZeroIfNull(), ci, partDetail.Data.CurrencyCode.EmptyIfNull(), 5, false);
            partDetail.Data.StrCompetitorBestOffer = Core.Helpers.Functions.FormatCurrency(partDetail.Data.CompetitorBestOffer.ZeroIfNull(), ci, partDetail.Data.CurrencyCode.EmptyIfNull(), 5, false);
            partDetail.Data.ReqTypeText = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.ReqTypeText.EmptyIfNull());
            partDetail.Data.ReqForTraceabilityText = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.ReqForTraceabilityText.EmptyIfNull());
            partDetail.Data.EAU = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.EAU.EmptyIfNull());
            partDetail.Data.IsPOHubReleased = Convert.ToInt32(partDetail.Data.POHubReleaseBy) > 0;
            partDetail.Data.DutyCodeAndRate = string.Format("{0} ({1})", partDetail.Data.DutyCode, Core.Helpers.Functions.FormatNumeric(partDetail.Data.ProductDutyRate.ZeroIfNull(), 5, ci));
            partDetail.Data.Descriptions = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.Descriptions.EmptyIfNull());
            if (string.IsNullOrEmpty(partDetail.Data.Descriptions) || partDetail.Data.Descriptions.Length <= 10)
                partDetail.Data.DescShort = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.Descriptions.EmptyIfNull());
            else partDetail.Data.DescShort = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.Descriptions.Substring(0, 10));
            partDetail.Data.IHSECCNCodeDefination = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.IHSECCNCodeDefination);
            partDetail.Data.StockAvailableDetail = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.StockAvailableDetail.EmptyIfNull());
            partDetail.Data.PurchasingNotes = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.PurchasingNotes.EmptyIfNull());
            partDetail.Data.CompanyAdvisoryNote = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.CompanyAdvisoryNote.EmptyIfNull());
            partDetail.Data.ManufacturerAdvisoryNote = Core.Helpers.Functions.ReplaceLineBreaks(partDetail.Data.ManufacturerAdvisoryNote);

            partDetail.Data.LastUpdated = LocalizerHelper.FormatDLUP(partDetail.Data.DLUP, partDetail.Data.UpdatedByEmployeeName, _miscLocalizer, CultureInfo.CurrentCulture);

            return new JsonResult(partDetail);
        }

        [HttpPost("main-info")]
        [ApiAuthorize(true, SecurityFunction.Orders_Sourcing_AddToRequirement)]
        public async Task<IActionResult> GetCustomerRequirementsMainInfo([FromBody] CustomerRequirementsMainInfoRequest request, CancellationToken cancellation)
        {
            var query = new GetCustomerRequirementsMainInfoQuery()
            {
                ClientId = ClientId,
                OrderBy = request.OrderBy,
                SortDir = request.SortDir,
                PageIndex = request.Index / request.Size,
                PageSize = request.Size,
                PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
                CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
                CustomerRequirementNoHi = request.CustomerRequirementNoHi,
                CustomerRequirementNoLo = request.CustomerRequirementNoLo,
                IncludeClosed = request.IncludeClosed,
                ReceivedDateFrom = request.ReceivedDateFrom,
                ReceivedDateTo = request.ReceivedDateTo,
            };

            var result = await _mediator.Send(query, cancellation);

            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var cultureInfo = new CultureInfo(Culture);

            foreach (var item in result.Data ?? Enumerable.Empty<CustomerRequirementMainInfoDto>())
            {
                item.FormatedPrice = Core.Helpers.Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
            }

            var response = new DatatableResponse<IEnumerable<CustomerRequirementMainInfoDto>>()
            {
                Success = result.Success,
                Data = result.Data,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
                Draw = request.Draw
            };

            return Ok(response);
        }

        [HttpGet("get-part-detail")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_Add)]
        public async Task<IActionResult> GetPartDetail([FromQuery] string partNo, [FromQuery] int companyNo)
        {
            return new JsonResult(await _mediator.Send(new GetAllPartDetailQuery()
            {
                ClientNo = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo),
                CompanyNo = companyNo
            }));
        }

        [HttpPost]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_Add)]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateAsync([FromBody] CreateCustomerRequirementRequest request)
        {
            var command = new CreateCustomerRequirementCommand
            {
                ClientNo = ClientId,
                Part = request.PartNo,
                ManufacturerNo = request.ManufactureValue,
                DateCode = request.DateCode?.Trim(),
                PackageNo = request.PackageValue,
                Quantity = request.Quantity,
                Price = request.TargetPrice,
                CurrencyNo = request.Currency,
                ReceivedDate = DateTime.Now,
                Salesman = request.Salesman,
                DatePromised = request.DeliDateRequired,
                Notes = request.NoteToCustomer?.Trim(),
                Instructions = request.InternalNotes?.Trim(),
                Shortage = request.Shortage,
                CompanyNo = request.CompanyNo,
                ContactNo = request.Contact,
                UsageNo = request.Usage,
                Alternate = false,
                OriginalCustomerRequirementNo = request.OriginalCustomerRequirementNo,
                ReasonNo = request.ReasonNo,
                ProductNo = request.ProductValue,
                CustomerPart = request.CustPartNo,
                Closed = request.Closed,
                Rohs = request.Rohs,
                UpdatedBy = UserId,
                PartWatch = request.PartWatch ?? false,
                Bom = request.Bom ?? false,
                BomName = request.BomName?.Trim(),
                BOMNo = request.HUBRFQ,
                FactorySealed = request.FactorySealed ?? false,
                MSL = request.MSL?.Trim(),
                PQA = request.PartialQuantityAcceptable ?? false,
                ObsoleteChk = false,
                LastTimeBuyChk = false,
                RefirbsAcceptableChk = request.RefurbsAcceptable ?? false,
                TestingRequiredChk = request.TestingRequired ?? false,
                TargetSellPrice = request.TargetSellPrice,
                CompetitorBestOffer = request.BestOffer,
                CustomerDecisionDate = request.CustomerDecisionDate,
                RFQClosingDate = request.RFQClosingDate,
                QuoteValidityRequired = request.QuoteValidityRequired,
                Type = request.Type,
                OrderToPlace = request.OrderToPlace ?? false,
                RequirementforTraceability = request.RequirementForTraceability,
                EAU = request.EstimatedAnnualUsage?.Trim(),
                AlternativesAccepted = request.AlternativesAccepted ?? false,
                RepeatBusiness = request.RegularRepeatBusiness ?? false,
                SupportTeamMemberNo = request.SupportTeamMemberValue,
                CountryOfOrigin = request.CountryOfOrigin?.Trim(),
                CountryOfOriginNo = request.CountryOfOriginNo,
                LifeCycleStage = request.PartStatus?.Trim(),
                HTSCode = request.HTSCode?.Trim(),
                AveragePrice = request.AveragePrice,
                Packing = request.Packing?.Trim(),
                PackagingSize = request.PackagingSize?.Trim(),
                Descriptions = request.Descriptions?.Trim(),
                IHSPartsId = request.IHSPartsId,
                IHSCurrencyCode = request.ColPriceCurrency?.Trim(),
                IHSProduct = request.IHSProduct?.Trim(),
                ECCNCode = request.EccnCodeLabelValue?.Trim(),
                ECCNNo = request.ECCNCodeValue,
                AS6081 = request.AS6081
            };

            var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);

            var aggregatorCommand = new Aggregator.UseCases.Orders.Requirements.Commands.CreateCustomerRequirementCommand
            {
                CreateCustomerRequestCommand = command,
                NotifyECCNCommand = new NotifyECCNCommand
                {
                    ClientId = ClientId,
                    UserId = UserId,
                    SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                    PartNo = command.Part,
                    CustomRequirementUrl = $"{urlBase}{urlPath}"
                },
                CreateCustomerRequirementAsAllAlternateCommand = !request.SuggestedReboundAlternative ? null : new CreateCustomerRequirementAsAllAlternateCommand
                {
                    // Fully handle in aggregator command
                    CustRequirementId = -1,
                    ClientNo = ClientId,
                    Part = request.PartNo,
                },
                PartMatchInfoQuery = new GetPartMatchInfoQuery
                {
                    PartSearch = StringHelper.RemovePunctuationRetainingPercentSigns(request.PartNo),
                    ClientId = ClientId,
                },
                SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail)
            };

            var response = await _mediator.Send(aggregatorCommand);
            return Ok(response);
        }

        [HttpDelete("delete")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
        public async Task<IActionResult> DeleteAlternatePartAsync([FromBody] List<int> alternatePartIds)
        {
            return Ok(await _mediator.Send(new DeleteAlternatePartCommand() { CustomerRequirementIds = [.. alternatePartIds] }));
        }

        [HttpPost("close")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_PartsRequired_Close)]
        public async Task<IActionResult> CloseAsync([FromBody] CloseCustomerRequirement close)
        {
            return Ok(await _mediator.Send(new UpdateCloseCommand()
            {
                CustomerRequirementId = close.CustomerRequirementId,
                IncludeAllRelatedAlternates = true,
                ReasonNo = close.Reason,
                UpdatedBy = UserId
            }));
        }

        [HttpPut("change-alt-status")]
        public async Task<IActionResult> ChangeAltStatus([FromBody] ChangeAltStatusRequest changeAltStatus)
        {
            return Ok(await _mediator.Send(new UpdateAltStatusCommand()
            {
                Id = changeAltStatus.Id,
                Status = changeAltStatus.Status,
                UpdatedBy = UserId
            }));
        }

        [HttpGet("get-bom-id")]
        public async Task<IActionResult> GetBomID([FromQuery] int id)
        {
            return Ok(await _mediator.Send(new GetCustomerRequirementECCNNotifyInfoQuery()
            {
                CustomerRequirementId = id
            }));
        }

        [HttpGet("get-ihs-eccn-code-detail")]
        public async Task<IActionResult> GetIHSEccnCodeDetail([FromQuery] string eccnCode)
        {
            return Ok(await _mediator.Send(new GetAllIHSEccnQuery()
            {
                ClientId = ClientId,
                EccnCode = eccnCode,
                UpdateBy = UserId
            }));
        }

        [HttpGet("get-restricted-manufacturer")]
        public async Task<IActionResult> GetRestrictedManufacturer([FromQuery] int rsManufacturerNo)
        {
            return Ok(await _mediator.Send(new GetRestrictedManufacturerQuery()
            {
                ClientNo = ClientId,
                ManufactureNo = rsManufacturerNo
            }));
        }

        [HttpPut("save-edit")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_MainInformation_Edit)]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveEdit([FromBody] UpdateCustomerRequirementRequest request)
        {
            var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var command = _mapper.Map<Aggregator.UseCases.Orders.Requirements.Commands.UpdateCustomerRequirementFlowCommand>(request);
            command.UpdateBy = UserId;
            command.ClientNo = ClientId;
            command.SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}");
            command.CustomRequirementUrl = $"{urlBase}{urlPath}";
            command.SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail);
            return Ok(await _mediator.Send(command));
        }

        private async Task<double> ConvertValueToBaseCurrencyAsync(double? dblValueToConvert, int currencyNo)
        {
            var dblRate = await _mediator.Send(new GetCurrentAtDateQuery()
            {
                CurrencyNo = currencyNo,
                DatePoint = DateTime.Now,
            });
            double dblValueToConvert_AsDouble = dblValueToConvert == null ? 0 : (double)dblValueToConvert;
            return dblValueToConvert_AsDouble / dblRate.Data!.ExchangeRate;
        }

        private async Task<string> GetFormatDLUP(int? updatedBy, DateTime? dlup)
        {
            BaseResponse<string> updatedByResult = new BaseResponse<string>();
            if (updatedBy.HasValue)
            {
                updatedByResult = await _mediator.Send(new GetNameByLoginIdQuery(updatedBy.Value));
            }
            if (dlup.HasValue)
            {
                return LocalizerHelper.FormatDLUP(dlup.Value, updatedByResult.Data, _miscLocalizer, CultureInfo.CurrentCulture);
            }

            return string.Empty;
        }

        [HttpPut("add-alternate")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_PartsRequired_AddAlternate)]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddAlternate([FromBody] CreateAlternateCustomerRequirementRequest request)
        {
            var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var command = _mapper.Map<Aggregator.UseCases.Orders.Requirements.Commands.CreateAlternatePartCustomerRequirementFlowCommand>(request);
            command.UpdateBy = UserId;
            command.ClientNo = ClientId;
            command.SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}");
            command.CustomRequirementUrl = $"{urlBase}{urlPath}";
            command.SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail);
            return Ok(await _mediator.Send(command));
        }

        [HttpPost("add-to-hubrfq")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddToHUBRFQ([FromBody] AddToHUBRFQRequest request)
        {
            return Ok(await _mediator.Send(new AddRequirementToHUBRFQCommand()
            {
                CustomerRequirementId = request.CustomerRequirementId,
                RecipientLoginIDs = request.AryRecipientLoginIDs ?? [],
                AssignUserNo = request.AssignUserNo,
                ClientId = ClientId,
                CompanyId = request.CompanyId,
                Contact2Id = request.Contact2Id,
                ContactNo = 0,
                CurrencyId = request.CurrencyId,
                DateRequired = request.DateRequired,
                LoginId = UserId,
                PartNo = request.PartNo?.Trim(),
                Subject = _messageLocalizer["PurchaseRequest"],
                RLStockSubject = _messageLocalizer["RLStockSubject1"],
                HUBRFQRaisedSubject = _messageLocalizer["HUBRFQRaised"],
                LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty,
                POHubMailGroupId = _sessionManager.GetInt32(SessionKey.POHubMailGroupId) ?? 0,
                IsPoHub = _sessionManager.GetBool(SessionKey.IsPOHub),
                ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty,
                UrlBase = RequestHelper.GetApplicationUrl(HttpContext.Request),
                Sender = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty
            }));
        }

        [HttpPost("upsert-lytica-api-data")]
        [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_Add)]
        public async Task<IActionResult> UpsertLyticaApiData([FromBody] UpsertLyticaApiDataRequest request)
        {
            await _mediator.Send(new UpsertLyticaDataCommand()
            {
                PartNo = request.PartNo,
                UpdatedBy = UserId
            });

            return Ok();
        }

        [HttpPost("refresh-lytica-api-data")]
        public async Task<IActionResult> RefreshLyticaApiData([FromBody] RefreshLyticaApiDataRequest request)
        {
            var isOutdated = await _mediator.Send(new IsOutdatedLyticaPartQuery
            {
                PartNumber = request.PartNumber,
                MfrNo = request.MfrNo,
                MfrName = request.MfrName,
                OutdatedSince = 3
            });

            if (isOutdated.Data)
            {
                _logger.LogDebug("Lytica data for part {PartNumber} is outdated, updating...", request.PartNumber);
                await _mediator.Send(new UpsertLyticaDataCommand()
                {
                    PartNo = request.PartNumber,
                    UpdatedBy = UserId
                });
            }

            return Ok();
        }

        #region "Sourcing Results"
        [HttpGet("part-detail/{customerRequirementId}/sourcing-results")]
        public async Task<IActionResult> GetSourcingResults(int customerRequirementId)
        {
            var query = new GetCusReqSourcingResultsQuery
            {
                ClientId = ClientId,
                CustomerRequirementId = customerRequirementId,
                IsPOHub = _sessionManager.IsPOHub,
                ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
                ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
                CultureInfo = _sessionManager.GetCurrentCulture()
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("part-detail/sourcing-results/{sourcingResultId}")]
        public async Task<IActionResult> GetSourcingResultByIdAsync(int sourcingResultId)
        {
            var query = new GetSourcingResultByIdQuery
            {
                SourcingResultId = sourcingResultId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpPut("part-detail/sourcing-results/{sourcingResultId}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditSourcingResultAsync(int sourcingResultId, [FromBody] EditSourcingResultRequestDto requestDto)
        {
            var canEdit = await CanProcessOpenCustomerRequirementAsync(null, [sourcingResultId]);

            if (!canEdit)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }

            if (requestDto.PageType == (int)SourcingPageType.RequirementDetails)
            {
                var hasPermission = await _securityManager.CheckFunctionPermissions(UserId, false, new List<SecurityFunction>() { SecurityFunction.Orders_CustomerRequirement_SourcingResults_Edit });

                if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);
            }

            var querySourcingResultById = new GetSourcingResultByIdQuery
            {
                SourcingResultId = sourcingResultId
            };

            var resultSourcingResultById = await _mediator.Send(querySourcingResultById);

            var query = new EditSourcingResultCommand
            {
                SourcingResultId = sourcingResultId,
                SupplierNo = resultSourcingResultById.Data?.SupplierNo,
                CurrencyNo = requestDto.CurrencyNo,
                DateCode = requestDto.DateCode,
                ManufacturerNo = requestDto.ManufacturerNo,
                MslLevelNo = requestDto.MSLLevelNo,
                Notes = requestDto.Notes,
                OfferStatusNo = requestDto.OfferStatusNo,
                PackageNo = requestDto.PackageNo,
                Part = requestDto.Part,
                PartWatchMatch = resultSourcingResultById.Data?.PartWatchMatch,
                Price = requestDto.Price,
                ProductNo = requestDto.ProductNo,
                Quantity = requestDto.Quantity,
                Rohs = requestDto.ROHS,
                UpdatedBy = UserId,
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpDelete("part-detail/sourcing-result/part-watch")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeletePartWatchAsync([FromBody] DeletePartWatchCommand command)
        {
            var canDelete = await CanProcessOpenCustomerRequirementAsync(null, command.Ids);

            if (!canDelete)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("part-detail/sourcing-results")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddSourcingResultAsync([FromBody] AddSourcingResultRequestDto requestDto)
        {
            var canAdd = await CanProcessOpenCustomerRequirementAsync(requestDto.CustomerRequirementNo, null);

            if (requestDto.PageType == (int)SourcingPageType.RequirementDetails)
            {
                var hasPermission = await _securityManager.CheckFunctionPermissions(UserId, false, new List<SecurityFunction>() { SecurityFunction.Orders_CustomerRequirement_SourcingResults_Add });

                if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);
            }

            if (!canAdd)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }

            var query = new AddSourcingResultCommand
            {
                CustomerRequirementNo = requestDto.CustomerRequirementNo,
                TypeName = null,
                SupplierNo = requestDto.SupplierNo,
                CurrencyNo = requestDto.CurrencyNo,
                DateCode = requestDto.DateCode,
                ManufacturerNo = requestDto.ManufacturerNo,
                MslLevelNo = requestDto.MslLevelNo,
                Notes = requestDto.Notes,
                OfferStatusNo = requestDto.OfferStatusNo,
                PackageNo = requestDto.PackageNo,
                Part = requestDto.Part,
                Price = requestDto.Price,
                ProductNo = requestDto.ProductNo,
                Quantity = requestDto.Quantity,
                Rohs = requestDto.Rohs,
                UpdatedBy = UserId,
                ClientNo = ClientId,
                Salesman = UserId,
                IsSupHasCurrency = requestDto.IsSupHasCurrency,
            };
            var result = await _mediator.Send(query);

            if (result.Success && result.Data?.OfferId > 0)
            {
                var matchedReqs = await _mediator.Send(new GetPartWatchMatchingRequirementQuery() { ClientId = ClientId, OfferId = result.Data?.OfferId });
                if (matchedReqs.Data != null && matchedReqs.Data.Any())
                {
                    var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
                    var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
                    foreach (var matchedReq in matchedReqs.Data)
                    {
                        await _mediator.Send(new NotifySalepersonCommand
                        {
                            Subject = _messageLocalizer["SalepersonPartWatchMatchSubject"],
                            CustomerRequirementNo = matchedReq.CustomerRequirementNo,
                            SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                            UserId = UserId,
                            SalepersonId = matchedReq.Salesman ?? 0,
                            CustomRequirementUrl = $"{urlBase}{urlPath}?req={matchedReq.CustomerRequirementId}",
                            SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail)
                        });
                    }
                }
            }

            return Ok(result);
        }

        #endregion

        [HttpGet("search-id")]
        public async Task<IActionResult> GetRequirementIdByNumber([FromQuery] int reqNum)
        {
            return Ok(await _mediator.Send(new GetCustomerRequirementIdQuery()
            {
                ClientNo = ClientId,
                ReqNum = reqNum
            }));
        }

        [HttpPut("clone-and-add-hubrfq")]
        public async Task<IActionResult> CloneRequirementDataHUBRFQ([FromBody] CloneRequirementDataHUBRFQRequest request)
        {
            var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var command = _mapper.Map<Aggregator.UseCases.Orders.Requirements.Commands.CloneRequirementDataHUBRFQFlowCommand>(request);
            command.UpdatedBy = UserId;
            command.ClientNo = ClientId;
            command.SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}");
            command.CustomRequirementUrl = $"{urlBase}{urlPath}";
            command.SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail);
            return Ok(await _mediator.Send(command));
        }

        [HttpGet("company/{companyId}/details")]
        public async Task<IActionResult> GetCompanyByIdAsync([FromRoute] int companyId)
        {
            return Ok(await _mediator.Send(new GetCompanyByIdQuery(companyId)));
        }

        [HttpPut("clone-and-send-hub")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CloneRequirementDataAndSendHUB([FromBody] CloneRequirementDataAndSendHubRequest request)
        {
            var urlPath = Navigations.CustomerRequirementDetails(string.Empty).CtaUri;
            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var command = _mapper.Map<Aggregator.UseCases.Orders.Requirements.Commands.CloneRequirementDataAndSendHubFlowCommand>(request);
            command.UpdatedBy = UserId;
            command.ClientNo = ClientId;
            command.SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}");
            command.CustomRequirementUrl = $"{urlBase}{urlPath}";
            command.SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail);
            command.Subject = _messageLocalizer["PurchaseRequest"];
            command.RLStockSubject = _messageLocalizer["RLStockSubject1"];
            command.HUBRFQRaisedSubject = _messageLocalizer["HUBRFQRaised"];
            command.POHubMailGroupId = _sessionManager.GetInt32(SessionKey.POHubMailGroupId) ?? 0;
            command.IsPoHub = _sessionManager.GetBool(SessionKey.IsPOHub);
            command.ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty;
            command.UrlBase = urlBase;
            command.SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty;
            return Ok(await _mediator.Send(command));
        }

        private async Task<bool> CanProcessOpenCustomerRequirementAsync(int? customerRequirementId, IList<int>? sourcingResultIds)
        {
            if (customerRequirementId.HasValue)
            {
                var query = new GetPartDetailQuery
                {
                    CustomerRequirementId = customerRequirementId.Value,
                    ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
                    ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
                };

                var partDetail = await _mediator.Send(query);

                // Return true if Closed is null or false
                return partDetail?.Data?.Closed != true;
            }

            var queryBySourcingIds = new GetPartStatusBySourcingResultIdsQuery
            {
                SourcingResultIds = sourcingResultIds ?? []
            };

            var response = await _mediator.Send(queryBySourcingIds);

            // Return true only if successful and requirement is not closed
            return response.Success && !response.Data;
        }

        [HttpGet("{customerRequirementId}/sourcing-results")]
        public async Task<IActionResult> GetSourcingResultForCustomerRequirement([FromRoute] int customerRequirementId, [FromQuery] bool isFromQuote)
        {
            var query = new GetSourcingResultForCustomerReqQuery
            {
                CustomerRequirementId = customerRequirementId,
                IsPOHub = _sessionManager.IsPOHub,
                IsFromQuote = isFromQuote
            };

            var result = await _mediator.Send(query);
            var cultureInfo = new CultureInfo(Culture);
            if (result.Success && result.Data != null)
            {
                foreach (var item in result.Data)
                {
                    if (item.POHubReleaseBy == 0 && !_sessionManager.IsPOHub && item.POHubCompanyNo.HasValue)
                        continue;
                    double currentRateAtDate = 1;
                    if (item.SourcingTable == "PQ" || item.SourcingTable == "OFPH" || item.SourcingTable == "EXPH")
                    {
                        item.SupplierNo = _sessionManager.IsPOHub ? item.POHubCompanyNo : item.ClientCompanyNo;
                        item.SupplierName = _sessionManager.IsPOHub ? item.POHubSupplierName : item.ClientSupplierName;
                        item.SupplierName = _sessionManager.IsPOHub ? item.POHubSupplierName : item.ClientSupplierName;
                        item.IsHub = true;
                     
                        if (item.CurrencyNo != ClientCurrencyId && item.ClientCurrencyNo != null)
                        {
                            currentRateAtDate = (await _mediator.Send(new GetCurrencyRateCurrentAtDateQuery(item.ClientCurrencyNo.Value, (item.OriginalEntryDate.HasValue) ? item.OriginalEntryDate.Value : item.DLUP))).Data ?? 1;
                        } 
                    }
                    else
                    {
                        item.IsHub = false;
                        if (item.CurrencyNo != ClientCurrencyId && item.CurrencyNo != null)
                        {
                            currentRateAtDate = (await _mediator.Send(new GetCurrencyRateCurrentAtDateQuery(item.CurrencyNo.Value, DateTime.Now))).Data ?? 1;
                        }
                    }
                    var priceInBase = Functions.ConvertValueToBaseCurrencyDecimal(item.Price, Convert.ToDecimal(currentRateAtDate));
                    item.FormatedPriceInBase = Functions.FormatCurrency(priceInBase, cultureInfo, ClientCurrencyCode, 5, false);
                    item.FormatedPrice = Functions.FormatCurrency(item.Price, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
                    item.Notes = Functions.ReplaceLineBreaks(item.Notes);

                    if (item.ManufacturerNo != null)
                    {
                        var getRestrictedManufacturer = await _mediator.Send(new GetRestrictedManufacturerQuery()
                        {
                            ClientNo = ClientId,
                            ManufactureNo = item.ManufacturerNo.Value
                        });
                        if (getRestrictedManufacturer.Success && getRestrictedManufacturer.Data != null)
                        {
                            item.IsRestrictedManufacturer = getRestrictedManufacturer.Data.IsRestrictedManufacturer;
                        }
                    }

                    if (item.OfferStatusNo.HasValue)
                    {
                        item.Status = _offerStatusLocalizer.GetString(((UserCases.Commons.Enums.OfferStatus)item.OfferStatusNo).ToString());
                    }
                }
            }
            return Ok(result);
        }
    }
}
