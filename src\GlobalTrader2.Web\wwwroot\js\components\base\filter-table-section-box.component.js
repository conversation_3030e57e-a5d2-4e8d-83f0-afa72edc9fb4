﻿export class FilterTableSectionBox {
    constructor(tableComp, filterComp, sectionBoxComp, options) {
        this.state = {
            tableComp,
            filterComp,
            sectionBoxComp,
            options,
            apiControllers: {},
            processing: [],
            dataListNuggetNo: 0
        }

        this.eventHandlers = [];

        const filterTableSectionBox = this;

        Object.defineProperty(this.state.processing, "push", {
            configurable: true,
            enumerable: false,
            writable: true,
            value: function (...args) {

                if (filterTableSectionBox.state.processing.length == 0) {
                    filterTableSectionBox.trigger("processing.mftsb");
                }


                return Array.prototype.push.apply(this, args);
            }
        });

        Object.defineProperty(this.state.processing, "pop", {
            configurable: true,
            enumerable: false,
            writable: true,
            value: function (...args) {

                const value = Array.prototype.pop.apply(this, args);

                if (filterTableSectionBox.state.processing.length == 0) {
                    filterTableSectionBox.trigger("processed.mftsb");
                }

                return value;
            }
        });
    }

    async initAsync() {
        // please apply debounce

        this.state.sectionBoxComp.init();
        this.state.tableComp.init();
        this.state.filterComp.init();

        this._registerFilterEvents();

        this._registerTableEvents();

        const data = await this.getFilterStates();

        const setObj = Object.values(data.filterDictionary).reduce((acc, item) => {
            acc[item.name] = item;
            return acc;
        }, {});

        this.state.filterComp.setMetaData(setObj);

        this.state.tableComp.setDefaultOrder(data.orderBy, data.sortNameDir);
        this.state.tableComp.setPageIndex(data.index, data.size);
        this.state.tableComp.setPageSize(data.size);
    }

    countAppliedFilters(tabId) {
        const filterInputs = this.state.filterComp.getInputs();
        return Object.values(filterInputs).reduce((count, input) => {
            const isSalesmanOnMyTab = (input.instance?.name === "Salesman" && tabId == 0);
            const isApplied = Boolean(input.inputValue) && input.instance?.isChecked?.();
            if (isSalesmanOnMyTab) return count;
            return count + (isApplied ? 1 : 0);
        }, 0);
    }

    /**
     * Attach an event listener.
     * @param {string} event - The event name.
     * @param {Function} callback - The event handler function.
     */
    on(event, callback) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(callback);
    }

    /**
     * Trigger an event and call all attached handlers.
     * @param {string} event - The event name.
     * @param {any} data - Data to pass to the event handlers.
     */
    trigger(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(callback => callback(data));
        }
    }

    setFilterStateType(type) {
        this.state.filterStateType = type;
    }

    async _reloadTableWhenFilteredAppliedAsync() {
        const filterData = this._getFilter();

        await this.state.tableComp.reloadAsync(filterData);
    }

    async _saveFilterState(data) {
        this.state.apiControllers.saveFilterState = new AbortController();

        this.state.processing.push(true);

        return await GlobalTrader.ApiClient.postAsync(`/data-list-nugget-state`, data, null, {
            abortSignal: this.state.apiControllers.saveFilterState.signal,
            isIgnoreErrorMessage: true
        }).finally(
            () => {
                this.state.processing.pop();
            }
        );
    }

    _getFilter() {
        let filterData = this.state.filterComp.getAppliedValues();

        if (this.state.options?.prepareDataBeforeReload && typeof this.state.options?.prepareDataBeforeReload === "function") {
            filterData = this.state.options.prepareDataBeforeReload(filterData);
        }
        return filterData;
    }

    async getFilterStates() {
        return await GlobalTrader.ApiClient.postAsync(`/data-list-nugget-state/get-or-create`, {
            subType: "",
            dataListNuggetNo: this.state.filterStateType
        }, {});
    }

    _registerTableEvents() {
        this.state.sectionBoxComp.on('onRefreshed.msb', async (event) => {
            const filterData = this._getFilter();
            await this.state.tableComp.reloadAsync(filterData);
        });

        this.state.tableComp.on('preXhr.dt', (e, settings, data) => {

            this.state.processing.push(true);
            this.state.tableComp.abortRequest();
            if (this.state.sectionBoxComp.isLocked() && !this.state.tableComp.getCurrentFilter()._ignoredSave) {
                let filterToSave = {};

                if (this.state.filterComp.isVisible()) {
                    filterToSave = this.state.filterComp.getAllValue();
                }

                this._saveFilterState({ ...{ filterDictionary: filterToSave, dataListNuggetNo: this.state.filterStateType }, ...this.state.tableComp.getPageInfo() });
            }
        })

        this.state.tableComp.on('xhr.dt', () => {
            this.state.processing.pop();
        })

        this.state.tableComp.on('error.mdt.dt', () => {
            this.state.processing.pop();
        })

        this.state.tableComp.on('abort.mdt.dt', () => {
            this.state.processing.pop();
        })

        this.on('processing.mftsb', () => {
            this.state.sectionBoxComp.loading(false);
            this.state.sectionBoxComp.onHideWarningMessage();
            this.state.filterComp.toggleApplyCancelButtons(false);
            this.state.sectionBoxComp.disableButtons(this.getDisableButtonsWhileProcessing())
        })

        this.on('processed.mftsb', () => {
            this.state.sectionBoxComp.stopLoading(false);
            this.state.filterComp.toggleApplyCancelButtons(true);
            this.state.sectionBoxComp.enableButtons(this.getDisableButtonsWhileProcessing())
        })
    }

    getDisableButtonsWhileProcessing() {
        return ['apply', 'reset', 'off', 'lock', 'filter', 'hide', 'show'];
    }

    cancelCurrentProcessing() {
        this.state.tableComp.abortRequest();
        this.state.apiControllers.saveFilterState?.abort();
    }

    _registerFilterEvents() {
        // Listen on the jQuery container element
        this.state.filterComp.on('applied.mtf', async (event) => {
            await this._reloadTableWhenFilteredAppliedAsync();
            if (this.state.autoHideFilter) {
                this.state.filterComp.hide();
            }
        });

        this.state.filterComp.on('off.mtf', async (event) => {
            this.state.filterComp.display(false);
            await this._reloadTableWhenFilteredAppliedAsync()
        });

        this.state.sectionBoxComp.on('filterChanged.msb', async (event) => {

            if (!this.state.filterComp.isVisible()) {
                this.state.filterComp.show();
            } else {
                this.state.filterComp.hide();
            }
            await this._reloadTableWhenFilteredAppliedAsync();
        });

        this.state.filterComp.on('reset.mtf', async (event) => {
            this.state.filterComp.reset();
        });


        this.state.sectionBoxComp.on('lockChanged.msb', async (event) => {
            if (this.state.sectionBoxComp.isLocked()) {
                let filterToSave = {};

                if (this.state.filterComp.isVisible()) {
                    filterToSave = this.state.filterComp.getAllValue();
                }

                this._saveFilterState({ ...{ filterDictionary: filterToSave, dataListNuggetNo: this.state.filterStateType }, ...this.state.tableComp.getPageInfo() });
            }
        });

        this.state.filterComp.on('cancel.mtf', () => {
            this.cancelCurrentProcessing();
            this.state.sectionBoxComp.onShowWarningMessage();
        });

        this.state.filterComp.on('inputEnter.mft', async () => {
            setTimeout(() => {
                this.state.filterComp.toggleApplyCancelButtons(false);
                this.state.filterComp.trigger('applied.mtf');
            }, 0); 
        });
    }

    registerHiddenButtonFilterEvents() {
        this.state.autoHideFilter = true;
        this.state.filterComp.on('show-filter.mtf', async () => {
            this.state.filterComp.show();
        })
    }
}