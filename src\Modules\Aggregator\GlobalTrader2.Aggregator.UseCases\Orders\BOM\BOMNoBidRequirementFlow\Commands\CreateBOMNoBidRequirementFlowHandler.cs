﻿using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyNoBid;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMNoBidRequirement.Commands;

namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMNoBidRequirementFlow.Commands
{
    public class CreateBOMNoBidRequirement<PERSON><PERSON><PERSON><PERSON><PERSON>(IMediator mediator) : I<PERSON>equestHandler<CreateBOMNoBidRequirementFlowCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;

        public async Task<BaseResponse<int>> Handle(CreateBOMNoBidRequirementFlowCommand request, CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(request);

            var response = new BaseResponse<int>();
            var bOMNoBidRequirementRes = await _mediator.Send(new CreateBOMNoBidRequirementCommand
            {
                BomId = request.BomId,
                NoBidNotes = request.Notes,
                UpdatedBy = request.UpdatedBy,
            }, cancellationToken);

            if (bOMNoBidRequirementRes.Success)
            {
                var notifyCommand = new NotifyNoBidCommand
                {
                    BOMId = request.BomId,
                    ToLogins = [.. request.SalesmanNo, request.UpdatedBy],
                    BOMName = request.BOMName!,
                    SenderLoginNo = request.LoginId,
                    SenderName = request.SenderName,
                    SenderEmail = request.LoginEmail,
                    Subject = request.Subject,
                    HUBRFQStatus = request.HUBRFQStatus,
                    Code = $"{request.BOMCode}( {request.ClientName} )",
                    ClientId = request.ClientId,
                    IsPoHub = request.IsPoHub,
                    IsNoBidAll = true,
                    ClientCurrencyCode = request.ClientCurrencyCode,
                    LoginId = request.LoginId,
                    HUBRFQHyperlink = request.HUBRFQHyperLink,
                };

                await _mediator.Send(notifyCommand, cancellationToken);
            }

            response.Success = true;
            return response;
        }
    }
}
