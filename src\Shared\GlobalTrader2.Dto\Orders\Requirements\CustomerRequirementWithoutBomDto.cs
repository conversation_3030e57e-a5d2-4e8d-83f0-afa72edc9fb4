﻿using GlobalTrader2.Dto.Converters.DateTime;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Dto.Orders.Requirements
{
    public class CustomerRequirementWithoutBomDto
    {
        public int CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }
        public string? CompanyName { get; set; } = string.Empty;
        public string? Part { get; set; } = string.Empty;
        public byte? ROHS { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? ReceivedDate { get; set; }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public string? FormatedPrice { get; set; }
        public string? CurrencyCode { get; set; }
        public int RowCnt { get; set; }
        public int? BOMNo { get; set; }
        public string? BOMName { get; set; }
        public long RowNum { get; set; }
    }
}
