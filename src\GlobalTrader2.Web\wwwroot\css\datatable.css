﻿.dt-container {
    font-size: 12px;
}

.dt-length label {
    padding-left: 3px;
}

.dt-paging {
    font-size: 12px;
}
.quickbrowse-section-table .simple-table.dataTable tbody tr td {
    font-size: 11px;
}

.quickbrowse-section-table .dt-layout-row:has(.dt-paging) {
    margin: 0
}

.quickbrowse-section-table .dt-layout-row:has(.dt-info) {
    margin: 0
}

.quickbrowse-section-table .dt-layout-table {
    margin-bottom: 0px !important;
}


.dt-paging .dt-paging-button.current {
    color: white !important;
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%) !important;
    border: 1px solid rgba(50, 126, 4, 1) !important;
}
        .dt-paging .dt-paging-button.current:hover {
            color: white !important;
            background: linear-gradient(180deg, #80C24D 0%, #459E00 100%) !important;
            border: 1px solid rgba(50, 126, 4, 1) !important;
        }

    .dt-paging .dt-paging-button:hover {
        color: black !important;
        background: #CDF2C7 !important;
        border: 1px solid rgba(50, 126, 4, 1) !important;
    }

table.dataTable > tbody > tr.selected > * {
    box-shadow: none !important;
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
}

table.dataTable.hover > tbody > tr.selected:hover > *, table.dataTable.display > tbody > tr.selected:hover > * {
    box-shadow: none !important;
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
}

table.dataTable.stripe > tbody > tr:nth-child(odd).selected > *, table.dataTable.display > tbody > tr:nth-child(odd).selected > * {
    box-shadow: none !important;
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
}

table.dataTable > tbody > tr > .selected {
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
    color: white;
}

.reminder {
    position: absolute;
    height: 15px !important;
    left: 10px;
}

.dt-hyper-link {
    text-decoration: none;
    color: #0d6efd !important;
}

    .dt-hyper-link:hover {
        text-decoration: underline;
    }

.link-align-left {
    margin-left: 0px;
    margin-right: 15px;
}

table.dataTable > tbody > tr.selected a {
    color: #0d6efd;
}

.dt-column-order.dt-column-order-custom {
    height: fit-content;
    top: 100px;
}

.dt-column-order.dt-column-order-custom::after, .dt-column-order.dt-column-order-custom::before {
    top: 10px !important;
}

table.dataTable thead > tr > th.dt-ordering-asc.header-custom span.dt-column-order:before,
table.dataTable thead > tr > th.dt-ordering-desc.header-custom span.dt-column-order:after {
    top: 20%
}

.is-hub {
    color: red;
}

.view-task {
    color: red !important;
}

div.dts tbody td {
    white-space: unset !important;
}

div.dts div.dt-scroll-body table, div.dts div.dataTables_scrollBody table {
    background-color: transparent !important;
}

div.dts div.dt-scroll-body, div.dts div.dataTables_scrollBody {
    background: transparent !important;
}

.dt-scroll-headInner {
    width: 100% !important;
    box-sizing: border-box;
}

.dts_label {
    display: none !important;
}

div.dt-container.dt-empty-footer .dt-scroll-body {
    border-bottom: unset !important;
}


div.dt-buttons .dt-button {
    height: 22px !important;
    font-size: 12px !important;
    display: flex !important;
    align-items: center !important;
    padding: 6px !important;
    gap: 6px !important;
    color: #fff !important;
    border-radius: 6px !important;
    background: var(--btn-primary-color) !important;
    border: 1px solid rgba(50, 126, 4, 1) !important;

    
}

    div.dt-buttons .dt-button:focus {
        box-shadow: 0 0 0 0.25rem var(--btn-primary-focus-shadow) !important;
        border: 1px solid rgba(50, 126, 4, 1) !important;
        color: white;
        outline: none !important;
    }