﻿using Azure.Storage.Blobs;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Domain.Entities.Imports;
using GlobalTrader2.Core.Infrastructure.Clients;
using GlobalTrader2.Core.Infrastructure.Persistence;
using GlobalTrader2.Core.Infrastructure.Repositories;
using GlobalTrader2.Core.Infrastructure.Services;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Models;
using GlobalTrader2.Core.Models.IHS;
using GlobalTrader2.Core.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using CoreConstants = GlobalTrader2.Core.Constants;

namespace GlobalTrader2.Core.Infrastructure
{
    public static class ConfigureServices
    {
        public static IServiceCollection AddInjectionInfrastructure(this IServiceCollection services, IConfiguration config)
        {
            var mainDbConnectionString = config.GetConnectionString("MainDb")
                ?? throw new InvalidOperationException("Connection string 'MainDb' not found.");
            var importDbConnectionString = config.GetConnectionString("ImportDb")
                ?? throw new InvalidOperationException("Connection string 'ImportDb' not found.");

            int maxRetryCount = int.TryParse(config["RetryPolicy:MaxRetryCount"], out int parsedMaxRetryCount) ? parsedMaxRetryCount : CoreConstants.DefaultAppSettings.MaxRetryCount;
            int maxRetryDelay = int.TryParse(config["RetryPolicy:MaxRetryDelay"], out int parsedMaxRetryDelay) ? parsedMaxRetryDelay : CoreConstants.DefaultAppSettings.MaxRetryDelayInSeconds;

            services.AddDbContext<AppDbContext>(options => options.UseSqlServer(
                mainDbConnectionString,
                sqlServerOptionsAction: sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: maxRetryCount,
                        maxRetryDelay: TimeSpan.FromSeconds(maxRetryDelay),
                        errorNumbersToAdd: null
                    );
                })
            );

            services.AddDbContext<ImportDbContext>(options => options.UseSqlServer(
                importDbConnectionString,
                sqlServerOptionsAction: sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: maxRetryCount,
                        maxRetryDelay: TimeSpan.FromSeconds(maxRetryDelay),
                        errorNumbersToAdd: null
                    );
                })
            );

            services.AddSingleton<IQueryableAsyncService, QueryableAsyncService>();
            services.AddHttpClient<ApiClientFactory>();
            services.RegisterBlobStorageService(config);
            services.RegisterIHSService();
            services.RegisterRepositoryDependencies();
            services.RegisterEmailService(config);
            services.RegisterFEService();
            services.RegisterDigiKeyService();
            services.RegisterLyticaService();
            services.RegisterPowerAutomateService();
            services.RegisterPdfService(config);
            services.RegisterFileDataReaderServices();
            return services;
        }

        private static void RegisterRepositoryDependencies(this IServiceCollection services)
        {
            services.AddScoped<IBaseRepository<AS6081TypeOfSupplier>, BaseRepository<AS6081TypeOfSupplier>>();
            services.AddScoped<IBaseRepository<AS6081RiskOfSupplier>, BaseRepository<AS6081RiskOfSupplier>>();
            services.AddScoped<IBaseRepository<AS6081ReasonForChosenSupplier>, BaseRepository<AS6081ReasonForChosenSupplier>>();
            services.AddScoped<IBaseRepository<CompanyDemo>, BaseRepository<CompanyDemo>>();
            services.AddScoped<IBaseRepository<TermsSelect>, BaseRepository<TermsSelect>>();
            services.AddScoped<IBaseRepository<Terms>, BaseRepository<Terms>>();
            services.AddScoped<IBaseRepository<TermsWarningMessage>, BaseRepository<TermsWarningMessage>>();
            services.AddScoped<IBaseRepository<MasterLoginResult>, BaseRepository<MasterLoginResult>>();
            services.AddScoped<IBaseRepository<LoginDetails>, BaseRepository<LoginDetails>>();
            services.AddScoped<IBaseRepository<LoginPreferenceDetails>, BaseRepository<LoginPreferenceDetails>>();
            services.AddScoped<IBaseRepository<MasterLoginClientName>, BaseRepository<MasterLoginClientName>>();
            services.AddScoped<IBaseRepository<RecentlyViewedDetail>, BaseRepository<RecentlyViewedDetail>>();
            services.AddScoped<IBaseRepository<FunctionSecurityDetail>, BaseRepository<FunctionSecurityDetail>>();
            services.AddScoped<IBaseRepository<FunctionSecurityPermission>, BaseRepository<FunctionSecurityPermission>>();
            services.AddScoped<IBaseRepository<AffectedRows>, BaseRepository<AffectedRows>>();
            services.AddScoped<IBaseRepository<SecurityUser>, BaseRepository<SecurityUser>>();
            services.AddScoped<IBaseRepository<DivisionDropdown>, BaseRepository<DivisionDropdown>>();
            services.AddScoped<IBaseRepository<SitePageInfo>, BaseRepository<SitePageInfo>>();
            services.AddScoped<IBaseRepository<Team>, BaseRepository<Team>>();
            services.AddScoped<IBaseRepository<Printer>, BaseRepository<Printer>>();
            services.AddScoped<IBaseRepository<PrinterDropdown>, BaseRepository<PrinterDropdown>>();
            services.AddScoped<IBaseRepository<SecurityUserProfile>, BaseRepository<SecurityUserProfile>>();
            services.AddScoped<IBaseRepository<SecurityUserGroup>, BaseRepository<SecurityUserGroup>>();
            services.AddScoped<IBaseRepository<RecentlyViewed>, BaseRepository<RecentlyViewed>>();
            services.AddScoped<IBaseRepository<LoginAccount>, BaseRepository<LoginAccount>>();
            services.AddScoped<IBaseRepository<object>, BaseRepository<object>>();
            services.AddScoped<IBaseRepository<SecurityGroup>, BaseRepository<SecurityGroup>>();
            services.AddScoped<IBaseRepository<SecurityGroupReadModel>, BaseRepository<SecurityGroupReadModel>>();
            services.AddScoped<IBaseRepository<SettingItemDetail>, BaseRepository<SettingItemDetail>>();
            services.AddScoped<IBaseRepository<Client>, BaseRepository<Client>>();
            services.AddScoped<IBaseRepository<Login>, BaseRepository<Login>>();
            services.AddScoped<IBaseRepository<TeamDetails>, BaseRepository<TeamDetails>>();
            services.AddScoped<IBaseRepository<TeamMember>, BaseRepository<TeamMember>>();
            services.AddScoped<IBaseRepository<InsertedSecurityGroup>, BaseRepository<InsertedSecurityGroup>>();
            services.AddScoped<IBaseRepository<SitePageDetails>, BaseRepository<SitePageDetails>>();
            services.AddScoped<IBaseRepository<ListPermissionGeneral>, BaseRepository<ListPermissionGeneral>>();
            services.AddScoped<IBaseRepository<FunctionSecurityLevel>, BaseRepository<FunctionSecurityLevel>>();
            services.AddScoped<IBaseRepository<SecurityFunctionSection>, BaseRepository<SecurityFunctionSection>>();
            services.AddScoped<IBaseRepository<SecurityGroupLoginReadModel>, BaseRepository<SecurityGroupLoginReadModel>>();
            services.AddScoped<IBaseRepository<TeamSelect>, BaseRepository<TeamSelect>>();
            services.AddScoped<IBaseRepository<UpdateSettingItem>, BaseRepository<UpdateSettingItem>>();
            services.AddScoped<IBaseRepository<LocalCurrency>, BaseRepository<LocalCurrency>>();
            services.AddScoped<IBaseRepository<GlobalCurrencyList>, BaseRepository<GlobalCurrencyList>>();
            services.AddScoped<IBaseRepository<CurrencyRate>, BaseRepository<CurrencyRate>>();
            services.AddScoped<IBaseRepository<WarehouseDetail>, BaseRepository<WarehouseDetail>>();
            services.AddScoped<IBaseRepository<SecurityFunctionReport>, BaseRepository<SecurityFunctionReport>>();
            services.AddScoped<IBaseRepository<TabSecurityFunctionDetail>, BaseRepository<TabSecurityFunctionDetail>>();
            services.AddScoped<IBaseRepository<Address>, BaseRepository<Address>>();
            services.AddScoped<IBaseRepository<AddressSelect>, BaseRepository<AddressSelect>>();
            services.AddScoped<IBaseRepository<ProductStatusMessage>, BaseRepository<ProductStatusMessage>>();
            services.AddScoped<IBaseRepository<HazardousNote>, BaseRepository<HazardousNote>>();
            services.AddScoped<IBaseRepository<Warehouse>, BaseRepository<Warehouse>>();
            services.AddScoped<IBaseRepository<WarehouseSelect>, BaseRepository<WarehouseSelect>>();
            services.AddScoped<IBaseRepository<CountryDropdown>, BaseRepository<CountryDropdown>>();
            services.AddScoped<IBaseRepository<LocalCurrencyDropdown>, BaseRepository<LocalCurrencyDropdown>>();
            services.AddScoped<IBaseRepository<EmailComposerDetail>, BaseRepository<EmailComposerDetail>>();
            services.AddScoped<IBaseRepository<CommunicationLogType>, BaseRepository<CommunicationLogType>>();
            services.AddScoped<IBaseRepository<GlobalCurrencyListDetails>, BaseRepository<GlobalCurrencyListDetails>>();
            services.AddScoped<IBaseRepository<MasterCountry>, BaseRepository<MasterCountry>>();
            services.AddScoped<IBaseRepository<MasterAppSetting>, BaseRepository<MasterAppSetting>>();
            services.AddScoped<IBaseRepository<MasterAppSettingItem>, BaseRepository<MasterAppSettingItem>>();
            services.AddScoped<IBaseRepository<CompanyType>, BaseRepository<CompanyType>>();
            services.AddScoped<IBaseRepository<SecurityGroupLogin>, BaseRepository<SecurityGroupLogin>>();
            services.AddScoped<IBaseRepository<SecurityLoginPermissionReadModel>, BaseRepository<SecurityLoginPermissionReadModel>>();
            services.AddScoped<IBaseRepository<GlobalSecurityLoginPermissionReadModel>, BaseRepository<GlobalSecurityLoginPermissionReadModel>>();
            services.AddScoped<IBaseRepository<IndustryType>, BaseRepository<IndustryType>>();
            services.AddScoped<IBaseRepository<SequenceNumber>, BaseRepository<SequenceNumber>>();
            services.AddScoped<IBaseRepository<CountingMethod>, BaseRepository<CountingMethod>>();
            services.AddScoped<IBaseRepository<ActiveClientReadModel>, BaseRepository<ActiveClientReadModel>>();
            services.AddScoped<IBaseRepository<TabSecurityGroupSecurityFunctionPermission>, BaseRepository<TabSecurityGroupSecurityFunctionPermission>>();
            services.AddScoped<IBaseRepository<WarningMessageDetail>, BaseRepository<WarningMessageDetail>>();
            services.AddScoped<IBaseRepository<WarningMessageDetailForClient>, BaseRepository<WarningMessageDetailForClient>>();
            services.AddScoped<IBaseRepository<WarningMessage>, BaseRepository<WarningMessage>>();
            services.AddScoped<IBaseRepository<SystemWarningMessage>, BaseRepository<SystemWarningMessage>>();
            services.AddScoped<IBaseRepository<Incoterm>, BaseRepository<Incoterm>>();
            services.AddScoped<IBaseRepository<Package>, BaseRepository<Package>>();
            services.AddScoped<IBaseRepository<MasterStatus>, BaseRepository<MasterStatus>>();
            services.AddScoped<IBaseRepository<StatusReason>, BaseRepository<StatusReason>>();
            services.AddScoped<IBaseRepository<SecurityGroupSecurityFunctionPermission>, BaseRepository<SecurityGroupSecurityFunctionPermission>>();
            services.AddScoped<IBaseRepository<HubrfqPvvQuestion>, BaseRepository<HubrfqPvvQuestion>>();
            services.AddScoped<IBaseRepository<Sequencer>, BaseRepository<Sequencer>>();
            services.AddScoped<IBaseRepository<BomAssignmentHistoryModel>, BaseRepository<BomAssignmentHistoryModel>>();
            services.AddScoped<IBaseRepository<CompanyAutoSearch>, BaseRepository<CompanyAutoSearch>>();
            services.AddScoped<IBaseRepository<ProductAutoSearch>, BaseRepository<ProductAutoSearch>>();
            services.AddScoped<IBaseRepository<ManufacturerAutoSearch>, BaseRepository<ManufacturerAutoSearch>>();
            services.AddScoped<IBaseRepository<LyticaManufacturerAutoSearch>, BaseRepository<LyticaManufacturerAutoSearch>>();
            services.AddScoped<IBaseRepository<CountryAutoSearch>, BaseRepository<CountryAutoSearch>>();
            services.AddScoped<IBaseRepository<Currency>, BaseRepository<Currency>>();
            services.AddScoped<IBaseRepository<MailGroup>, BaseRepository<MailGroup>>();
            services.AddScoped<IBaseRepository<MailGroupMember>, BaseRepository<MailGroupMember>>();
            services.AddScoped<IBaseRepository<OGELLicense>, BaseRepository<OGELLicense>>();
            services.AddScoped<IBaseRepository<ApplicationSetting>, BaseRepository<ApplicationSetting>>();
            services.AddScoped<IBaseRepository<RestrictedManufacturerDetail>, BaseRepository<RestrictedManufacturerDetail>>();
            services.AddScoped<IBaseRepository<CompanyDisable>, BaseRepository<CompanyDisable>>();
            services.AddScoped<IBaseRepository<Reason>, BaseRepository<Reason>>();
            services.AddScoped<IBaseRepository<Session>, BaseRepository<Session>>();
            services.AddScoped<IBaseRepository<ClientInvoiceHeader>, BaseRepository<ClientInvoiceHeader>>();
            services.AddScoped<IBaseRepository<RestrictedManufacturerCount>, BaseRepository<RestrictedManufacturerCount>>();
            services.AddScoped<IBaseRepository<ClientInvoiceHeader>, BaseRepository<ClientInvoiceHeader>>();
            services.AddScoped<IBaseRepository<DocumentSize>, BaseRepository<DocumentSize>>();
            services.AddScoped<IBaseRepository<CertificateCategory>, BaseRepository<CertificateCategory>>();
            services.AddScoped<IBaseRepository<Certificate>, BaseRepository<Certificate>>();
            services.AddScoped<IBaseRepository<RestrictedManufacturer>, BaseRepository<RestrictedManufacturer>>();
            services.AddScoped<IBaseRepository<Country>, BaseRepository<Country>>();
            services.AddScoped<IBaseRepository<Tax>, BaseRepository<Tax>>();
            services.AddScoped<IBaseRepository<DivisionForClient>, BaseRepository<DivisionForClient>>();
            services.AddScoped<IBaseRepository<ToDoListTaskReadModel>, BaseRepository<ToDoListTaskReadModel>>();
            services.AddScoped<IBaseRepository<DivisionMember>, BaseRepository<DivisionMember>>();
            services.AddScoped<IBaseRepository<ToDoListType>, BaseRepository<ToDoListType>>();
            services.AddScoped<IBaseRepository<MasterLoginDetails>, BaseRepository<MasterLoginDetails>>();
            services.AddScoped<IBaseRepository<MasterLogin>, BaseRepository<MasterLogin>>();
            services.AddScoped<IBaseRepository<Division>, BaseRepository<Division>>();
            services.AddScoped<IBaseRepository<AddressDetail>, BaseRepository<AddressDetail>>();
            services.AddScoped<IBaseRepository<RootCauseCategory>, BaseRepository<RootCauseCategory>>();
            services.AddScoped<IBaseRepository<RootCauseSubCategory>, BaseRepository<RootCauseSubCategory>>();
            services.AddScoped<IBaseRepository<AuditSetting>, BaseRepository<AuditSetting>>();
            services.AddScoped<IBaseRepository<DataListNuggetState>, BaseRepository<DataListNuggetState>>();
            services.AddScoped<IBaseRepository<Todo>, BaseRepository<Todo>>();
            services.AddScoped<IBaseRepository<LoginPreference>, BaseRepository<LoginPreference>>();
            services.AddScoped<IBaseRepository<GlobalTax>, BaseRepository<GlobalTax>>();
            services.AddScoped<IBaseRepository<GlobalTaxRate>, BaseRepository<GlobalTaxRate>>();
            services.AddScoped<IBaseRepository<ToDoItem>, BaseRepository<ToDoItem>>();
            services.AddScoped<IBaseRepository<ToDoItemDetail>, BaseRepository<ToDoItemDetail>>();
            services.AddScoped<IBaseRepository<MailMessageFolder>, BaseRepository<MailMessageFolder>>();
            services.AddScoped<IBaseRepository<MailMessageDetailsReadModel>, BaseRepository<MailMessageDetailsReadModel>>();
            services.AddScoped<IBaseRepository<CountItem>, BaseRepository<CountItem>>();
            services.AddScoped<IBaseRepository<StockLogReason>, BaseRepository<StockLogReason>>();
            services.AddScoped<IBaseRepository<ProductForClient>, BaseRepository<ProductForClient>>();
            services.AddScoped<IBaseRepository<DutyRate>, BaseRepository<DutyRate>>();
            services.AddScoped<IBaseRepository<ClientSetup>, BaseRepository<ClientSetup>>();
            services.AddScoped<IBaseRepository<AutoSearchMailMessageTo>, BaseRepository<AutoSearchMailMessageTo>>();
            services.AddScoped<IBaseRepository<MailMessage>, BaseRepository<MailMessage>>();
            services.AddScoped<IBaseRepository<SourcingLink>, BaseRepository<SourcingLink>>();
            services.AddScoped<IBaseRepository<GlobalProductGroup>, BaseRepository<GlobalProductGroup>>();
            services.AddScoped<IBaseRepository<GlobalProduct>, BaseRepository<GlobalProduct>>();
            services.AddScoped<IBaseRepository<GlobalProductCategory>, BaseRepository<GlobalProductCategory>>();
            services.AddScoped<IBaseRepository<DutyRate>, BaseRepository<DutyRate>>();
            services.AddScoped<IBaseRepository<Product>, BaseRepository<Product>>();
            services.AddScoped<IBaseRepository<ShipVia>, BaseRepository<ShipVia>>();
            services.AddScoped<IBaseRepository<ShipViaDetail>, BaseRepository<ShipViaDetail>>();
            services.AddScoped<IBaseRepository<Eccn>, BaseRepository<Eccn>>();
            services.AddScoped<IBaseRepository<CountryHeaderMapping>, BaseRepository<CountryHeaderMapping>>();
            services.AddScoped<IBaseRepository<TbTeam>, BaseRepository<TbTeam>>();
            services.AddScoped<IBaseRepository<ToDoCategory>, BaseRepository<ToDoCategory>>();
            services.AddScoped<IBaseRepository<LocalCurrencyRateDetails>, BaseRepository<LocalCurrencyRateDetails>>();
            services.AddScoped<IBaseRepository<MailMessagesNewReadModel>, BaseRepository<MailMessagesNewReadModel>>();
            services.AddScoped<IBaseRepository<StarRating>, BaseRepository<StarRating>>();
            services.AddScoped<IBaseRepository<DocumentFooterMessagesForClient>, BaseRepository<DocumentFooterMessagesForClient>>();
            services.AddScoped<IBaseRepository<ClientDetail>, BaseRepository<ClientDetail>>();
            services.AddScoped<IBaseRepository<EntertainmentType>, BaseRepository<EntertainmentType>>();
            services.AddScoped<IBaseRepository<TaxRate>, BaseRepository<TaxRate>>();
            services.AddScoped<IBaseRepository<SystemDocumentFooter>, BaseRepository<SystemDocumentFooter>>();
            services.AddScoped<IBaseRepository<Quote>, BaseRepository<Quote>>();
            services.AddScoped<IBaseRepository<QuoteReadModel>, BaseRepository<QuoteReadModel>>();
            services.AddScoped<IBaseRepository<CompanyListReadModel>, BaseRepository<CompanyListReadModel>>();
            services.AddScoped<IBaseRepository<ContactReadModel>, BaseRepository<ContactReadModel>>();
            services.AddScoped<IBaseRepository<TabSecurityFunctionPermissionReadModel>, BaseRepository<TabSecurityFunctionPermissionReadModel>>();
            services.AddScoped<IBaseRepository<ContactForPageReadModel>, BaseRepository<ContactForPageReadModel>>();
            services.AddScoped<IBaseRepository<Company>, BaseRepository<Company>>();
            services.AddScoped<IBaseRepository<CompanyForPageReadModel>, BaseRepository<CompanyForPageReadModel>>();
            services.AddScoped<IBaseRepository<ContactListReadModel>, BaseRepository<ContactListReadModel>>();
            services.AddScoped<IBaseRepository<Contact>, BaseRepository<Contact>>();
            services.AddScoped<IBaseRepository<CompanyAddress>, BaseRepository<CompanyAddress>>();
            services.AddScoped<IBaseRepository<CustomerRequirement>, BaseRepository<CustomerRequirement>>();
            services.AddScoped<IBaseRepository<InsertedContactReadModel>, BaseRepository<InsertedContactReadModel>>();
            services.AddScoped<IBaseRepository<RequirementDropDownData>, BaseRepository<RequirementDropDownData>>();
            services.AddScoped<IBaseRepository<Usage>, BaseRepository<Usage>>();
            services.AddScoped<IBaseRepository<CompanySearchItem>, BaseRepository<CompanySearchItem>>();
            services.AddScoped<IBaseRepository<ManufacturerCheckRestricted>, BaseRepository<ManufacturerCheckRestricted>>();
            services.AddScoped<IBaseRepository<StockDetails>, BaseRepository<StockDetails>>();
            services.AddScoped<IBaseRepository<StockInfoDetails>, BaseRepository<StockInfoDetails>>();
            services.AddScoped<IBaseRepository<AlternativePartDetails>, BaseRepository<AlternativePartDetails>>();
            services.AddScoped<IBaseRepository<AlternativePart>, BaseRepository<AlternativePart>>();
            services.AddScoped<IBaseRepository<ManufacturerLinkReadModel>, BaseRepository<ManufacturerLinkReadModel>>();
            services.AddScoped<IBaseRepository<ManufacturerDataListReadModel>, BaseRepository<ManufacturerDataListReadModel>>();
            services.AddScoped<IBaseRepository<PartNoAutoSearch>, BaseRepository<PartNoAutoSearch>>();
            services.AddScoped<IBaseRepository<CommunicationLog>, BaseRepository<CommunicationLog>>();
            services.AddScoped<IBaseRepository<ManufacturerLink>, BaseRepository<ManufacturerLink>>();
            services.AddScoped<IBaseRepository<AutoSearchCompanyForSuppliersReadModel>, BaseRepository<AutoSearchCompanyForSuppliersReadModel>>();
            services.AddScoped<IBaseRepository<ManufacturerMainInfo>, BaseRepository<ManufacturerMainInfo>>();
            services.AddScoped<IBaseRepository<CommunicationLogDetailsReadModel>, BaseRepository<CommunicationLogDetailsReadModel>>();
            services.AddScoped<IBaseRepository<SecurityFunctionEntity>, BaseRepository<SecurityFunctionEntity>>();
            services.AddScoped<IBaseRepository<TabSecurityFunction>, BaseRepository<TabSecurityFunction>>();
            services.AddScoped<IBaseRepository<RohsStatus>, BaseRepository<RohsStatus>>();
            services.AddScoped<IBaseRepository<Manufacturer>, BaseRepository<Manufacturer>>();
            services.AddScoped<IBaseRepository<MSLLevel>, BaseRepository<MSLLevel>>();
            services.AddScoped<IBaseRepository<BOM>, BaseRepository<BOM>>();
            services.AddScoped<IBaseRepository<BOMDetailsModel>, BaseRepository<BOMDetailsModel>>();
            services.AddScoped<IBaseRepository<REQStatus>, BaseRepository<REQStatus>>();
            services.AddScoped<IBaseRepository<StockInfo>, BaseRepository<StockInfo>>();
            services.AddScoped<IBaseRepository<SalesInfo>, BaseRepository<SalesInfo>>();
            services.AddScoped<IBaseRepository<ManufacturerPdf>, BaseRepository<ManufacturerPdf>>();
            services.AddScoped<IBaseRepository<SalesOrder>, BaseRepository<SalesOrder>>();
            services.AddScoped<IBaseRepository<SalesOrderForTodoTaskReadModel>, BaseRepository<SalesOrderForTodoTaskReadModel>>();
            services.AddScoped<IBaseRepository<Soihseccn>, BaseRepository<Soihseccn>>();
            services.AddScoped<IBaseRepository<ExcessDetails>, BaseRepository<ExcessDetails>>();
            services.AddScoped<IBaseRepository<CustomerRequirementDetail>, BaseRepository<CustomerRequirementDetail>>();
            services.AddScoped<IBaseRepository<CustomerRequirementDetailsModel>, BaseRepository<CustomerRequirementDetailsModel>>();
            services.AddScoped<IBaseRepository<CustomerRequirementDetailItem>, BaseRepository<CustomerRequirementDetailItem>>();
            services.AddScoped<IBaseRepository<RequirementPartDetail>, BaseRepository<RequirementPartDetail>>();
            services.AddScoped<IBaseRepository<AdvisoryNote>, BaseRepository<AdvisoryNote>>();
            services.AddScoped<IBaseRepository<CurrentAtDate>, BaseRepository<CurrentAtDate>>();
            services.AddScoped<IBaseRepository<SettingItemReadModel>, BaseRepository<SettingItemReadModel>>();
            services.AddScoped<IBaseRepository<ManuFacturerExcel>, BaseRepository<ManuFacturerExcel>>();
            services.AddScoped<IBaseRepository<ContactGroup>, BaseRepository<ContactGroup>>();
            services.AddScoped<IBaseRepository<ExcessDetailReadModel>, BaseRepository<ExcessDetailReadModel>>();
            services.AddScoped<IBaseRepository<OfferStatus>, BaseRepository<OfferStatus>>();
            services.AddScoped<IBaseRepository<ApiUrlKey>, BaseRepository<ApiUrlKey>>();
            services.AddScoped<IBaseRepository<SupplierApiInsertData>, BaseRepository<SupplierApiInsertData>>();
            services.AddScoped<IBaseRepository<Domain.Entities.PartDetail>, BaseRepository<Domain.Entities.PartDetail>>();
            services.AddScoped<IBaseRepository<IhsPartDetail>, BaseRepository<IhsPartDetail>>();
            services.AddScoped<IBaseRepository<IhsPartSearchResult>, BaseRepository<IhsPartSearchResult>>();
            services.AddScoped<IBaseRepository<CustomerRequirementMainInfo>, BaseRepository<CustomerRequirementMainInfo>>();
            services.AddScoped<IBaseRepository<ECCNMapPartNo>, BaseRepository<ECCNMapPartNo>>();

            services.AddScoped<IBaseRepository<KubAssistanceDetailsCache>, BaseRepository<KubAssistanceDetailsCache>>();
            services.AddScoped<IBaseRepository<KubAveragePriceDetail>, BaseRepository<KubAveragePriceDetail>>();
            services.AddScoped<IBaseRepository<KubTotalLineInvoiceDetail>, BaseRepository<KubTotalLineInvoiceDetail>>();
            services.AddScoped<IBaseRepository<KubQuoteDetail>, BaseRepository<KubQuoteDetail>>();
            services.AddScoped<IBaseRepository<KubMainProductGroupDetail>, BaseRepository<KubMainProductGroupDetail>>();
            services.AddScoped<IBaseRepository<KubCountryWiseSaleDetail>, BaseRepository<KubCountryWiseSaleDetail>>();
            services.AddScoped<IBaseRepository<KubPoDetailsCache>, BaseRepository<KubPoDetailsCache>>();
            services.AddScoped<IBaseRepository<KubGpCalculationDetail>, BaseRepository<KubGpCalculationDetail>>();
            services.AddScoped<IBaseRepository<KubBomQuote>, BaseRepository<KubBomQuote>>();
            services.AddScoped<IBaseRepository<KubBomCustomerRequirement>, BaseRepository<KubBomCustomerRequirement>>();
            services.AddScoped<IBaseRepository<KubBomBuyPriceDetails>, BaseRepository<KubBomBuyPriceDetails>>();
            services.AddScoped<IBomPartStockRepository<KubBomStockDetails>, BomPartStockRepository>();

            services.AddScoped<IBaseRepository<ManufacturerAdvisoryNote>, BaseRepository<ManufacturerAdvisoryNote>>();
            services.AddScoped<IBaseRepository<OfferDetailReadModel>, BaseRepository<OfferDetailReadModel>>();
            services.AddScoped<IBaseRepository<CompanyAddressReadModel>, BaseRepository<CompanyAddressReadModel>>();
            services.AddScoped<IBaseRepository<Region>, BaseRepository<Region>>();
            services.AddScoped<IBaseRepository<GlobalSalesPerson>, BaseRepository<GlobalSalesPerson>>();
            services.AddScoped<IBaseRepository<MailGroupLite>, BaseRepository<MailGroupLite>>();
            services.AddScoped<IBaseRepository<CreditLimitLog>, BaseRepository<CreditLimitLog>>();
            services.AddScoped<IBaseRepository<CompanyMainInfoReadModel>, BaseRepository<CompanyMainInfoReadModel>>();
            services.AddScoped<IBaseRepository<PartMatchInfo>, BaseRepository<PartMatchInfo>>();
            services.AddScoped<IBaseRepository<CompanyIndustryType>, BaseRepository<CompanyIndustryType>>();
            services.AddScoped<IBaseRepository<OfferDetails>, BaseRepository<OfferDetails>>();
            services.AddScoped<IBaseRepository<OrderCustomerRequirement>, BaseRepository<OrderCustomerRequirement>>();
            services.AddScoped<IBaseRepository<CreditLimit>, BaseRepository<CreditLimit>>();
            services.AddScoped<IBaseRepository<PrintDocumentLog>, BaseRepository<PrintDocumentLog>>();
            services.AddScoped<IBaseRepository<CustomerRequirementECCNNotifyInfo>, BaseRepository<CustomerRequirementECCNNotifyInfo>>();
            services.AddScoped<IBaseRepository<CustomerRequirementSourcingResult>, BaseRepository<CustomerRequirementSourcingResult>>();
            services.AddScoped<IBaseRepository<SourcingResultQuote>, BaseRepository<SourcingResultQuote>>();
            services.AddScoped<IBaseRepository<CompanyProspectsModel>, BaseRepository<CompanyProspectsModel>>();
            services.AddScoped<IBaseRepository<CompanyPdf>, BaseRepository<CompanyPdf>>();
            services.AddScoped<IBaseRepository<LabelType>, BaseRepository<LabelType>>();
            services.AddScoped<IBaseRepository<CompanyAddressDetailReadModel>, BaseRepository<CompanyAddressDetailReadModel>>();
            services.AddScoped<IBaseRepository<PowerAppUrl>, BaseRepository<PowerAppUrl>>();
            services.AddScoped<IBaseRepository<ManufacturerLinkForSupplierReadModel>, BaseRepository<ManufacturerLinkForSupplierReadModel>>();
            services.AddScoped<IBaseRepository<BuyForClientAndGlobal>, BaseRepository<BuyForClientAndGlobal>>();
            services.AddScoped<IBaseRepository<EpoReadModel>, BaseRepository<EpoReadModel>>();
            services.AddScoped<IBaseRepository<StrategicOfferDetail>, BaseRepository<StrategicOfferDetail>>();
            services.AddScoped<IBaseRepository<CommonDropdownReadModel>, BaseRepository<CommonDropdownReadModel>>();
            services.AddScoped<IBaseRepository<SourcingAuditLogDetails>, BaseRepository<SourcingAuditLogDetails>>();
            services.AddScoped<IBaseRepository<CertificatesReadModel>, BaseRepository<CertificatesReadModel>>();
            services.AddScoped<IBaseRepository<GlobalSalesPersonName>, BaseRepository<GlobalSalesPersonName>>();
            services.AddScoped<IBaseRepository<ReverseLogisticsReadModel>, BaseRepository<ReverseLogisticsReadModel>>();
            services.AddScoped<IBaseRepository<CompanyCertificate>, BaseRepository<CompanyCertificate>>();
            services.AddScoped<IBaseRepository<ReverseLogisticDetails>, BaseRepository<ReverseLogisticDetails>>();
            services.AddScoped<IBaseRepository<IpoReadModel>, BaseRepository<IpoReadModel>>();
            services.AddScoped<IBaseRepository<QuoteLineReadModel>, BaseRepository<QuoteLineReadModel>>();
            services.AddScoped<IBaseRepository<PurchaseOrderLineReadModel>, BaseRepository<PurchaseOrderLineReadModel>>();

            services.AddScoped<IBaseRepository<LinkedCompanyModel>, BaseRepository<LinkedCompanyModel>>();
            services.AddScoped<IBaseRepository<CurrencyDetailModel>, BaseRepository<CurrencyDetailModel>>();
            services.AddScoped<IBaseRepository<SummariseCompanyLastYearSalesOrderValueModel>, BaseRepository<SummariseCompanyLastYearSalesOrderValueModel>>();
            services.AddScoped<IBaseRepository<SummariseCompanyThisYearSalesOrderValueModel>, BaseRepository<SummariseCompanyThisYearSalesOrderValueModel>>();
            services.AddScoped<IBaseRepository<SummariseCompanyThisYearLastYearSalesValueModel>, BaseRepository<SummariseCompanyThisYearLastYearSalesValueModel>>();
            services.AddScoped<IBaseRepository<SalesOrderOpenLineSummaryValuesModel>, BaseRepository<SalesOrderOpenLineSummaryValuesModel>>();
            services.AddScoped<IBaseRepository<SalesOrderReadModel>, BaseRepository<SalesOrderReadModel>>();
            services.AddScoped<IBaseRepository<InvoiceNotExportedModel>, BaseRepository<InvoiceNotExportedModel>>();


            services.AddScoped<IBaseRepository<LinkedAccountsCombinedInfoModel>, BaseRepository<LinkedAccountsCombinedInfoModel>>();
            services.AddScoped<IBaseRepository<CompanyCIPPDF>, BaseRepository<CompanyCIPPDF>>();
            services.AddScoped<IBaseRepository<SalesOrderForCompanyReadModel>, BaseRepository<SalesOrderForCompanyReadModel>>();
            services.AddScoped<IBaseRepository<SalesOrderSummaryValuesReadModel>, BaseRepository<SalesOrderSummaryValuesReadModel>>();
            services.AddScoped<IBaseRepository<Audit>, BaseRepository<Audit>>();
            services.AddScoped<IBaseRepository<InsuranceHistory>, BaseRepository<InsuranceHistory>>();
            services.AddScoped<IBaseRepository<ThisYearSalesOrderValueReadModel>, BaseRepository<ThisYearSalesOrderValueReadModel>>();
            services.AddScoped<IBaseRepository<LastYearSalesOrderValueReadModel>, BaseRepository<LastYearSalesOrderValueReadModel>>();
            services.AddScoped<IBaseRepository<InvoiceNotExportedReadModel>, BaseRepository<InvoiceNotExportedReadModel>>();
            services.AddScoped<IBaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel>, BaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel>>();
            services.AddScoped<IBaseRepository<SummeriseThisYearLastYearValueReadModel>, BaseRepository<SummeriseThisYearLastYearValueReadModel>>();
            services.AddScoped<IBaseRepository<EmailListByGroupName>, BaseRepository<EmailListByGroupName>>();
            services.AddScoped<IBaseRepository<HUBRFQForMail>, BaseRepository<HUBRFQForMail>>();
            services.AddScoped<IBaseRepository<HUBRFQHasRLStock>, BaseRepository<HUBRFQHasRLStock>>();
            services.AddScoped<IBaseRepository<PartLinesRL>, BaseRepository<PartLinesRL>>();
            services.AddScoped<IBaseRepository<RLPart>, BaseRepository<RLPart>>();
            services.AddScoped<IBaseRepository<EmailListByGroup>, BaseRepository<EmailListByGroup>>();
            services.AddScoped<IBaseRepository<AutoSearchCompanyForProspectReadModel>, BaseRepository<AutoSearchCompanyForProspectReadModel>>();
            services.AddScoped<IBaseRepository<AutoSearchContactReadModel>, BaseRepository<AutoSearchContactReadModel>>();
            services.AddScoped<IBaseRepository<PurchaseInfoReadModel>, BaseRepository<PurchaseInfoReadModel>>();
            services.AddScoped<IBaseRepository<SummariseThisYearPurchaseOrderValueReadModel>, BaseRepository<SummariseThisYearPurchaseOrderValueReadModel>>();
            services.AddScoped<IBaseRepository<SummariseLastYearPurchaseOrderValueReadModel>, BaseRepository<SummariseLastYearPurchaseOrderValueReadModel>>();
            services.AddScoped<IBaseRepository<ViewPurchaseOrderReadModel>, BaseRepository<ViewPurchaseOrderReadModel>>();
            services.AddScoped<IBaseRepository<OffersHistoryDetails>, BaseRepository<OffersHistoryDetails>>();
            services.AddScoped<IBaseRepository<CustomerRequirementReadModel>, BaseRepository<CustomerRequirementReadModel>>();
            services.AddScoped<IBaseRepository<OffersHistory>, BaseRepository<OffersHistory>>();
            services.AddScoped<IBaseRepository<Lytica>, BaseRepository<Lytica>>();
            services.AddScoped<IBaseRepository<PurchaseRequestLineReadModel>, BaseRepository<PurchaseRequestLineReadModel>>();
            services.AddScoped<IBaseRepository<QuoteVw>, BaseRepository<QuoteVw>>();
            services.AddScoped<IBaseRepository<PurchaseOrder>, BaseRepository<PurchaseOrder>>();
            services.AddScoped<IBaseRepository<Invoice>, BaseRepository<Invoice>>();
            services.AddScoped<IBaseRepository<CustomerRma>, BaseRepository<CustomerRma>>();
            services.AddScoped<IBaseRepository<SupplierRma>, BaseRepository<SupplierRma>>();
            services.AddScoped<IBaseRepository<Credit>, BaseRepository<Credit>>();
            services.AddScoped<IBaseRepository<Debit>, BaseRepository<Debit>>();
            services.AddScoped<IBaseRepository<InternalPurchaseOrder>, BaseRepository<InternalPurchaseOrder>>();
            services.AddScoped<IBaseRepository<SalesDetails>, BaseRepository<SalesDetails>>();
            services.AddScoped<IBaseRepository<CompanyCountModel>, BaseRepository<CompanyCountModel>>();
            services.AddScoped<IBaseRepository<CustomerRequirementForCompanyReadModel>, BaseRepository<CustomerRequirementForCompanyReadModel>>();
            services.AddScoped<IBaseRepository<BomCustomerRequirementModel>, BaseRepository<BomCustomerRequirementModel>>();
            services.AddScoped<IBaseRepository<LyticaManufacturerModel>, BaseRepository<LyticaManufacturerModel>>();
            services.AddScoped<IBaseRepository<BOMManagerForCompanyModel>, BaseRepository<BOMManagerForCompanyModel>>();
            services.AddScoped<IBaseRepository<InvoiceReadModel>, BaseRepository<InvoiceReadModel>>();
            services.AddScoped<IBaseRepository<PurchaseOrderReadModel>, BaseRepository<PurchaseOrderReadModel>>();
            services.AddScoped<IBaseRepository<CustomerRMAReadModel>, BaseRepository<CustomerRMAReadModel>>();
            services.AddScoped<IBaseRepository<SupplierRMAReadModel>, BaseRepository<SupplierRMAReadModel>>();
            services.AddScoped<IBaseRepository<CreditReadModel>, BaseRepository<CreditReadModel>>();
            services.AddScoped<IBaseRepository<DebitReadModel>, BaseRepository<DebitReadModel>>();
            services.AddScoped<IBaseRepository<CompanyReadModel>, BaseRepository<CompanyReadModel>>();
            services.AddScoped<IBaseRepository<HubrfqCommunicationNoteModel>, BaseRepository<HubrfqCommunicationNoteModel>>();
            services.AddScoped<IBaseRepository<GetDetailsForLineCalculationsReadModel>, BaseRepository<GetDetailsForLineCalculationsReadModel>>();
            services.AddScoped<IBaseRepository<IsIpoExistReadModel>, BaseRepository<IsIpoExistReadModel>>();
            services.AddScoped<IBaseRepository<GetAllSoLinesBySoIdReadModel>, BaseRepository<GetAllSoLinesBySoIdReadModel>>();
            services.AddScoped<IBaseRepository<GetClosedSoLinesBySoIdReadModel>, BaseRepository<GetClosedSoLinesBySoIdReadModel>>();
            services.AddScoped<IBaseRepository<GetOpenSoLinesBySoIdReadModel>, BaseRepository<GetOpenSoLinesBySoIdReadModel>>();
            services.AddScoped<IBaseRepository<AutoSourcingReadModel>, BaseRepository<AutoSourcingReadModel>>();
            services.AddScoped<IBaseRepository<ExportApprovalSOLineReadModel>, BaseRepository<ExportApprovalSOLineReadModel>>();

            services.AddScoped<IBaseRepository<PvvBom>, BaseRepository<PvvBom>>();
            services.AddScoped<IBaseRepository<PvvBomQuestion>, BaseRepository<PvvBomQuestion>>();
            services.AddScoped<IBaseRepository<HubRfqPvvAnswer>, BaseRepository<HubRfqPvvAnswer>>();
            services.AddScoped<IBaseRepository<BOMListForCustomerRequirement>, BaseRepository<BOMListForCustomerRequirement>>();
            services.AddScoped<IBaseRepository<DataTemp>, BaseRepository<DataTemp>>();
            services.AddScoped<IBaseRepository<HubRfqPvvAnswerTemp>, BaseRepository<HubRfqPvvAnswerTemp>>();
            services.AddScoped<IBaseRepository<BOMExcelHeaderFrom>, BaseRepository<BOMExcelHeaderFrom>>();
            services.AddScoped<IBaseRepository<BOMImportData>, BaseRepository<BOMImportData>>();
            services.AddScoped<IBaseRepository<ExportToExcelErrorBomImport>, BaseRepository<ExportToExcelErrorBomImport>>();
            services.AddScoped<IBaseRepository<SourcingResultDetails>, BaseRepository<SourcingResultDetails>>();
            services.AddScoped<IBaseRepository<CompanySalesInfoReadModel>, BaseRepository<CompanySalesInfoReadModel>>();
            services.AddScoped<IBaseRepository<GetCurrencyRateCurrentAtDateReadModel>, BaseRepository<GetCurrencyRateCurrentAtDateReadModel>>();
            services.AddScoped<IBaseRepository<PurchaseRequisitionFilterResult>, BaseRepository<PurchaseRequisitionFilterResult>>();
            services.AddScoped<IBaseRepository<BOMCustomerRequirementSourcingResult>, BaseRepository<BOMCustomerRequirementSourcingResult>>();
            services.AddScoped<IBaseRepository<SourcingResult>, BaseRepository<SourcingResult>>();
            services.AddScoped<IBaseRepository<SearchForPOApproveSupplier>, BaseRepository<SearchForPOApproveSupplier>>();
            services.AddScoped<IBaseRepository<BOMDLNDetailModel>, BaseRepository<BOMDLNDetailModel>>();
            services.AddScoped<IBaseRepository<SaleCompanyModel>, BaseRepository<SaleCompanyModel>>();
            services.AddScoped<IBaseRepository<TempPvvBomQuestion>, BaseRepository<TempPvvBomQuestion>>();
            services.AddScoped<IBaseRepository<BOMPHDetailModel>, BaseRepository<BOMPHDetailModel>>();
            services.AddScoped<IBaseRepository<SalesOrderLineModel>, BaseRepository<SalesOrderLineModel>>();
            services.AddScoped<IBaseRepository<PurchaseRequisitionForPageModel>, BaseRepository<PurchaseRequisitionForPageModel>>();
            services.AddScoped<IBaseRepository<PartWatchMatchingRequirement>, BaseRepository<PartWatchMatchingRequirement>>();
            services.AddScoped<IBaseRepository<BomForPageModel>, BaseRepository<BomForPageModel>>();
            services.AddScoped<IBaseRepository<IhsPartSearchDetailModel>, BaseRepository<IhsPartSearchDetailModel>>();
            services.AddScoped<IBaseRepository<IsSoLineExistingSourcingResultReadModel>, BaseRepository<IsSoLineExistingSourcingResultReadModel>>();
            services.AddScoped<IBaseRepository<GetQuoteLineDetailsReadModel>, BaseRepository<GetQuoteLineDetailsReadModel>>();
            services.AddScoped<IBaseRepository<GetProductStatusMessageReadModel>, BaseRepository<GetProductStatusMessageReadModel>>();

            services.AddScoped<IHubSourcingResultImportRepository<HubSourcingResultImportTempDetails>, HubSourcingResultImportRepository>();

            services.AddScoped<IImportBaseRepository<BomImportSourcingTemp>, ImportBaseRepository<BomImportSourcingTemp>>();
            services.AddScoped<IBaseRepository<SalesOrderLineDetails>, BaseRepository<SalesOrderLineDetails>>();
            services.AddScoped<IBaseRepository<SalesOrderStatus>, BaseRepository<SalesOrderStatus>>();
            services.AddScoped<IBaseRepository<CustomerRequirementForBom>, BaseRepository<CustomerRequirementForBom>>();
            services.AddScoped<IBaseRepository<PurchaseRequestLineDetail>, BaseRepository<PurchaseRequestLineDetail>>();
            services.AddScoped<IBaseRepository<ReportColumn>, BaseRepository<ReportColumn>>();
            services.AddScoped<IBaseRepository<QuoteLineOpenForQuoteReadModel>, BaseRepository<QuoteLineOpenForQuoteReadModel>>();
            services.AddScoped<IBaseRepository<SalesOrderPDF>, BaseRepository<SalesOrderPDF>>();

            services.AddScoped<IBaseRepository<SalesOrderDetailsReadModel>, BaseRepository<SalesOrderDetailsReadModel>>();
            services.AddScoped<IBaseRepository<GetPromiseLogReadModel>, BaseRepository<GetPromiseLogReadModel>>();
            services.AddScoped<IBaseRepository<EditExportApprovalReadModel>, BaseRepository<EditExportApprovalReadModel>>();
            services.AddScoped<IBaseRepository<UpdateExportApprovalReadModel>, BaseRepository<UpdateExportApprovalReadModel>>();
            services.AddScoped<IBaseRepository<SOsurchargeReadModel>, BaseRepository<SOsurchargeReadModel>>();
            services.AddScoped<IBaseRepository<SalesOrderForPageReadModel>, BaseRepository<SalesOrderForPageReadModel>>();
            services.AddScoped<IBaseRepository<ActiveHUBRFQClientModel>, BaseRepository<ActiveHUBRFQClientModel>>();
            services.AddScoped<IBaseRepository<SalesOrderValueSummaryReadModel>, BaseRepository<SalesOrderValueSummaryReadModel>>();
            services.AddScoped<IBaseRepository<AuthorisationHistoryReadModel>, BaseRepository<AuthorisationHistoryReadModel>>();
            services.AddScoped<IBaseRepository<As6081AlertMessages>, BaseRepository<As6081AlertMessages>>();
            services.AddScoped<IBaseRepository<BOMSourcingResultForReleaseModel>, BaseRepository<BOMSourcingResultForReleaseModel>>();
            services.AddScoped<IBaseRepository<ExportApprovalDataReadModel>, BaseRepository<ExportApprovalDataReadModel>>();
            services.AddScoped<IBaseRepository<ApproveRejectExportApprovalReadModel>, BaseRepository<ApproveRejectExportApprovalReadModel>>();
            services.AddScoped<IBaseRepository<ExportApprovalStatusOGEL>, BaseRepository<ExportApprovalStatusOGEL>>();
            services.AddScoped<IBaseRepository<SOPaymentInfo>, BaseRepository<SOPaymentInfo>>();
            services.AddScoped<IBaseRepository<SalesOrderExcel>, BaseRepository<SalesOrderExcel>>();
            services.AddScoped<IBaseRepository<CustomerRequirementBomModel>, BaseRepository<CustomerRequirementBomModel>>();
            services.AddScoped<IBaseRepository<KubPermissionCheckModel>, BaseRepository<KubPermissionCheckModel>>();
            services.AddScoped<IBaseRepository<KubAssistanceModel>, BaseRepository<KubAssistanceModel>>();
            services.AddScoped<IBaseRepository<CurrencyConversionModel>, BaseRepository<CurrencyConversionModel>>();
            services.AddScoped<IBaseRepository<SOAddVancePaymentnotificationReadModel>, BaseRepository<SOAddVancePaymentnotificationReadModel>>();
            services.AddScoped<IBaseRepository<ProductSource>, BaseRepository<ProductSource>>();
            services.AddScoped<IBaseRepository<AutoSearchPowerAppApproverReadModel>, BaseRepository<AutoSearchPowerAppApproverReadModel>>();
            services.AddScoped<IBaseRepository<SalesOrderNotifyerReadModel>, BaseRepository<SalesOrderNotifyerReadModel>>();
            services.AddScoped<IBaseRepository<SorDetailForPowerAppReadModel>, BaseRepository<SorDetailForPowerAppReadModel>>();

            services.AddScoped<IBaseRepository<SalesOrderLine>, BaseRepository<SalesOrderLine>>();
            services.AddScoped<IBaseRepository<OgelSelectSoNotifierReadModel>, BaseRepository<OgelSelectSoNotifierReadModel>>();
            services.AddScoped<IBaseRepository<PowerAppTokenInfoReadModel>, BaseRepository<PowerAppTokenInfoReadModel>>();
            services.AddScoped<IBaseRepository<OgelEccnGroupMemberEmailReadModel>, BaseRepository<OgelEccnGroupMemberEmailReadModel>>();
            services.AddScoped<IBaseRepository<GetAllSoLinesforEuuReadModel>, BaseRepository<GetAllSoLinesforEuuReadModel>>();
            services.AddScoped<IBaseRepository<GetSupplierTypeForSearchReadModel>, BaseRepository<GetSupplierTypeForSearchReadModel>>();
            services.AddScoped<IBaseRepository<CompanyIndustryForSearchReadModel>, BaseRepository<CompanyIndustryForSearchReadModel>>();
            services.AddScoped<IBaseRepository<GetSourcingDataSearchReadModel>, BaseRepository<GetSourcingDataSearchReadModel>>();

            services.AddScoped<IBaseRepository<POQuoteLineDataListNuggetReadModel>, BaseRepository<POQuoteLineDataListNuggetReadModel>>();
            services.AddScoped<IBaseRepository<POQuoteReadModel>, BaseRepository<POQuoteReadModel>>();
            services.AddScoped<IBaseRepository<POQuoteForPageReadModel>, BaseRepository<POQuoteForPageReadModel>>();
            services.AddScoped<IBaseRepository<POQuoteLineReadModel>, BaseRepository<POQuoteLineReadModel>>();
            services.AddScoped<IBaseRepository<PurchaseRequestLineDetailReadModel>, BaseRepository<PurchaseRequestLineDetailReadModel>>();
            services.AddScoped<IBaseRepository<POQuoteLineLogReadModel>, BaseRepository<POQuoteLineLogReadModel>>();
            services.AddScoped<IBaseRepository<POQuoteLineLogBomReadModel>, BaseRepository<POQuoteLineLogBomReadModel>>();
            services.AddScoped<IBaseRepository<SoLineEuuPdf>, BaseRepository<SoLineEuuPdf>>();
            services.AddScoped<IBaseRepository<SourcingImage>, BaseRepository<SourcingImage>>();

            services.AddScoped<IBaseRepository<ItemsearchQuoteLineReadModel>, BaseRepository<ItemsearchQuoteLineReadModel>>();
            services.AddScoped<IBaseRepository<ItemsearchSalesOrderLineReadModel>, BaseRepository<ItemsearchSalesOrderLineReadModel>>();
            services.AddScoped<IBaseRepository<SupplierMessageReadModel>, BaseRepository<SupplierMessageReadModel>>();
            services.AddScoped<IBaseRepository<PoLineWarning>, BaseRepository<PoLineWarning>>();
            services.AddScoped<IBaseRepository<PurchaseOrderDetailsReadModel>, BaseRepository<PurchaseOrderDetailsReadModel>>();
            services.AddScoped<IBaseRepository<ListForBomSourcingResultModel>, BaseRepository<ListForBomSourcingResultModel>>();
            services.AddScoped<IBaseRepository<SourcingLogModel>, BaseRepository<SourcingLogModel>>();
            services.AddScoped<IBaseRepository<ItemSearchStockReadModel>, BaseRepository<ItemSearchStockReadModel>>();
            services.AddScoped<IBaseRepository<ItemSearchServiceReadModel>, BaseRepository<ItemSearchServiceReadModel>>();
            services.AddScoped<IBaseRepository<SourcingResultForCustomerReqReadModel>, BaseRepository<SourcingResultForCustomerReqReadModel>>();
            services.AddScoped<IBaseRepository<StockItemDetailReadModel>, BaseRepository<StockItemDetailReadModel>>();
            services.AddScoped<IBaseRepository<ServiceItemDetailReadModel>, BaseRepository<ServiceItemDetailReadModel>>();
            services.AddScoped<IBaseRepository<CustomerRequirementWithoutBom>, BaseRepository<CustomerRequirementWithoutBom>>();
        }

        private static void RegisterIHSService(this IServiceCollection services)
        {
            services.AddSingleton<IIhsApiClient, IhsApiClient>();
            services.AddSingleton<IIHSService, IHSService>();
        }

        private static void RegisterBlobStorageService(this IServiceCollection services, IConfiguration configuration)
        {
            var blobStorageConnectionString = configuration.GetConnectionString("AzureBlobStorage")
                ?? throw new InvalidOperationException("Connection string 'AzureBlobStorage' not found.");
            services.AddSingleton(x => new BlobServiceClient(blobStorageConnectionString));
            services.AddScoped<IBlobStorageService, BlobStorageService>();
        }

        private static void RegisterEmailService(this IServiceCollection services, IConfiguration configuration)
        {
            var emailSettings = configuration.GetSection("EmailSettings")
                ?? throw new InvalidOperationException("'EmailSettings' configuration not found.");
            services.Configure<EmailSettings>(emailSettings);

            if (string.IsNullOrWhiteSpace(emailSettings["TestReceiverEmail"]))
            {
                services.AddScoped<ISmtpClient, EmailClient>();
            }
            else
            {
                services.AddScoped<ISmtpClient, EmailTestClient>();
            }

            services.AddScoped<IEmailService, EmailService>();
        }

        private static void RegisterFEService(this IServiceCollection services)
        {
            services.AddSingleton<IFEApiClient, FEApiClient>();
            services.AddSingleton<IFEService, FEService>();
        }

        private static void RegisterDigiKeyService(this IServiceCollection services)
        {
            services.AddSingleton<IDigiKeyApiClient, DigiKeyApiClient>();
            services.AddSingleton<IDigiKeyService, DigiKeyService>();
        }

        private static void RegisterLyticaService(this IServiceCollection services)
        {
            services.AddSingleton<ILyticaApiClient, LyticaApiClient>();
        }
        private static void RegisterPowerAutomateService(this IServiceCollection services)
        {
            services.AddScoped<IPowerAutomateApiClient, PowerAutomateApiClient>();
        }

        private static void RegisterPdfService(this IServiceCollection services, IConfiguration configuration)
        {
            IronPdf.License.LicenseKey = configuration["IronPdf:LicenseKey"];
            IronPdf.Installation.AutomaticallyDownloadNativeBinaries = true;
            IronPdf.Installation.ChromeGpuMode = IronPdf.Engines.Chrome.ChromeGpuModes.Disabled;

            services.AddScoped<IPdfService, PdfService>();
        }

        private static void RegisterFileDataReaderServices(this IServiceCollection services)
        {
            services.AddSingleton<IExcelDataReaderService, ExcelDataReaderService>();
            services.AddScoped<ICsvFileDataReaderService<ImportSourcingResultModel>, CsvFileDataReaderService<ImportSourcingResultModel>>();
        }
    }
}
