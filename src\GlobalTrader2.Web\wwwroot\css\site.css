﻿html {
    font-size: 14px;
    position: relative;
    min-height: 100%;
}

body, div, p, td, select, input, textarea {
    font-family: Tahoma, sans-serif !important;
}

.title-bar-background-brick {
    background-image: url('/App_Themes/Original/images/backgrounds/brick.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-fern {
    background-image: url('/App_Themes/Original/images/backgrounds/fern.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-flowers {
    background-image: url('/App_Themes/Original/images/backgrounds/flowers.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-fruit {
    background-image: url('/App_Themes/Original/images/backgrounds/fruit.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-map {
    background-image: url('/App_Themes/Original/images/backgrounds/map.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-metal {
    background-image: url('/App_Themes/Original/images/backgrounds/metal.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-office {
    background-image: url('/App_Themes/Original/images/backgrounds/office.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-palm {
    background-image: url('/App_Themes/Original/images/backgrounds/palm.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-plants {
    background-image: url('/App_Themes/Original/images/backgrounds/plants.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-silk {
    background-image: url('/App_Themes/Original/images/backgrounds/silk.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-sky {
    background-image: url('/App_Themes/Original/images/backgrounds/sky.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-skyold {
    background-image: url('/App_Themes/Original/images/backgrounds/skyold.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-snowflake {
    background-image: url('/App_Themes/Original/images/backgrounds/snowflake.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-spark {
    background-image: url('/App_Themes/Original/images/backgrounds/spark.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-sun {
    background-image: url('/App_Themes/Original/images/backgrounds/sun.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-sunflowers {
    background-image: url('/App_Themes/Original/images/backgrounds/sunflowers.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-wood {
    background-image: url('/App_Themes/Original/images/backgrounds/wood.jpg');
    background-repeat: no-repeat;
}

.title-bar-background-xmaslights {
    background-image: url('/App_Themes/Original/images/backgrounds/xmaslights.jpg');
    background-repeat: no-repeat;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

:focus-visible {
    outline: 2px solid var(--primary);
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.header-menu {
    text-decoration: none;
    color: #006600 !important;
    padding: 4px 3px;
    font-weight: bold;
    font-size: 11px;
}

.subTitle {
    font-size: 14px;
    font-weight: normal;
    color: #006600;
}

header h1 {
    font-size: 22px;
    font-weight: normal;
    margin: 0px;
    overflow: hidden;
}

img.client-logo {
    font-size: 0px;
}

.header-logo {
    background-image: url(/App_Themes/Original/images/misc/logo.gif);
    background-repeat: no-repeat;
    bottom: 10px;
    height: 17px;
    overflow: hidden;
    padding: 0;
    text-indent: -99999px;
    width: 159px;
    float: right;
}

.sidebar-toggle {
    height: 100%;
}

/*Override default jquerycss*/
.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button {
    font-family: Tahoma, sans-serif;
}

/*Definition stylesheet for new wireframe GTv2*/
.btn-success-custom {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(#80c24d, #459e00);
    border-radius: 6px;
    border: 1px solid #327e04;
    height: 22px;
    color: #ffffff;
    gap: 6px;
    font-size: 12px;
    font-weight: 400;
}

.btn-info-custom {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #327E04;
    border-radius: 6px;
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
    height: 22px;
    gap: 6px;
    color: #ffffff;
    font-size: 12px;
    font-weight: 400;
}

.btn-danger-custom {
    display: flex;
    align-items: center;
    border-radius: 6px;
    background: linear-gradient(180deg, #FE8270 0%, #F46962 100%);
    height: 22px;
    border: 1px solid #cd5c0a;
    color: #ffffff;
    gap: 6px;
    font-size: 12px;
    font-weight: 400;
}

.btn-disabled-custom {
    display: flex;
    align-items: center;
    border: 1px solid #363636;
    border-radius: 6px;
    background: #cccccc;
    height: 22px;
    color: #ffffff;
    gap: 6px;
}

.force-disabled-content {
    pointer-events: none !important;
    cursor: pointer !important;
    opacity: 0.5;
}

/*Checkbox-gtv2 custom*/
.checkbox-custom {
    position: relative;
    width: 23px;
    height: 23px;
    appearance: none;
    background-color: #fafaf4;
    border: 1px solid #327E04;
    border-radius: 2.11px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s;
    display: flex;
    align-items: start;
    justify-content: center;
    box-shadow: inset 0 0 1px #cccccc;
}

    .checkbox-custom:checked::after {
        content: "";
        position: absolute;
        left: 5px;
        top: 0;
        width: 45%;
        height: 75%;
        border: solid #327E04;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

/*Checkbox-gtv2 custom end*/

.input-custom {
    border-radius: 0;
    border: 1px solid #d4ccb0;
    font-size: 14px;
    line-height: 16.9px;
    font-weight: 400;
    color: #000000;
    background: #fafaf4;
}

    .input-custom input:focus {
        border-radius: 0;
        border: 1px solid #d4ccb0;
    }

.textarea-custom {
    border-radius: 0;
    border: 1px solid #d4ccb0;
    font-size: 14px;
    line-height: 16.9px;
    font-weight: 400;
    color: #000000;
    background: #fafaf4;
}

    .textarea-custom input:focus {
        border-radius: 0;
        border: 1px solid #d4ccb0;
    }

/*select-menu, fetch-dropdown*/
.select-menu-custom + span,
.select-menu-custom + span:hover,
.select-menu-custom + span:focus {
    border-radius: 0;
    border: 1px solid #d4ccb0;
    background: #fafaf4;
    color: #000000;
    font-size: 14px;
    line-height: 16.9px;
    font-weight: 400;
    height: 28px;
    padding-top: 4px;
    flex: 1;
}

.select-menu-custom + .ui-selectmenu-button + .select-menu-custom-refesh-button {
    margin-left: 5px;
}

    .select-menu-custom + .ui-selectmenu-button + .select-menu-custom-refesh-button img {
        height: 19px;
        width: 19px;
    }

.select-menu-custom + .ui-selectmenu-button,
.select-menu-custom + .ui-selectmenu-button:hover,
.select-menu-custom + .ui-selectmenu-button:focus,
.select-menu-custom + .ui-selectmenu-button:hover:focus {
    padding: 5px 14px;
}

    .select-menu-custom + .ui-selectmenu-button .ui-selectmenu-text {
        font-size: 12px;
        font-weight: 400;
    }

.select-menu-custom.ui-menu {
    border-radius: 0;
    border: 1px solid #d4ccb0;
    background: #fafaf4;
}

.ui-menu-custom {
    max-height: 300px;
}

.select-menu-custom.ui-menu .ui-menu-item .ui-menu-item-wrapper,
.ui-menu-item-wrapper:hover,
.ui-menu-item-wrapper:focus,
.ui-menu-item-wrapper.ui-state-active,
.ui-menu-item-wrapper.ui-state-active:hover,
.ui-menu-item-wrapper.ui-state-active:focus {
    font-size: 12px;
    font-weight: 400;
    margin: -1px;
    border: none;
}

@media (max-width: 1368px) {
    .select-menu-custom + span, .select-menu-custom + span:hover, .select-menu-custom + span:focus {
        height: 28px;
    }
}

.select-menu-basic-custom + span,
.select-menu-basic-custom + span:hover,
.select-menu-basic-custom + span:focus {
    padding: 6px 12px 6px 12px;
    border-radius: 4px;
    border: 1px solid #ced4da;
    background: #ffffff;
    color: #000000;
    font-weight: 400;
}

.ui-button .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
    background-image: url("../lib/jquery-ui/images/ui-icons_808080_256x240.png");
}

ui-selectmenu-icon.select-menu-custom-loading-icon {
    background-image: url("../img/icons/loading.gif");
}

.ui-accordion-header img {
    height: 17px;
}
/*section-box*/
.opacity-1 {
    opacity: 1 !important;
}

.section-box-subtitle {
    margin-left: 15px;
    color: #000000;
}

.section-box.ui-accordion {
    background-color: #fafaf4;
}

    .section-box.ui-accordion .ui-accordion-header {
        font-size: 12px;
        line-height: 18.11px;
        position: relative;
        padding: 3px 13px;
        background-image: none;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        min-height: 28px;
    }

        .section-box.ui-accordion .ui-accordion-header .ui-icon {
            margin-top: 0;
        }

    .section-box.ui-accordion .section-box-refesh-button img {
        height: 17px;
    }

    .section-box.ui-accordion .ui-accordion-content {
        background: #ffffff;
        padding: 10px 24px;
    }

    .section-box.ui-accordion .section-box-refesh-button {
        position: absolute;
        right: 13px;
        top: -1px;
    }

    .section-box.ui-accordion .ui-accordion-header .section-box-button-group {
        padding: 5px 0px;
        width: fit-content;
        display: flex;
        flex-basis: 100%;
    }

    .section-box.ui-accordion .ui-accordion-header .section-box-message {
        padding: 5px 0px;
        height: 32px;
        flex-basis: 100%;
        color: #000000;
    }

    .section-box.ui-accordion .ui-accordion-header .ui-button {
        font-size: 12px;
        height: 22px;
        padding: 0px 6px;
        font-weight: 400;
        line-height: 14.48px;
        cursor: pointer;
    }

    .section-box.ui-accordion span.not-found {
        font-size: 11px;
        font-style: italic;
        color: #999999;
    }

    .section-box.ui-accordion .section-box-footer {
        padding: 3px 13px;
        margin: 0px -24px -10px -24px;
        background: #fafaf4 url("images/ui-bg_highlight-hard_100_fafaf4_1x100.png") 50% 50% repeat-x;
        background-image: url("images/ui-bg_highlight-hard_100_fafaf4_1x100.png");
        border-top: 1px solid #d4ccb0;
    }
/* ui-state */
.ui-state-info,
.ui-widget-content .ui-state-info,
.ui-widget-header .ui-state-info {
    border: 1px solid #1ABCFE;
    background: #FFEDAD;
    color: #0D6EFD;
}

    .ui-state-info .ui-icon,
    .ui-state-info.ui-icon {
        background-image: url("../lib/jquery-ui/images/ui-icons_0D6EFD_256x240.png");
    }

/* simple-table */
.simple-table.dataTable thead {
    background-color: #DEE2E6;
    font-size: 12px;
    font-weight: 700;
    line-height: 14.48px;
}

.simple-table.dataTable tbody tr td {
    font-size: 12px;
    font-weight: 400;
    line-height: 14.48px;
}

.simple-table.dataTable tbody tr.text-bold td {
    font-weight: 700;
}

.simple-table.dataTable tbody tr.text-light-gray td {
    color: #0000004D;
}

.simple-table.dataTable tbody tr.red-background {
    background-color: #FF6464;
}

.simple-table.dataTable tbody tr.selected {
    background: linear-gradient(180deg, #80C24D 0%, #459E00 100%);
}

.simple-table.dataTable tbody tr.hover-row {
    background-color: #CDF2C7;
}

.simple-table.dataTable tbody tr.text-italic td {
    font-style: italic;
}

/*dialog confirm custom*/
.dialog-confirm-container {
    font-family: Tahoma, sans-serif;
}

    .dialog-confirm-container.ui-dialog {
        padding: 4.52px;
    }

    .dialog-confirm-container.ui-widget.ui-widget-content {
        border: 1px solid #D4CCB0;
    }


    .dialog-confirm-container.ui-dialog .ui-dialog-titlebar {
        padding: 3px 20px;
        width: 285.96px;
    }

.dialog-disable-user-title img {
    height: 18px;
    width: 18px;
}

.dialog-confirm-container .ui-dialog-title {
    display: flex;
    align-items: center;
    color: #212529;
    gap: 3.5px;
    font-weight: 400;
    font-size: 15px;
    line-height: 18.11px;
    margin: 0;
}

.dialog-confirm-container .dialog-disable-user-content {
    width: 285.96px !important;
    padding: 18.8px 20px;
    gap: 20px;
    font-size: 12px;
    line-height: 14.48px;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    border-top: 1px solid #D4CCB0;
}

.dialog-confirm-container .ui-dialog-buttonset .btn-success-custom {
    width: 55px;
}

.dialog-confirm-container .ui-dialog-buttonset .btn-danger-custom {
    width: 51px;
}

.dialog-disable-user-content #content-data {
    display: flex;
    flex-direction: row;
    gap: 5px;
    width: 100%;
}

.dialog-disable-user-content p {
    margin: 0;
    word-break: break-word;
}

#content-data strong {
    width: 97.5px;
}

.dialog-confirm-container.ui-dialog .ui-dialog-buttonpane {
    padding: 10px 20px;
    margin: 0;
    width: 285.96px;
    height: 48px;
}

.ui-dialog-buttonpane .ui-dialog-buttonset {
    display: flex;
    gap: 5px;
}

.ui-dialog .ui-dialog-buttonpane .btn-success-custom {
    margin: 0;
    height: 28px;
    font-size: 12px;
    line-height: 14.48px;
}

.ui-dialog .ui-dialog-buttonpane .btn-danger-custom {
    margin: 0;
    height: 28px;
    font-size: 12px;
    line-height: 14.48px;
}

.ui-dialog-buttonset .btn-success-custom,
.ui-dialog-buttonset .btn-danger-custom {
    display: flex;
    gap: 6px;
    align-items: center;
}

    .ui-dialog-buttonset .btn-success-custom img,
    .ui-dialog-buttonset .btn-danger-custom img {
        width: 18px;
        height: 18px;
    }

/*Toast*/
.ui-icon-alert-info {
    width: 16px;
    height: 16px;
    background-image: url("../img/icons/alert-info.svg") !important;
}

#toast-container {
    position: fixed;
    top: 4px;
    right: 4px;
    z-index: 9999;
    max-width: 345px;
}

.toast-container {
    margin-bottom: 5px;
}

    .toast-container .ui-icon .ui-icon-info {
        width: 16px;
        height: 16px;
    }

    .toast-container .ui-state-highlight,
    .toast-container .ui-state-error,
    .toast-container .ui-state-info {
        height: 100%;
        padding: 18.59px 13.32px;
    }

    .toast-container p {
        margin: 0;
        min-height: 21px;
        display: flex;
        gap: 3px;
    }

    .toast-container #toast-type {
        flex: 1 1 0%;
        text-transform: capitalize;
        font-size: 17.05px;
        line-height: 20.63px;
        font-weight: 700;
    }

    .toast-container #toast-message {
        text-transform: none;
        margin-left: 3px;
        font-size: 16.23px;
        line-height: 19.64px;
        font-weight: 400;
        word-break: break-word;
    }

.page-primary-title {
    font-size: 19px;
    font-weight: 400;
    line-height: 22.93px;
    text-align: left;
}

.page-sub-title {
    color: var(--primary);
    font-weight: 400;
    line-height: 13.28px;
    text-align: left;
    font-size: 11px;
    text-transform: uppercase;
}

.no-scroll {
    overflow: hidden;
}


/*Dialog form*/
.dialog-form-container.ui-dialog .ui-dialog-buttonpane .btn-success-custom {
    width: 62px;
}

.dialog-form-container.ui-dialog .ui-dialog-titlebar {
    padding: 3px 20px;
    width: 285.96px;
}

.dialog-form-container .ui-dialog-title {
    display: flex;
    align-items: center;
    color: #212529;
    gap: 3.5px;
    font-weight: 400;
    font-size: 15px;
    line-height: 18.11px;
    margin: 0;
}

    .dialog-form-container .ui-dialog-title img {
        width: 18px;
        height: 18px;
    }

.dialog-form-container .ui-dialog-buttonpane {
    margin: 0;
    padding: 10px 20px;
}

/*input error*/
.error-input-field {
    border: 1px solid #cd5c0A !important;
    color: #ec3814 !important;
}

.error-input {
    border: 1px solid #cd5c0A !important;
}

.required {
    font-weight: bold;
    color: rgba(236, 56, 20, 1);
}

    .required:not(.ms-1):not(p .required) {
        margin-left: .25rem !important;
    }

.form-error-summary {
    display: flex;
    gap: 5px;
    align-items: center;
    background: #F8D7DA;
    color: #EC3814;
    line-height: 14.48px;
    padding: 2px;
}

    .form-error-summary img {
        width: 24px;
        height: 24px;
    }

    .form-error-summary div p {
        margin-bottom: 0;
    }

section-error-summary {
    display: flex;
    gap: 5px;
    align-items: center;
    background: #F8D7DA;
    color: #EC3814;
    line-height: 14.48px;
    padding: 2px;
}

.section-error-summary img {
    width: 24px;
    height: 24px;
}

.section-error-summary div p {
    margin-bottom: 0;
}

h5 {
    font-size: 14px;
    font-weight: bold;
}

.line {
    height: 1px;
    background: linear-gradient(to right, transparent 50%, #223049 50%);
    background-size: 4px 2px, 100% 2px;
}

.dt-column-line {
    border-bottom: dotted 1px #999
}

.primary-solid-line {
    height: 1px;
    background: rgba(212, 204, 176, 1);
    background-size: 4px 2px, 100% 2px;
}

.row-text-bold td {
    font-weight: 700 !important;
}

input.error-input-field::placeholder {
    color: #ec3814
}
/* Fieldset */
legend {
    display: block;
    unicode-bidi: isolate;
    padding-inline: 2px;
    float: none;
    width: auto;
    text-transform: uppercase;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 14.48px;
}

fieldset {
    height: auto;
    display: block;
    min-inline-size: min-content;
    margin-inline: 2px;
    border: 1px solid #D4CCB0;
    border-image: initial;
    padding-block: 0.35em 0.625em;
    padding-inline: 0.75em;
    border-radius: 4px;
    padding: 20px 10px;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dotted {
    border-bottom: dotted 1px #cccccc;
}

.custom-capitalize {
    text-transform: lowercase; /* Start by making all letters lowercase */
}

    .custom-capitalize:first-letter {
        text-transform: uppercase; /* Capitalize only the first letter */
    }

.settings-table {
    width: 100%;
    margin-top: 10px;
}

    .settings-table td {
        padding: 10px;
        vertical-align: middle;
    }

    .settings-table .label {
        font-weight: bold;
        color: #333;
    }

    .settings-table .checkbox {
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
    }

    .settings-table .star-rating .star {
        font-size: 13px !important;
    }

.default-bg {
    background-image: url('/App_Themes/Original/images/backgrounds/plants.jpg');
    background-repeat: no-repeat;
}

/* Group members content */
.group-name a {
    text-decoration: none;
    font-size: 12px;
    color: #0D6EFD;
}

    .group-name a:hover {
        text-decoration-line: underline;
    }

#group-members-content {
    max-height: 202px;
}

div.quotedMail {
    background-color: #F0F0FF;
    border: 2px solid #CFCFFF;
    color: #0000FF;
    padding: 5px;
}

.counting-message {
    margin-top: 5px;
}

/* Hyperlink no underline */
.no-underline-hyperlink {
    text-decoration: none;
    color: #0d6efd !important;
}

    .no-underline-hyperlink:hover {
        text-decoration: underline;
    }

/* Focusable for label */
.button-like-div {
    all: unset;
    display: block;
    width: 100%;
    background-color: transparent;
    text-align: left;
    cursor: text !important;
    pointer-events: none;
}

.button-like-label:focus {
    outline: none; /* Remove focus outline */
}

.hazardous {
    display: inline;
    line-height: normal;
    background-repeat: no-repeat;
    background-color: yellow;
    background-position: right 0px;
    padding-right: 28px;
    font-size: 14px;
    color: #212529;
}

a .hazardous {
    color: inherit;
}

.rohs-sourcing-dt {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
}

.rohs {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 12px;
}

.onstop-badge {
    display: inline-flex;
    align-items: center;
    font-size: 11px;
    background-color: #dc3545;
    color: #fff;
    border-radius: 4px;
    padding: 1px 3px;
    min-width: 16px;
    justify-content: center;
    margin-top: 1px;
}
    .onstop-badge:hover {
        padding: 2px 3px;
    }

    .onstop-badge img {
        margin: 3px 2px;
    }

    .onstop-badge .text {
        display: none;
    }

    .onstop-badge:hover .text {
        display: inline;
        cursor: default;
    }

    .onstop-badge:hover img {
        margin: 0;
    }

.rohsCompliant {
    background-image: url('../img/rohs/compliant.gif');
}

.rohsNonCompliant {
    background-image: url('../img/rohs/non_compliant.gif');
}

.rohsExempt {
    background-image: url('../img/rohs/exempt.gif');
}

.rohsROHS2 {
    background-image: url('../img/rohs/ROHS2.gif');
}

.rohsROHS56 {
    background-image: url('../img/rohs/ROHS56.gif');
}

.rohsROHS66 {
    background-image: url('../img/rohs/ROHS66.gif');
}

.hazardousone {
    background-image: url('../img/hazardous/Hazardousone.png');
}

.hazardousProduct {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    background-image: url(../img/hazardous/Hazardousone.png);
}

.hazardousIpo {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    background-image: url(../img/hazardous/IPOprodone.png);
}

.hazardousRh {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    background-image: url(../img/hazardous/Restrictedprodone.png);
}

.ihspartstatusdoc {
    display: inline;
    background-repeat: no-repeat;
    padding-right: 20px;
    font-size: 12px;
    white-space: nowrap;
    background-image: url(../img/hazardous/ihspartstatuspng.png);
    background-size: contain;
    background-position: right;
}

.stacked-header-top {
    border-bottom: dotted 1px #999;
    margin-bottom: 5px;
    padding-bottom: 8px;
}

.stacked-cell-top {
    margin-bottom: 4px;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select-menu-gtv2-refresh-button img {
    height: var(--form-control-height) !important;
}
.fs-10px{
    font-size: 10px;
}
.advisory-notes {
    background-image: url(/img/icons/circle-exclamation-red.svg);
    display: inline-block; 
    width: 14px; 
    height: 14px; 
    background-repeat: no-repeat; 
    background-size: contain;
    margin-left: 10px;
}
.custom-tooltip {
    position: relative;
    display: inline-block;
}

.custom-tooltip .tooltip-text {
    visibility: hidden;
    width: auto;
    background-color: #f5f3e5;
    text-align: left;
    border-radius: 6px;
    padding: 8px 12px;
    position: absolute;
    z-index: 10;
    left: 110%;
    font-size: 12px !important;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
    color: black;
    white-space: nowrap;
}

.custom-tooltip:hover .tooltip-text {
    visibility: visible;
}
.attention-note {
    background-color: yellow !important;
    color: #dc3545 !important;
    font-weight: bold !important;
}
.green-note {
    font-weight: bold !important;
    color: green !important;
}
.not-found-resource-message {
    display: none;
    align-items: center;
    justify-content: center;
    height: 105px;
    background-color: #8b0000;
    color: white;
    font-weight: bold;
    font-size: 10px;
    padding: 0 6px;
}
.text-pre-wrap{
    white-space: pre-wrap;
}