﻿import { SearchSelectComponent } from '../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
export class EditSalesOrderMainInfoManager {
    constructor({
        salesOrderId,
        companyId,
        globalLoginClientNo,
        successCallback
    }) {
        this.isFirstOpen = true;
        this.salesOrderId = salesOrderId;
        this.companyId = companyId;
        this.globalLoginClientNo = globalLoginClientNo;
        this.successCallback = successCallback;
        this.salesOrdersMainInfo = null;
        this.companySalesInfo = null;
        this.currencyDropdown = null;
        this.supportTeamMemberSearch = null;
        this.$dialog = $("#edit-main-info-dialog");
        this.$form = $("#edit-main-info-form");
        this.$buyerDropdown = this.$form.find("#edit-form-buyer");
        this.$salespersonDropdown = this.$form.find("#edit-form-salesperson");
        this.$additionalSalespersonDropdown = this.$form.find("#edit-form-additional-salesperson");
        this.$divisionSalesDropdown = this.$form.find("#edit-form-division-sales");
        this.$divisionHeaderDropdown = this.$form.find("#edit-form-division-header");
        this.$currencyDropdown = this.$form.find("#edit-form-currency");
        this.$termsDropdown = this.$form.find("#edit-form-terms");
        this.$shipViaDropdown = this.$form.find("#edit-form-ship-via");
        this.$shipToDropdown = this.$form.find("#edit-form-ship-to");
        this.$taxDropdown = this.$form.find("#edit-form-tax");
        this.$incotermsDropdown = this.$form.find("#edit-form-incoterms");
        this.$ogelDropdown = this.$form.find("#edit-form-ogel");
        this.$salesmanPerson2PercentInput = this.$form.find("#edit-form-additional-salesperson-percent");
        this.$shippingCostInput = this.$form.find("#edit-form-shipping-cost");
        this.$freightInput = this.$form.find("#edit-form-freight");
        this.$shippingAccountInput = this.$form.find("#edit-form-shipping-ac");
        this.defaultDecimalValue = "0.00";
        this.isSoHasAnyLine = null;
        
        this.$currencyNameText = this.$form.find('#currency-name-text');
        this.$buyerNameText = this.$form.find('#buyer-name-text');
        this.$shipviaNameText = this.$form.find('#shipvia-name-text');
        this.$termNameText = this.$form.find('#term-name-text');
        this.$shippingCostText = this.$form.find('#shipping-cost-text');
        this.$freightText = this.$form.find('#freight-text');
        this.$taxNameText = this.$form.find('#tax-name-text');

        this.$customerPOInput = $('#edit-form-customer-po');

        this.$isPaidCheckbox = this.$form.find('#edit-form-paid');
        this.$sourceSupplyRequiredCheckbox = this.$form.find('#edit-form-source-supply-required');

        this.$OGELDropDownContainer = this.$form.find("#OGEL-edit-container");

        this.isCurrencyInSameFaimly = null;
        this.isSOAuthorised = null;
        this.oldFreight = null;

        this._shipViaTax = null;
        this._shipViaTaxValue = null;
        this._shipViaIncoterm = null;
        this._shipViaDivisionHeader = null;

        this._shipToDivisionHeader = null;
        this._shipToTax = null;
        this._shipToIncoterm = null;
        this._shipToTaxValue = null;
    }
    
    initialize() {
        this.setupDialog();
        this.isCurrencyInSameFaimly = $("#isCurrencyInSameFaimly").val().toLowerCase() == "true";
        this.isSOAuthorised = $("#isSOAuthorised").val().toLowerCase() == "true";
        this.oldFreight = $("#oldFreight").val() ?? 0;

        
    }
    setupDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "60vw",
            close: () => {
                this.$dialog.find(".form-error-summary").hide();
                this.$dialog.find('.is-invalid').removeClass("is-invalid");
                this.$form[0].reset();
                this.$form.validate().resetForm();
                this.resetSelectedValueDropdowns();
                this.supportTeamMemberSearch.resetSearchSelect(false);
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                    click: async () => {
                        if (this.$form.valid()) {
                            this.$dialog.find(".form-error-summary").hide();
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.$dialog.dialog("setLoading", true);

                            const response = await this.editSalesOrderMainInfo();

                            this.$dialog.dialog("setLoading", false);
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

                            if (!response?.success) {
                                showToast("danger", response.title);
                                return;
                            }
                            this.$dialog.dialog("close");
                            showToast('success', window.localizedStrings.saveChangedMessage);

                            if (this.successCallback) this.successCallback();
                        }
                        else {
                            this.$dialog.find(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                    click: function () {
                        $(this).dialog("close");
                    },
                },
            ]
        });
    }

    openDialog() {
        this.$dialog.dialog("open");
        this.handleDialogOpen();
    }

    async handleDialogOpen() {
        this.$dialog.dialog("setLoading", true);

        if (this.isFirstOpen) {
            this.setupDropDown();
            this.setupInputBehaviours();
            this.setupValidation();
            this.isFirstOpen = false;
        }

        await this.getSalesOrdersMainInfo();
        this.isSoHasAnyLine = this.salesOrdersMainInfo.isSoHasAnyLine;
        await this.getCompanyDefaults();
        if (this.currencyDropdown == null) {
            this.currencyDropdown = this.$currencyDropdown.dropdown({
                serverside: false,
                endpoint: '/lists/buy-currency-by-global-no',
                valueKey: 'currencyId',
                textKey: 'name',
                placeholderValue: "",
                params: {
                    globalLoginClientNo: this.globalLoginClientNo,
                    globalNo: this.companySalesInfo.globalCurrencyNo,
                },
            });
        }
        this.bindingDataToForm();
        this.loadDefaultShipviaInfo(this.salesOrdersMainInfo.shipViaNo, this.salesOrdersMainInfo.shipToNo);
        this.$dialog.dialog("setLoading", false);

        $(this.$dialog)[0].scrollTop = 0;
        this.$form.find('select:visible:first').trigger('focus');
    }

    async getSalesOrdersMainInfo() {
        let response = await GlobalTrader.ApiClient.getAsync(`/orders/sales-order-detail/${this.salesOrderId}/main-info`);
        if (response.success) {
            this.salesOrdersMainInfo = response.data;
        } else {
            console.log(response.errors);
        }
    }

    async getCompanyDefaults() {
        const response = await GlobalTrader.ApiClient.getAsync(`/orders/sales-orders/sales-info`, {
            CompanyId: this.companyId,
        });
        if (response.success) {
            this.companySalesInfo = response.data;
        } else {
            console.log(response.errors);
        }
    }

    bindingDataToForm() {
        this.$form.find("select").each((i, element) => {
            const value = this.salesOrdersMainInfo[element.name] ?? "";
            $(element).dropdown("select", value.toString());
        })

        this.$form.find("textarea").each((i, element) => {
            const value = this.salesOrdersMainInfo[element.name];
            $(element).val(GlobalTrader.StringHelper.setCleanTextValue(value)).trigger('change');;
        })

        this.$form.find("input[type=text]").each((i, element) => {
            const value = this.salesOrdersMainInfo[element.name];
            $(element).val(value);
        })

        this.$form.find("span[data-field]").each((i, element) => {
            const fieldName = $(element).data("field");
            const value = this.salesOrdersMainInfo[fieldName];
            $(element).html(value);
        })

        this.$form.find("input[type=checkbox].form-check-input").each((i, element) => {
            $(element).prop("checked", this.getPropertyCaseInsensitive(this.salesOrdersMainInfo, element.name)).trigger("change");
        })

        if (this.salesOrdersMainInfo.supportTeamMemberNo && this.salesOrdersMainInfo.supportTeamMemberName) {
            this.supportTeamMemberSearch.selectItem({
                label: this.salesOrdersMainInfo.supportTeamMemberName,
                value: this.salesOrdersMainInfo.supportTeamMemberNo,
            })
        }
        $("#edit-form-currency-freight").text(this.salesOrdersMainInfo.currencyCode);
        
        if (this.companySalesInfo.isShippingWaived) {
            $("#edit-form-shipping-waived").removeClass("d-none")
        }

        if (this.salesOrdersMainInfo.ogeL_Required) {
            this.$OGELDropDownContainer.show();
        }
        else if (this.companySalesInfo.ogel) {
            if (this.salesOrdersMainInfo.shipToNo == this.$shipToDropdown.val()) {
                this.$OGELDropDownContainer.show();
            }
        }

        if (this.salesOrdersMainInfo.isOGELDisabled) {
            this.$ogelDropdown.prop("disabled", true);
        } else {
            this.$ogelDropdown.prop("disabled", false);
        }

        const allowEditAfterAuthorisation = !this.isSOAuthorised || (this.isSOAuthorised && salesOrderMainInfoPermission.canEditFieldsAfterAuthorisation);
        this.allowEditingCurrency(salesOrderMainInfoPermission.canEditCurrencyAndTerms && this.isCurrencyInSameFaimly);
        this.allowEditingIsPaid(salesOrderMainInfoPermission.canEditPaid && allowEditAfterAuthorisation);
        this.allowEditingBuyer(allowEditAfterAuthorisation);
        this.allowEditingShipvia(salesOrderMainInfoPermission.canEditShippingCosts && allowEditAfterAuthorisation);
        this.allowEditingTerm(salesOrderMainInfoPermission.canEditCurrencyAndTerms && allowEditAfterAuthorisation);
        this.allowEditingShippingCostAndFreight(salesOrderMainInfoPermission.canEditShippingCosts && allowEditAfterAuthorisation);
        this.allowEditingTax(salesOrderMainInfoPermission.canEditTax);
        this.allowEditingSourceSupplyRequired(salesOrderMainInfoPermission.canEditAS9120 && !this.isSoHasAnyLine);
        this.showSourceSupplyRequiredMessage(salesOrderMainInfoPermission.canEditAS9120 && this.isSoHasAnyLine);
    }

    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    setupDropDown() {
        this.$buyerDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/contact/${this.companyId}`,
            valueKey: 'contactId',
            textKey: 'contactName',
            placeholderValue: "",
        });

        this.$salespersonDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/employee',
            valueKey: 'loginId',
            textKey: 'employeeName',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
        });

        this.$additionalSalespersonDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/employee',
            valueKey: 'loginId',
            textKey: 'employeeName',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
            onSelected: (data, selectedSalesman) => {
                if (!data) return;
                if (selectedSalesman && selectedSalesman > 0) {
                    const currentSalesman2Percent = this.$salesmanPerson2PercentInput.val();
                    if (currentSalesman2Percent <= 0) {
                        this.$salesmanPerson2PercentInput.val(50);
                    }
                    this.$form.find('#Salesman2Percent-required').show();
                } else {
                    this.$salesmanPerson2PercentInput.val(0);
                    this.$form.find('#Salesman2Percent-required').hide();
                }
            },
        });

        this.$divisionSalesDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/divisions',
            valueKey: 'divisionId',
            textKey: 'divisionName',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
        });

        this.$divisionHeaderDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/divisions',
            valueKey: 'divisionId',
            textKey: 'divisionName',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
        });

        this.$termsDropdown.dropdown({
            serverside: false,
            endpoint: '/terms/dropdown-terms',
            valueKey: 'id',
            textKey: 'name',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
        });

        this.$shipViaDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/sell-ship-via-for-client',
            valueKey: 'id',
            textKey: 'name',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo,
            }
        });

        this.$shipToDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/address-drop-down/${this.companyId}`,
            valueKey: 'id',
            textKey: 'name',
            placeholderValue: "",
        });

        this.$taxDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/taxes`,
            valueKey: 'taxId',
            textKey: 'taxName',
            placeholderValue: "",
            params: {
                globalLoginClientNo: this.globalLoginClientNo
            },
        });

        this.$incotermsDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/incoterms`,
            valueKey: 'incotermId',
            textKey: 'name',
            placeholderValue: "",
        });
        this.$ogelDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/ogel-required-dropdown`,
            valueKey: 'id',
            textKey: 'name',
            placeholderValue: "",
            placeholder: null
        });
    }

    setupInputBehaviours() {
        allowPositiveDecimalInput("#edit-form-shipping-cost", true, 2)
        allowPositiveDecimalInput("#edit-form-freight", true, 2)
        $("#edit-form-shipping-cost").on('blur', () => {
            const value = this.$shippingCostInput.val();
            if (!value || value === '0') {
                this.$shippingCostInput.val(this.defaultDecimalValue);
            }
        });

        $("#edit-form-freight").on('blur', () => {
            const value = this.$freightInput.val();
            if (!value || value === '0') {
                this.$freightInput.val(this.defaultDecimalValue);
            }
        });

        this.$salesmanPerson2PercentInput.on('blur', function () {
            let val = this.value.trim();
            if (val.length > 1) {
                val = val.replace(/^0+/, '') || '0'; // Remove leading zeros unless it's all zeros
            }

            let numericVal = Number(val);
            if (isNaN(numericVal) || numericVal < 0) {
                numericVal = 0;
            } else if (numericVal > 100) {
                numericVal = 100;
            }
            $(this).val(numericVal);
        });
        this.$salesmanPerson2PercentInput.on('input', function () {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        this.supportTeamMemberSearch = new SearchSelectComponent(
            "edit-form-support-team-member-search",
            "edit-form-support-team-member-no",
            "single",
            "keyword",
            "/orders/customer-requirements/auto-search-sale-persion",
            2
        );

        this.$shipViaDropdown.change( () => {
            this.onSelectShipVia()
        });

        this.$shipToDropdown.change(() => {
            this.onSelectShipTo();
        });
        this.$salespersonDropdown.change(() => {
            this.onSelectSaleman();
        });

        this.$customerPOInput.on("blur", (e) => {
            const value = $(e.target).val()
            this.$customerPOInput.val(value.toUpperCase());
        })
    }

    async loadDefaultShipviaInfo(shipViaNoSelected, selectedShipToId) {
        const shipviaResult = await GlobalTrader.ApiClient.getAsync(
            `/setup/company-settings/shipping-methods/${shipViaNoSelected}/sales-order`,
            {
                SOCurrencyNo: this.$currencyDropdown.dropdown("selectedValue"),
                SODate: this.salesOrdersMainInfo.dateOrderedDesc
            }
        );

        if (shipviaResult.success) {
            this._shipViaTax = shipviaResult.data.taxNo;
            this._shipViaIncoterm = shipviaResult.data.incotermNo;
            this._shipViaDivisionHeader = shipviaResult.data.divisionHeaderNo;
            this._shipViaTaxValue = shipviaResult.data.taxName;
        }

        const shipToResult = await GlobalTrader.ApiClient.getAsync(`/orders/sales-orders/shipping-address/${selectedShipToId}`);
        if (shipToResult?.success) {
            this._shipToTax = shipToResult.data.taxbyAddress;
            this._shipToTaxValue = shipToResult.data.taxName;
            this._shipToIncoterm = shipToResult.data.incotermNo;
            this._shipToDivisionHeader = shipToResult.data.divisionHeaderNo;
        }
    }

    allowEditingCurrency(isAllow = true) {
        if (isAllow) {
            $("#edit-form-currency-container").show();
            this.$currencyNameText.hide();
        } else {
            $("#edit-form-currency-container").hide();
            this.$currencyNameText.text(this.salesOrdersMainInfo.currency);
            this.$currencyNameText.show();
        }
    }

    allowEditingBuyer(isAllow = true) {
        if (isAllow) {
            $("#edit-form-buyer-container").show();
            this.$buyerNameText.hide();
        } else {
            $("#edit-form-buyer-container").hide();
            this.$buyerNameText.text(this.salesOrdersMainInfo.contactName);
            this.$buyerNameText.show();
        }
    }

    allowEditingShipvia(isAllow = true) {
        if (isAllow) {
            $("#edit-form-ship-via-container").show();
            this.$shipviaNameText.hide();
        } else {
            $("#edit-form-ship-via-container").hide();
            this.$shipviaNameText.text(this.salesOrdersMainInfo.shipViaName);
            this.$shipviaNameText.show();
        }
    }

    allowEditingTerm(isAllow = true) {
        if (isAllow) {
            $("#edit-form-term-container").show();
            this.$termNameText.hide();
        } else {
            $("#edit-form-term-container").hide();
            this.$termNameText.text(this.salesOrdersMainInfo.termsName);
            this.$termNameText.show();
        }
    }

    allowEditingTax(isAllow = true) {
        if (isAllow) {
            $("#edit-form-tax-container").show();
            this.$taxNameText.hide();
        } else {
            $("#edit-form-tax-container").hide();
            this.$taxNameText.text(this.salesOrdersMainInfo.taxName);
            this.$taxNameText.show();
        }
    }

    allowEditingShippingCostAndFreight(isAllow = true) {
        if (isAllow) {
            this.$shippingCostInput.show();
            this.$freightInput.show();

            this.$shippingCostText.hide();
            this.$freightText.hide();
        } else {
            this.$shippingCostInput.hide();
            this.$freightInput.hide();

            this.$shippingCostText.text(this.salesOrdersMainInfo.shippingCostVal);
            this.$freightText.text(this.salesOrdersMainInfo.freightVal);
            this.$shippingCostText.show();
            this.$freightText.show();
        }
    }

    allowEditingIsPaid(isAllow) {
        this.$isPaidCheckbox.prop("disabled", !isAllow);
    }

    allowEditingSourceSupplyRequired(isAllow) {
        this.$sourceSupplyRequiredCheckbox.prop("disabled", !isAllow);
    }

    showSourceSupplyRequiredMessage(isShow) {
        if (isShow) {
            $("#as9120Message").show();
        }
        else {
            $("#as9120Message").hide();
        }
    }

    editSalesOrderMainInfo() {
        const formData = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);
        formData.ogeL_Required_Value = this.$ogelDropdown.dropdown("selectedValue") == "1";
        formData.salesPersion = formData.salesPersion == "" ? null : formData.salesPersion;
        return GlobalTrader.ApiClient.putAsync(`/orders/sales-order-detail/${this.salesOrderId}/main-info`, {
            ...formData
        }, {
            "RequestVerificationToken": this.$form.find('input[name="__RequestVerificationToken"]').val()
        });
    }

    async onSelectShipVia() {
        const setDefaultShippingValues = () => {
            this.$shippingCostInput.val(this.defaultDecimalValue);
            this.$freightInput.val(this.defaultDecimalValue);
            this.$shippingCostText.text(this.defaultDecimalValue);
            this.$freightText.text(this.defaultDecimalValue);
        };
        const shipViaNoSelected = this.$shipViaDropdown.val();

        if (!shipViaNoSelected || shipViaNoSelected == '0') {
            setDefaultShippingValues();
            return;
        }
        const shipDataResult = await GlobalTrader.ApiClient.getAsync(
            `/setup/company-settings/shipping-methods/${shipViaNoSelected}/sales-order`,
            {
                SOCurrencyNo: this.$currencyDropdown.dropdown("selectedValue"),
                SODate: this.salesOrdersMainInfo.dateOrderedDesc
            }
        );
        if (!shipDataResult?.success) {
            setDefaultShippingValues();
            return;
        };

        this.$shippingCostInput.val(shipDataResult.data.costStr)
        this.$shippingCostText.text(shipDataResult.data.costStr);
        if (this.companySalesInfo.isShippingWaived) {
            if (confirm("Press OK to set the default freight charge for this new shipping method or press Cancel to leave the freight charge field empty.")) {
                this.$freightInput.val(shipDataResult.data.chargeStr);
                this.$freightText.text(shipDataResult.data.chargeStr);
                alert("Freight charge will be changed.\nRelated Division header, Tax and Incoterms also changed.");
            } else {
                this.$freightInput.val(this.defaultDecimalValue);
                this.$freightText.text(this.defaultDecimalValue);
                alert("Freight charge will be left as is.");
            }
            this._shipViaTax = shipDataResult.data.taxNo;
            this._shipViaIncoterm = shipDataResult.data.incotermNo;
            this._shipViaDivisionHeader = shipDataResult.data.divisionHeaderNo;
            this._shipViaTaxValue = shipDataResult.data.taxName;
        } else {
            this.$freightInput.val(shipDataResult.data.chargeStr);
            this.$freightText.text(shipDataResult.data.chargeStr);
            this._shipViaTax = shipDataResult.data.taxNo;
            this._shipViaIncoterm = shipDataResult.data.incotermNo;
            this._shipViaDivisionHeader = shipDataResult.data.divisionHeaderNo;
            this._shipViaTaxValue = shipDataResult.data.taxName;
            alert("Freight charge and shipping cost changed.\nRelated Division header, Tax and Incoterms also changed.");
        }
        this.setDivHdTaxIncData();
    }

    setDivHdTaxIncData() {
        this.$incotermsDropdown.dropdown("select", this._shipViaIncoterm > 0 ? this._shipViaIncoterm : this._shipToIncoterm);
        this.$taxNameText.text(this._shipViaTax > 0 ? this._shipViaTaxValue : this._shipToTaxValue);
        this.$taxDropdown.dropdown("select", this._shipViaTax > 0 ? this._shipViaTax : this._shipToTax);
        this.$divisionHeaderDropdown.dropdown("select", this._shipViaDivisionHeader > 0 ? this._shipViaDivisionHeader : this._shipToDivisionHeader);
    }

    async onSelectShipTo() {
        const selectedShipToId = this.$shipToDropdown.val();
        if (!selectedShipToId || selectedShipToId == '0') {
            this.$taxDropdown.dropdown("reset");
            this.$incotermsDropdown.dropdown("reset");
            this.showOGELDropdown(false);
            return;
        }

        const shipToResult = await GlobalTrader.ApiClient.getAsync(`/orders/sales-orders/shipping-address/${selectedShipToId}`);
        if (!shipToResult?.success) return;

        this._shipToTax = shipToResult.data.taxbyAddress;
        this._shipToTaxValue = shipToResult.data.taxName;
        this._shipToIncoterm = shipToResult.data.incotermNo;
        this._shipToDivisionHeader = shipToResult.data.divisionHeaderNo;
        this.setDivHdTaxIncData();

        this.$shippingAccountInput.val(shipToResult.data.shipViaAccount);

        this.showOGELDropdown(shipToResult.data.ogel);
    }

    showOGELDropdown(isAllow = true) {
        if (isAllow) {
            this.$ogelDropdown.dropdown("select", '1');
            this.$OGELDropDownContainer.show();
        } else {
            this.$ogelDropdown.dropdown("select", '');
            this.$OGELDropDownContainer.hide();
        }
    }

    async onSelectSaleman() {
        const salesmanId = this.$salespersonDropdown.val();
        if (!salesmanId || salesmanId == '0') return;

        const salesmanInfoResult = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/${salesmanId}`);
        if (salesmanInfoResult.success && salesmanInfoResult.data) {
            this.$divisionSalesDropdown.dropdown("select", salesmanInfoResult.data.divisionNo);
        }
    }

    resetSelectedValueDropdowns() {
        this.$buyerDropdown.dropdown("reset");
        this.$salespersonDropdown.dropdown("reset");
        this.$additionalSalespersonDropdown.dropdown("reset");
        this.$divisionSalesDropdown.dropdown("reset");
        this.$divisionHeaderDropdown.dropdown("reset");
        this.$termsDropdown.dropdown("reset");
        this.$shipViaDropdown.dropdown("reset");
        this.$shipToDropdown.dropdown("reset");
        this.$taxDropdown.dropdown("reset");
        this.$incotermsDropdown.dropdown("reset");
        this.$ogelDropdown.dropdown("reset");
    }

    setupValidation() {
        $.validator.addMethod("notEqualTo", function (value, element, param) {
            return value.trim() == "" || value !== $(param).val();
        }, "");
        $.validator.addMethod("freightValidate", function (value, element, param) {
            return parseFloat(value) <= parseFloat(param) || parseFloat(value) == 0;
        }, "");
        $.validator.addMethod("conditionalMinSalesman2", function (value, element, param) {
            return value !== "" || $(param).val() == '0'
        }, "");

        this.$form.validate({
            ignore: [],
            rules: {
                contactNo: {
                    required: true,
                    min: 1
                },
                salesmanNo: {
                    required: true,
                    min: 1,
                    notEqualTo: "#edit-form-additional-salesperson",
                },
                divisionNo: {
                    required: true,
                    min: 1
                },
                divisionHeaderNo: {
                    required: true,
                    min: 1
                },
                currencyNo: {
                    required: true,
                    min: 1
                },
                termsNo: {
                    required: true,
                    min: 1
                },
                shipViaNo: {
                    required: true,
                    min: 1
                },
                shipToNo: {
                    required: true,
                    min: 1,
                },
                taxNo: {
                    required: true,
                    min: 1,
                },
                incotermNo: {
                    required: true,
                    min: 1,
                },
                customerPO: {
                    required: true,
                    minlength: 3,
                    maxlength: 30
                },
                salesman2Percent: {
                    required: {
                        depends: () => {
                            const selectedAdditionalSalesman = parseInt(this.$additionalSalespersonDropdown.dropdown("selectedValue")) || null;
                            return selectedAdditionalSalesman && selectedAdditionalSalesman > 0;
                        },
                    },
                    maxlength: 3
                },
                freightVal: {
                    freightValidate: {
                        param: this.oldFreight
                    }
                },
                salesman2No: {
                    conditionalMinSalesman2: {
                        param: "#edit-form-additional-salesperson-percent"
                    },
                    notEqualTo: {
                        param: "#edit-form-salesperson",
                        depends: () => {
                            const selectedSalesman = parseInt(this.$salespersonDropdown.dropdown("selectedValue")) || null;
                            return selectedSalesman && selectedSalesman > 0;
                        }
                    }
                },
            },
            messages: {
                freightVal: {
                    freightValidate: "Freight should be less than credit balance of the company"
                },
                contactNo: window.localizedStrings.requiredField,
                salesmanNo: {
                    required: window.localizedStrings.requiredField,
                    notEqualTo: editSalesOrdersLocalizer.sameSalesPersonErrorMsg
                },
                divisionNo: window.localizedStrings.requiredField,
                divisionHeaderNo: window.localizedStrings.requiredField,
                currencyNo: window.localizedStrings.requiredField,
                termsNo: window.localizedStrings.requiredField,
                shipViaNo: window.localizedStrings.requiredField,
                shipToNo: window.localizedStrings.requiredField,
                taxNo: window.localizedStrings.requiredField,
                incotermNo: window.localizedStrings.requiredField,
                customerPO: {
                    minlength: editSalesOrdersLocalizer.customerPOMinLengthErrorMsg,
                },
                salesman2Percent: {
                    required: editSalesOrdersLocalizer.additionalSalespersonPercentRequiredErrorMsg,
                },
                salesman2No: {
                    conditionalMinSalesman2: editSalesOrdersLocalizer.additionalSalespersonRequiredErrorMsg,
                    notEqualTo: editSalesOrdersLocalizer.sameSalesPersonErrorMsg
                }
            },
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
            errorPlacement: function (error, element) {
                const inputName = element.attr("name");
                const inputNamesToPlaceSpecificMessageError = ["freightVal", "shippingCostVal", "salesman2Percent", "contactNo", "salesmanNo", "divisionNo", "divisionHeaderNo", "currencyNo", "termsNo", "shipViaNo", "shipToNo", "taxNo", "incotermNo", "salesman2No", "customerPO"];
                if (inputNamesToPlaceSpecificMessageError.includes(inputName)) {
                    error.appendTo(element.parent().parent());
                } 
                else {
                    error.insertAfter(element.parent());
                }
            },
        });
    }
}