﻿@page

@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.Details
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable;
@using GlobalTrader2.SharedUI.Helper
@using Microsoft.Extensions.Localization
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject SessionManager _sessionManager
@inject SettingManager _settingManager
@{

    ViewData["Title"] = Model.Detail.POQuoteNumber;
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

<div id="price-request-details-container" class="mb-3 page-content-container">
    <div class="d-flex justify-content-between align-items-start mt-2">
        <div>
            <h2 class="page-primary-title m-0">Price Request @Model.Detail.POQuoteNumber</h2>
            <span class="status-text">
                <b class="me-2">Status</b> <span id="bom-details-status">@Model.Detail.PRStatus</span>
            </span>
        </div>
        @* <span class="flex-shrink-0">
            <a class="btn btn-primary" id="add-new-hub" href="/Orders/HUBRFQ/AddNewHUBRFQ">
                <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                <span class="lh-base">@_localizer["AddNewHUBRFQ"]</span>
            </a>
        </span> *@
    </div>
    <div class="mb-3" id="main-information-wrapper">
        <div id="main-information-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Main Information</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    @* @if (Model.ShowEditBomButton)
                    {
                        <button class="btn btn-primary" id="main-information-edit-btn" title="Edit">
                            <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Edit"]</span>
                        </button>
                    }*@
                </span>
            </h3>

            <div id="main-information-content" class="@contentClasses">
                <div class="form-error-summary d-none mb-2 align-items-center">
                    <img src="/img/icons/x-octagon.svg" alt="X-icon">
                    <p class="m-0"></p>
                </div>
                <div class="col-6">
                    <div class="row">
                        <label for="divisionName" class="col-2 form-label fw-bold">Division</label>
                        <span name="divisionName" type="div" class="col-10 text-break" data-bind-name="divisionName"></span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="row">
                        <label for="notes" class="col-2 form-label fw-bold">Notes</label>
                        <span name="notes" type="div" class="col-10 text-break" data-bind-name="notes"></span>
                    </div>
                    <div class="row">
                        <label for="buyer" class="col-2 form-label fw-bold">Buyers</label>
                        <span name="buyer" type="div" class="col-10 text-break" data-bind-name="salesPersonName"></span>
                    </div>
                </div>
                <div class="text-end">
                    <i name="last-updated" data-bind-name="lastUpdatedByInText" type="div"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3" id="hurfq-lines-wrapper">
        <div id="hurfq-lines-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">HUBRFQ Lines</span>
            </h3>

            <div id="hurfq-lines-content" class="@contentClasses">
                <table id="hurfq-lines-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="mb-3" id="price-request-quote-wrapper">
        <div id="price-request-quote-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Price Request Quotes</span>
            </h3>

            <div id="price-request-quote-content" class="@contentClasses">
                <table id="price-request-quote-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="mb-3" id="uploaded-document-wrapper">  
        <div id="uploaded-document-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Uploaded Documents</span>
            </h3>
        </div>
    </div>

    <div class="mb-3" id="log-wrapper">
        <div id="log-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Log</span>
            </h3>

            <div id="log-content" class="@contentClasses">
                <table id="log-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    var stateValue = {
        id: '@Model.PoQuoteId',
    }

    const localizedTitles = {
        noData: "@_localizer["NoData"]"
    }
</script>
@section Scripts {
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
   
    <script src="@_settingManager.GetCdnUrl("/lib/jquery-validation/dist/jquery.validate.js")"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/dropdown-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script src="/js/modules/orders/purchase-quote/details/purchase-quote-detail.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="/dist/js/orders-purchase-quote-detail.bundle.js" type="module" asp-append-version="true"></script>
    </environment>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/components/base/search-table-page-base.js")" asp-append-version="true"></script>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>    
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
}
