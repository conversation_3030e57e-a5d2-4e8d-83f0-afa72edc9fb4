@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.Core.Constants;
@using Microsoft.Extensions.Hosting

@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IConfiguration _configuration
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment Env

@if (Env.IsDevelopment())
{
    WebResourceManager.AddScriptModule("/js/modules/orders/hubrfq/containers/import-sourcing.js");
}
else
{
    WebResourceManager.AddScriptModule("/dist/js/orders-import-sourcing.bundle.js");
}

<div class="dialog-container p-2 d-none" id="hubrfq-import-sourcing-result-dialog" title="@_localizer["HUBRFQ Items"]">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5 id="hubrfq-import-sourcing-result-title" class="text-uppercase">@_localizer["Import Sourcing Results"]</h5>
            <span>
                @_localizer["Import Sourcing Title"]
            </span>
        </div>

        <div class="line mb-2"></div>
    </div>
    <form method="post" id="PPV-BOM-detail-form" class="row common-form">
        @Html.AntiForgeryToken()
        <fieldset>
            <legend>
                @_localizer["CSV Data"]
            </legend>
            <div>
                <div id="hubrfq-import-sourcing-csv-file-uploader"></div>

                <div class="d-flex mt-2 justify-content-between">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" id="hubrfq-import-sourcing-csv-file-reset-btn" disabled>
                            <span class="lh-base">@_commonLocalizer["Reset"]</span>
                        </button>
                        <button class="btn btn-warning" id="hubrfq-import-sourcing-csv-file-display-btn" disabled>
                            <span class="lh-base">@_localizer["Display Raw CSV Data"]</span>
                        </button>
                    </div>
                    <span>
                        @_commonLocalizer["Note"]: @_commonLocalizer.GetString("MaxImportRowsMessage", _configuration.GetSection(AppSettingKeys.ImportData)[AppSettingKeys.ImportDataMaxRows] ?? string.Empty)
                    </span>
                </div>
                <div id="hubrfq-import-display-wrapper" class="d-none">
                    <div id="hubrfq-import-sourcing-correction-wrapper" class="border mt-2">
                    <div class="correction-title">@_localizer["Correction"]</div>
                    <div class="row correction-inputs">
                        <div class="form-control-wrapper align-items-center col-6">
                            <div class="row align-items-center">
                                <label for="hubrfq-import-sourcing-incorrect-mfr-dropdown" class="form-label fw-bold col-3">
                                    <p class="mb-0">
                                        @_localizer["Incorrect MFR"]:
                                    </p>
                                </label>
                                <div class="col-5">
                                    <select id="hubrfq-import-sourcing-incorrect-mfr-dropdown" class="form-control form-select" name="IncorrectMFR" aria-label="Default Select"></select>
                                </div>
                                <div class="col-3">
                                    <p class="m-0 text-danger"><span id="hubrfq-import-sourcing-incorrect-mfr-count"></span> @_localizer["mismatch(es)"]</p>
                                </div>
                            </div>
                        </div>
                        <div class="form-control-wrapper align-items-center col-6">
                            <div class="row align-items-center">
                                <label for="hubrfq-import-sourcing-incorrect-supplier-dropdown" class="form-label fw-bold col-3">
                                    <p class="mb-0">
                                        @_localizer["Incorrect Supplier"]:
                                    </p>
                                </label>
                                <div class="col-5">
                                    <select id="hubrfq-import-sourcing-incorrect-supplier-dropdown" class="form-control form-select" name="IncorrectSupplier" aria-label="Default Select"></select>
                                </div>
                                <div class="col-3">
                                    <p class="m-0 text-danger"><span id="hubrfq-import-sourcing-incorrect-supplier-count"></span> @_localizer["mismatch(es)"]</p>
                                </div>
                            </div>
                        </div>
                        <div class="form-control-wrapper align-items-center col-6">
                            <div class="row align-items-center">
                                <label for="hubrfq-import-sourcing-manufacturer-auto-search" class="form-label fw-bold col-3">
                                    <p class="mb-0">
                                        @_localizer["GT MFR"]:
                                    </p>
                                </label>
                                <div class="col-5">
                                    <div id="hubrfq-import-sourcing-manufacturer-auto-search-wrapper" class="position-relative">
                                        <input type="text" id="hubrfq-import-sourcing-manufacturer-auto-search" class="form-control form-input"
                                            data-search-input="single" data-input-value-id="hubrfq-import-sourcing-manufacturer"
                                            data-api-url="/manufacturers/auto-search" data-api-key-search="keyword"
                                            aria-label="hubrfq-import-sourcing-manufacturer-auto-search" />
                                        <input type="text" id="hubrfq-import-sourcing-manufacturer" name="manufacturer" hidden aria-label="manufacturer" />
                                    </div>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-primary" id="hubrfq-import-sourcing-manufacturer-replace-all-btn" disabled>
                                        <span class="lh-base">@_localizer["Replace All"]</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-control-wrapper align-items-center col-6">
                            <div class="row align-items-center">
                                <label for="dateCode" class="form-label fw-bold col-3">
                                    <p class="mb-0">
                                        @_localizer["GT Supplier"]:
                                    </p>
                                </label>
                                <div class="col-5">
                                    <div id="hubrfq-import-sourcing-supplier-auto-search-wrapper" class="position-relative">
                                        <input type="text" id="hubrfq-import-sourcing-supplier-auto-search" class="form-control form-input"
                                            data-search-input="single" data-input-value-id="hubrfq-import-sourcing-supplier"
                                            data-api-url="/companies/auto-search" data-api-key-search="keyword"
                                            aria-label="hubrfq-import-sourcing-supplier-auto-search" />
                                        <input type="text" id="hubrfq-import-sourcing-supplier" name="supplier" hidden aria-label="supplier" />
                                    </div>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-primary" id="hubrfq-import-sourcing-supplier-replace-all-btn" disabled>
                                        <span class="lh-base">@_localizer["Replace All"]</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    <div class="fst-italic fw-bold my-2">
                    @_localizer["Please see below data to be bulk imported into GT. Please note line: highlighted in red indicate a data mismatch with GT and will require correction before being able to successfully import into GT"]
                </div>
                    <div class="d-flex align-items-center gap-2">
                        <input type="checkbox" class="form-control form-check-input mt-0 p-0" name="hubrfqImportSourcingShowchk" id="hubrfq-import-sourcing-show-mismatched-only" checked>
                        <label for="hubrfq-import-sourcing-show-mismatched-only" class="form-label fw-bold">
                        <p class="mb-0">
                            @_localizer["Show only data row with a data mismatch"]
                        </p>
                    </label>
                </div>
                <div id="hubrfq-import-sourcing-dynamic-box" class="mt-2">
                    <table id="hubrfq-import-sourcing-raw-data-table" class="table simple-table table-border nowrap display">
                        <thead>
                            <tr>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                    <div class="d-flex gap-2 justify-content-center mt-2">
                        <button id="hubrfq-import-save-check-btn" class="btn btn-primary">
                            <span class="lh-base">@_commonLocalizer["Save & Check"]</span>
                        </button>
                        <button id="hubrfq-import-import-btn" class="btn btn-primary">
                            <span class="lh-base">@_commonLocalizer["Import"]</span>
                        </button>
                    </div>
                </div>
            </div>
        </fieldset>
    </form>
</div>

<div id="reset-import-sourcing-dialog" class="dialog-container hidden overflow-hidden" title="@_localizer["Reset_Title"]">
    <div class="dialog-description">
        <span>@_localizer["Reset_Message"]</span>
    </div>
</div>

<div id="unsave-warning-dialog" class="dialog-container hidden overflow-hidden" title="@_localizer["Unsave_Warning_Title"]">
    <div class="dialog-description">
        <span>@_localizer["Unsave_Warning_Message"]</span>
    </div>
</div>

<script>
    var localizedMessages = {
        replaceSuccessfulMsg: '@_localizer["ReplaceSuccessfulMsg"]'
    };
</script>