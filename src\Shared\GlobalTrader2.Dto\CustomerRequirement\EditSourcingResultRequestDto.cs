﻿namespace GlobalTrader2.Dto.CustomerRequirement;

public class EditSourcingResultRequestDto
{
    public string? Part { get; set; }
    public int? ManufacturerNo { get; set; }
    public string? DateCode { get; set; }
    public int? ProductNo { get; set; }
    public int? PackageNo { get; set; }
    public int Quantity { get; set; }
    public double Price { get; set; }
    public int? CurrencyNo { get; set; }
    public int? SupplierNo { get; set; }
    public byte? ROHS { get; set; }
    public int? OfferStatusNo { get; set; }
    public string? Notes { get; set; }
    public int? MSLLevelNo { get; set; }
    public bool PartWatchMatch { get; set; }
    public int PageType { get; set; }
}
