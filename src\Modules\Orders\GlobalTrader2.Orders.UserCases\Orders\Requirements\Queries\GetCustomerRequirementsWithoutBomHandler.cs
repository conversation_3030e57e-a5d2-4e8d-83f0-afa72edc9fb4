﻿using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.Orders.Requirements;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries
{
    public class GetCustomerRequirementsWithoutBomHandler : IRequestHandler<GetCustomerRequirementsWithoutBomQuery, BaseResponse<IEnumerable<CustomerRequirementWithoutBomDto>>>
    {
        private readonly IBaseRepository<CustomerRequirementWithoutBom> _repository;
        private readonly IMapper _mapper;

        public GetCustomerRequirementsWithoutBomHandler(IBaseRepository<CustomerRequirementWithoutBom> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<CustomerRequirementWithoutBomDto>>> Handle(GetCustomerRequirementsWithoutBomQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IEnumerable<CustomerRequirementWithoutBomDto>>();
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@ClientId", SqlDbType.Int) {Value = request.ClientId },
                new SqlParameter("@OrderBy", SqlDbType.Int) {Value = request.OrderBy },
                new SqlParameter("@SortDir", SqlDbType.Int) {Value = request.SortDir },
                new SqlParameter("@PageIndex", SqlDbType.Int) {Value = request.PageIndex },
                new SqlParameter("@PageSize", SqlDbType.Int) {Value = request.PageSize },
                new SqlParameter("@PartSearch", SqlDbType.NVarChar) {Value = request.PartSearch ?? (object)DBNull.Value},
                new SqlParameter("@CMSearch", SqlDbType.NVarChar) {Value = request.CompanySearch ?? (object)DBNull.Value},
                new SqlParameter("@IncludeClosed", SqlDbType.Bit) {Value = request.IncludeClosed},
                new SqlParameter("@CustomerRequirementNoLo", SqlDbType.Int) {Value = request.CustomerRequirementNoLo ?? (object)DBNull.Value},
                new SqlParameter("@CustomerRequirementNoHi", SqlDbType.Int) {Value = request.CustomerRequirementNoHi ?? (object)DBNull.Value},
                new SqlParameter("@ReceivedDateFrom", SqlDbType.DateTime) {Value = request.ReceivedDateFrom ?? (object)DBNull.Value},
                new SqlParameter("@ReceivedDateTo", SqlDbType.DateTime) {Value = request.ReceivedDateTo ?? (object)DBNull.Value},
                new SqlParameter("@BomId", SqlDbType.Int) {Value = request.BomId ?? (object)DBNull.Value},
                new SqlParameter("@BomName", SqlDbType.NVarChar) {Value = request.BomName ?? (object)DBNull.Value},
            };

            var customerRequirements = await _repository.SqlQueryRawAsync(
                $"{StoredProcedures.Itemsearch_CustRequirementWithoutBOM} @ClientId,@OrderBy,@SortDir,@PageIndex,@PageSize,@PartSearch,@CMSearch,@IncludeClosed,@CustomerRequirementNoLo,@CustomerRequirementNoHi,@ReceivedDateFrom,@ReceivedDateTo,@BomId,@BomName", parameters.ToArray()
            );

            response.Success = true;
            response.Data = _mapper.Map<IEnumerable<CustomerRequirementWithoutBomDto>>(customerRequirements);

            return response;
        }
    }
}
