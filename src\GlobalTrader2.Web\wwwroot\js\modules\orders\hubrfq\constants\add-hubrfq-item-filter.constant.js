import { FieldType } from "../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#";
import { NumberType } from "../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";

export const ADD_HUBRFQ_ITEM_FILTER_INPUTS = [
  {
    fieldType: FieldType.NUMBER,
    label: "Requirement No",
    name: "requirementSearch",
    id: "addHubrfqItemRequirementSearch",
    attributes: {
      "data-input-type": "numeric",
      "data-input-format": "int",
      "data-input-min": 0,
      "data-input-max": 2147483647,
      "data-input-type-allow-empty": true,
    },
    extraPros: {
      numberType: NumberType.INT,
    },
    value: "",
  },
  {
     fieldType: FieldType.DATE,
     label: "Date Received From",
     name: "receivedDateFrom",
     id: "addHubrfqItemReceivedDateFrom",
     value: "",
     pairWith: "receivedDateTo",
  },
  {
    fieldType: FieldType.TEXT,
    label: "Part No",
    name: "partSearch",
    id: "addHubrfqItemPartSearch",
    value: "",
  },
  {
    fieldType: FieldType.DATE,
    label: "Date Received To",
    name: "receivedDateTo",
    id: "addHubrfqItemReceivedDateTo",
    value: "",
  },
  {
    fieldType: FieldType.TEXT,
    label: "BOM Name",
    name: "bomNameSearch",
      id: "addHubrfqItemBomNameSearch",
    value: "",
  },
];
