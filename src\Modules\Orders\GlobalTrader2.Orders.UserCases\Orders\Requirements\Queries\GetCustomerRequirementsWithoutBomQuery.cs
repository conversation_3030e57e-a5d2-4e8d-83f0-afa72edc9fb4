﻿
using GlobalTrader2.Dto.Orders.Requirements;

namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries
{
    public class GetCustomerRequirementsWithoutBomQuery : IRequest<BaseResponse<IEnumerable<CustomerRequirementWithoutBomDto>>>
    {
        public int? PageIndex { get; set; }
        public int? PageSize { get; set; }
        public int? OrderBy { get; set; }
        public int? SortDir { get; set; }

        public int? ClientId { get; set; }
        public string? PartSearch { get; set; }
        public string? CompanySearch { get; set; }
        public bool? IncludeClosed { get; set; }
        public int? CustomerRequirementNoLo { get; set; }
        public int? CustomerRequirementNoHi { get; set; }
        public DateTime? ReceivedDateFrom { get; set; }
        public DateTime? ReceivedDateTo { get; set; }
        public int? BomId { get; set; }
        public string? BomName { get; set; }
    }
}
