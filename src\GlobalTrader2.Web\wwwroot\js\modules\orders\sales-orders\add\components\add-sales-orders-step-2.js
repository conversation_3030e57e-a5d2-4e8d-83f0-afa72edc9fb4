﻿import { PageSearchSelectComponent } from '../../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#';
export class AddSalesOrdersStep2 {
    constructor({ companyId, companyName, quoteId, contactId, lineIds }) {
        this.companyId = companyId;
        this.companyName = companyName;
        this.quoteId = quoteId;
        this.contactId = contactId;
        this.lineIds = lineIds;

        this.permission = addSalesOrdersPermissionFromServer;
        this.salesInfoDetails = null;
        this.quoteDetails = null;

        this.$container = $('#add-sales-order-step2-container');
        this.$form = $('#add-sales-order-step2-form');
        this.$saveButton = $('#add-sales-orders-save');
        
        this.apiEndpoint = {
            salesInfoDetails: '/orders/sales-orders/sales-info',
            addSaleOrder: 'orders/sales-orders',
            quoteDetails: () => `/orders/quotes/${this.quoteId}/main-info`,
            //Dropdown endpoint
            contactDropDown: () => `/lists/contact/${this.companyId}`,
            shipToDropDown: () => `/lists/address-drop-down/${this.companyId}`,
            salesmanDropDown: '/lists/employee',
            divisionSalesDropDown: '/lists/divisions/company-details',
            currencyDropDown: '/lists/buy-currency-by-global-no',
            taxDropDown: '/contact/all-companies/addresses/taxes',
            incotermDropDown: '/lists/incoterms',
            termsDropDown: '/terms/dropdown-terms',
            shipViaDropDown: '/lists/sell-ship-via-for-client',
            ogelDropDown: '/lists/ogel-required-dropdown',
        }
        this.defaultDecimalValue = "0.00";
        this.supportTeamMemberSelectSearchControl = null;
        this.$salesman2PercentInput = this.$form.find('#Salesman2Percent');
        this.$shippingCostInput = this.$form.find('input[name="ShippingCost"]');
        this.$freightInput = this.$form.find('input[name="Freight"]');
        this.$shippingAccountInput = this.$form.find('input[name="Account"]');
        this.$customerNoteInput = this.$form.find('textarea[name="Instructions"]');
        this.$internalNotesInput = this.$form.find('textarea[name="Notes"]');
        this.$AS9120Container = this.$form.find('#AS9120-container');
        this.$AS9120CheckboxInput = this.$form.find('input[name="AS9120"]');
        this.$customerPOInput = this.$form.find('input[name="CustomerPO"]');

        this.$customerText = this.$form.find('#customer');
        this.$freightQuoteLabel = this.$form.find('#freight-quote-label');
        this.$shippingWaivedLabel = this.$form.find('#shipping-waived-label');
        this.$freightCurrencyText = this.$form.find('#freight-currency-text');
        this.$termNameText = this.$form.find('#terms-name-text');
        this.$currencyNameText = this.$form.find('#currency-name-text');
        this.$taxNameText = this.$form.find('#tax-name-text');
        //Setup dropdown
        this.$contactDropDown = this.$form.find('#Contact-dropdown');
        this.$salesmanDropDown = this.$form.find('#Salesman-dropdown');
        this.$salesman2DropDown = this.$form.find('#Salesman2-dropdown');
        this.$divisionSalesDropDown = this.$form.find('#DivisionSales-dropdown');
        this.$divisionHeaderDropDown = this.$form.find('#DivisionHeader-dropdown');
        this.$currencyDropDown = this.$form.find('#Currency-dropdown');
        this.$termsDropDown = this.$form.find('#Terms-dropdown');
        this.$taxDropDown = this.$form.find('#Tax-dropdown');
        this.$incotermDropDown = this.$form.find('#Incoterm-dropdown');
        this.$shipViaDropDown = this.$form.find('#ship-via-dropdown');
        this.$shipToDropDown = this.$form.find('#ship-to-dropdown');
        this.$OGELDropDown = this.$form.find('#OGEL-dropdown');
        this.$OGELHiddenInput = this.$form.find('#OGEL-hidden');

        this.$OGELDropDownContainer = this.$form.find('#OGEL-container');
        this.$currencyDropdownContainer = this.$form.find('#currency-dropdown-container');
        this.$termDropdownContainer = this.$form.find('#term-dropdown-container');
        this.$taxDropdownContainer = this.$form.find('#tax-dropdown-container');
        //Private
        this._currencyID = null;
        this._incotermNo = null;
        this._AS6081 = null;

        this._shipViaTax = null;
        this._shipViaIncoterm = null;
        this._shipViaDivisionHeader = null;
        this._shipToDivisionHeader = null;
        this._shipToTax = null;
        this._shipToIncoterm = null;
        this._shipViaTaxValue = null;
        this._shipToTaxValue = null;

        this._isLoadShipViaDropdownFirstTime = true;
        this._isLoadCurrencyDropdownFirstTime = true;
        this._isLoadShipToDropdownFirstTime = true;
        this._isLoadSalesmanDropdownFirstTime = true;
    }


    async initialize() {
        this.setLoading();
        if (this.quoteId > 0) await this.getQuoteAsync();
        await this.getCompanyDefaults();
        await this.setupForm();
        this.bindDefaultDataToForm();
        this.bindDataToFormForQuote(this.quoteDetails);
        this.bindDataToFormForCompanyDefaults(this.salesInfoDetails);
        this.setLoading(false);
        this.$container.find('input, select, textarea')
            .filter(':visible:enabled:not([readonly])')
            .first()
            .trigger("focus");
    }

    async reloadFormDataAsync(data) {
        this.setLoading();
        this.$shipViaDropDown.data("disableTriggerSelectShipVia", "true");
        this.$currencyDropDown.data("disableTriggerSelectShipVia", "true");
        this.$shipToDropDown.data("disableTriggerSelectShipTo", "true");
        this.clearAllFormState();
        this.checkFormInputPermissions();
        if (data) {
            this.companyId = data.companyId;
            this.companyName = data.companyName;
            await this.getCompanyDefaults();
            await this.reloadDropDownAsync();
            this.bindDefaultDataToForm();
            this.bindDataToFormForCompanyDefaults(this.salesInfoDetails);
        }
        this.$shipViaDropDown.data("disableTriggerSelectShipVia", "false");
        this.$currencyDropDown.data("disableTriggerSelectShipVia", "false");
        this.$shipToDropDown.data("disableTriggerSelectShipTo", "false");
        this.setLoading(false);


        this.$container.find('input:visible:enabled:not([readonly])').first().trigger("focus");
    }
    async getQuoteAsync() {
        const result = await GlobalTrader.ApiClient.getAsync(`${this.apiEndpoint.quoteDetails()}`);
        if (result.success) {
            this.quoteDetails = result.data;
            this.companyId = result.data.companyNo;
            this.companyName = result.data.companyName;

            this._currencyID = result.data.currencyNo
            this._incotermNo = result.data.incotermNo;
        } else {
            window.showToast('danger', window.localizedStrings.unexpectedError);
        }
    }
    async getCompanyDefaults() {
        const result = await GlobalTrader.ApiClient.getAsync(`${this.apiEndpoint.salesInfoDetails}`, {
            CompanyId: this.companyId,
            DocCurrencyNo: this._currencyID,
        });
        if (!result.success || !result.data) return;
        this.salesInfoDetails = result.data;
    }
    async setupForm() {
        await this.setupDropDownAsync();
        this.setupInputBehaviours();
        this.setupValidation();
        this.setupButtonEvent();
        this.checkFormInputPermissions();
    }
    async setupDropDownAsync() {
        await Promise.all([
            this.$contactDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.contactDropDown(),
                textKey: 'contactName',
                valueKey: 'contactId',
                placeHolderValue: "",
            }),

            this.$salesmanDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.salesmanDropDown,
                valueKey: 'loginId',
                textKey: 'employeeName',
                placeHolderValue: "",
                onSelected: (data, selectedSalesman) => {
                    const isDisableTriggerSelectedSalesman = this.$salesmanDropDown.data("disableTriggerSelectSalesman") === 'true';
                    if (!data || isDisableTriggerSelectedSalesman) return;

                    if (this._isLoadSalesmanDropdownFirstTime) {
                        this._isLoadSalesmanDropdownFirstTime = false;
                    } else {
                        this.onSelectSaleman(selectedSalesman);
                    }
                },
            }),

            this.$salesman2DropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.salesmanDropDown,
                valueKey: 'loginId',
                textKey: 'employeeName',
                placeHolderValue: "",
                onSelected: (data, selectedSalesman) => {
                    if (!data) return;
                    if (selectedSalesman && selectedSalesman > 0) {
                        const currentSalesman2Percent = this.$salesman2PercentInput.val();
                        if (currentSalesman2Percent <= 0) {
                            this.$salesman2PercentInput.val(50);
                        }
                        this.$form.find('#Salesman2Percent-required').show();
                    } else {
                        this.$salesman2PercentInput.val(0);
                        this.$form.find('#Salesman2Percent-required').hide();
                    }
                },
            }),

            this.$divisionSalesDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.divisionSalesDropDown,
                params: {
                    globalLoginClientNo: null,
                    selectedClientNo: null
                },
                textKey: 'divisionName',
                valueKey: 'divisionId',
                placeHolderValue: "",
            }),

            this.$divisionHeaderDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.divisionSalesDropDown,
                params: {
                    globalLoginClientNo: null,
                    selectedClientNo: null
                },
                textKey: 'divisionName',
                valueKey: 'divisionId',
                placeHolderValue: "",
            }),

            this.$currencyDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.currencyDropDown,
                params: {
                    globalNo: this.salesInfoDetails.globalCurrencyNo,
                    globalLoginClientNo: null,
                    buy: false
                },
                textKey: 'name',
                valueKey: 'currencyId',
                placeHolderValue: "",
                onSelected: (data, selectedCurrencyId) => {
                    const isDisableTriggerSelectedShipVia = this.$currencyDropDown.data("disableTriggerSelectShipVia") === 'true';
                    if (!data || isDisableTriggerSelectedShipVia) return;
                    
                    if (this._isLoadCurrencyDropdownFirstTime) {
                        this._isLoadCurrencyDropdownFirstTime = false;
                    } else {
                        const currencyName = data.find(x => x.currencyId == selectedCurrencyId)?.code || "";
                        this._currencyID = selectedCurrencyId;
                        if (!GlobalTrader.StringHelper.isNullOrWhitespace(currencyName)) {
                            this.$freightCurrencyText.text(currencyName);
                        }
                        this.onSelectShipVia();
                    }
                },
            }),

            this.$termsDropDown.dropdown({
                serverside: false,
                params: {},
                endpoint: this.apiEndpoint.termsDropDown,
                placeHolderValue: "",
            }),

            this.$taxDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.taxDropDown,
                textKey: 'taxName',
                valueKey: 'taxId',
                placeHolderValue: "",
            }),

            this.$incotermDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.incotermDropDown,
                textKey: 'name',
                valueKey: 'incotermId',
                placeHolderValue: "",
            }),

            this.$shipViaDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.shipViaDropDown,
                textKey: 'name',
                valueKey: 'id',
                placeHolderValue: "",
                onSelected: (data) => {
                    const isDisableTriggerSelectedShipVia = this.$shipViaDropDown.data("disableTriggerSelectShipVia") === 'true';
                    if (!data || isDisableTriggerSelectedShipVia) return;
                    if (this._isLoadShipViaDropdownFirstTime) {
                        this._isLoadShipViaDropdownFirstTime = false;
                    } else {
                        this.onSelectShipVia();
                    }
                },
            }),
            this.$shipToDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.shipToDropDown(),
                textKey: 'name',
                valueKey: 'id',
                placeHolderValue: "",
                onSelected: (data) => {
                    const isDisableTriggerSelectedShipTo = this.$shipToDropDown.data("disableTriggerSelectShipTo") === 'true';
                    if (!data || isDisableTriggerSelectedShipTo) return;
                    if (this._isLoadShipToDropdownFirstTime) {
                        this._isLoadShipToDropdownFirstTime = false;
                    } else {
                        this.onSelectShipTo();
                    }
                },
            }),
            this.$OGELDropDown.dropdown({
                serverside: false,
                endpoint: this.apiEndpoint.ogelDropDown,
                textKey: 'name',
                valueKey: 'id',
                placeholder: null,
                onSelected: (data, selectedValue) => {
                    this.$OGELHiddenInput.val(selectedValue || '0');
                },
            }),
        ])

        this.$OGELDropDown.dropdown().parent().removeClass('justify-content-center');
    }
    async reloadDropDownAsync() {
        await Promise.all([
            this.$contactDropDown.dropdown("reload", this.apiEndpoint.contactDropDown()),
            this.$salesmanDropDown.dropdown("reload"),
            this.$salesman2DropDown.dropdown("reload"),
            this.$divisionSalesDropDown.dropdown("reload"),
            this.$divisionHeaderDropDown.dropdown("reload"),
            this.$currencyDropDown.dropdown("reload",
                this.apiEndpoint.currencyDropDown,
                {
                    globalNo: this.salesInfoDetails.globalCurrencyNo,
                    globalLoginClientNo: null,
                    buy: false
                }
            ),
            this.$termsDropDown.dropdown("reload"),
            this.$taxDropDown.dropdown("reload"),
            this.$incotermDropDown.dropdown("reload"),
            this.$shipViaDropDown.dropdown("reload"),
            this.$shipToDropDown.dropdown("reload", this.apiEndpoint.shipToDropDown()),
        ])
    }
    async onSelectShipVia() {
        const setDefaultShippingValues = () => {
            this.$shippingCostInput.val(this.defaultDecimalValue);
            this.$freightInput.val(this.defaultDecimalValue);
        };
        const shipViaNoSelected = this.$shipViaDropDown.val();

        if (!shipViaNoSelected || shipViaNoSelected == '0') {
            setDefaultShippingValues();
            return;
        }
        const shipDataResult = await GlobalTrader.ApiClient.getAsync(
            `/setup/company-settings/shipping-methods/${shipViaNoSelected}/sales-order`,
            {
                SOCurrencyNo: this.$currencyDropDown.val(),
                SODate: this.$form.find('input[name="DateOrdered"]').val()
            }
        );
        if (!shipDataResult?.success) {
            setDefaultShippingValues();
            return;
        };

        this._incotermNo = 0;
        this._shipViaTax = 0;
        this._shipViaIncoterm = 0;
        this._shipViaDivisionHeader = 0;
        this._shipViaTaxValue = "";
        this.$shippingCostInput.val(shipDataResult.data.costStr)

        if (this.salesInfoDetails.isShippingWaived) {
            if (confirm("Press OK to set the default freight charge for this new shipping method or press Cancel to leave the freight charge field empty.")) {
                this.$freightInput.val(shipDataResult.data.chargeStr);
                alert("Freight charge will be changed.\nRelated Division header, Tax and Incoterms also changed.");
            } else {
                this.$freightInput.val(this.defaultDecimalValue);
                alert("Freight charge will be left as is.");
            }
            this._shipViaTax = shipDataResult.data.taxNo;
            this._shipViaIncoterm = shipDataResult.data.incotermNo;
            this._shipViaDivisionHeader = shipDataResult.data.divisionHeaderNo;
            this._shipViaTaxValue = shipDataResult.data.taxName;
        } else {
            this.$freightInput.val(shipDataResult.data.chargeStr);
            this._shipViaTax = shipDataResult.data.taxNo;
            this._shipViaIncoterm = shipDataResult.data.incotermNo;
            this._shipViaDivisionHeader = shipDataResult.data.divisionHeaderNo;
            this._shipViaTaxValue = shipDataResult.data.taxName;
            alert("Freight charge and shipping cost changed.\nRelated Division header, Tax and Incoterms also changed.");
        }
        this.setDivHdTaxIncData();
    }
    async onSelectShipTo() {
        const selectedShipToId = this.$shipToDropDown.val();
        if (!selectedShipToId || selectedShipToId == '0') {
            this.$taxDropDown.dropdown("reset");
            this.$taxNameText.text("");
            this.$incotermDropDown.dropdown("reset");
            this.showOGELDropdown(false);
            return;
        }

        const shipToResult = await GlobalTrader.ApiClient.getAsync(`/orders/sales-orders/shipping-address/${selectedShipToId}`);
        if (!shipToResult?.success) return;

        this._shipToTax = shipToResult.data.taxbyAddress;
        this._shipToTaxValue = shipToResult.data.taxName;
        this._shipToIncoterm = shipToResult.data.incotermNo;
        this._shipToDivisionHeader = shipToResult.data.divisionHeaderNo
        this.setDivHdTaxIncData();

        this.$shippingAccountInput.val(shipToResult.data.shipViaAccount);

        this.showOGELDropdown(shipToResult.data.ogel);
    }
    async onSelectSaleman() {
        const salesmanId = this.$salesmanDropDown.val();
        if (!salesmanId || salesmanId == '0') return;

        const salesmanInfoResult = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/${salesmanId}`);
        if (salesmanInfoResult.success && salesmanInfoResult.data) {
            this.$divisionSalesDropDown.dropdown("select", salesmanInfoResult.data.divisionNo);
        }
    }
    async handleSubmitForm() {
        if (this.$form.valid()) {
            this.setLoading();
            this.$container.find(".form-error-summary").hide();
            this.enableSaveButton(false);

            if (this.$form.find("input[name='SupportTeamMemberNo']").val() === "") {
                this.$form.find("input[name='SupportTeamMemberNo']").val(0);
            };
            this.$form.find('input[name="CompanyNo"]').val(this.companyId);
            this.$form.find('input[name="QuoteId"]').val(this.quoteId);
            this.$form.find('input[name="QuoteLineIDsStr"]').val(this.lineIds);

            const result = await GlobalTrader.FormHelper.sendPostRequestAsync(this.apiEndpoint.addSaleOrder, this.$form);
            this.setLoading(false);
            if (!result?.success) {
                this.enableSaveButton();
                return;
            };
            this.$form.trigger("saveSuccess", {
                companyId: this.companyId,
                salesOrderId: result.data
            });
        } else {
            this.$container.find(".form-error-summary").show();
        }
    }

    bindDefaultDataToForm() {
        if (!this.quoteId || parseInt(this.quoteId) <= 0) {
            this.$divisionSalesDropDown.dropdown("select", window.sessionKeyValue.loginDivisionId);
            this.$divisionHeaderDropDown.dropdown("select", window.sessionKeyValue.loginDivisionId);
            this.$customerText.text(this.companyName);
            if (this.contactId > 0) this.$contactDropDown.dropdown("select", this.contactId);
        }
        this.$salesmanDropDown.dropdown("select", window.sessionKeyValue.loginId);
    }
    bindDataToFormForCompanyDefaults(data) {
        let freightVal = this.defaultDecimalValue;
        if (data.isShippingWaived) {
            this.$shippingWaivedLabel.show();
        } else {
            this.$shippingWaivedLabel.hide();
            freightVal = !GlobalTrader.StringHelper.isNullOrWhitespace(data.freightInDocCurrency) ? data.freightInDocCurrency : data.freightVal;
        }
        this.$freightInput.val(freightVal);
        this.$shippingCostInput.val(data.shippingCostVal);
        this.$shipViaDropDown.dropdown("select", data.shipViaDefaultSPAdrShipViaNo);
        this.$taxDropDown.dropdown("select", data.taxbyAddress);
        this.$taxNameText.text(data.taxValue);
        this.$shipToDropDown.dropdown("select", data.shipToAddressNo);
        this.$form.find('#BillingAddress').text(data.billToAddress);
        this.$shippingAccountInput.val(data.shippingAccountNo);
        this.$customerText.text(this.companyName);

        this.$form.find('input[name="DateOrdered"]').datepicker2('setToDay');

        //override permissions for editing currency and tax if the company does not have them properly set
        if (!data.currencyNo) this.allowEditingCurrency();
        if (!data.termsNo) this.allowEditingTax();
        //don't set fields if come from a Quote
        if (!this.quoteId || this.quoteId <= 0) {
            this.$currencyDropDown.dropdown("select", data.currencyNo);
            this.$currencyNameText.text(data.currency);
            this.$termsDropDown.dropdown("select", data.termsNo);
            this.$termNameText.text(data.termsName);
            this.$freightCurrencyText.text(data.currencyCode);
            this.$AS9120CheckboxInput.prop('checked', data.traceability);
            //don't set contact if we have an explicitly passed one
            if (!this.contactId || this.contactId <= 0) this.$contactDropDown.dropdown("select", data.contactNo);
        }

        this.setPrivateValues(data);
        this.setDivHdTaxIncData();
        this.showOGELDropdown(data.ogel);
    }
    bindDataToFormForQuote(quoteDetail) {
        if (!quoteDetail) return;

        this.$contactDropDown.dropdown("select", this.contactId)
        this.$salesmanDropDown.dropdown("select", quoteDetail.salesman);
        this.$divisionSalesDropDown.dropdown("select", quoteDetail.divisionNo);
        this.$termsDropDown.dropdown("select", quoteDetail.termsNo);
        this.$termNameText.text(quoteDetail.termsName);
        this.$currencyDropDown.dropdown("select", quoteDetail.currencyNo);
        this.$currencyNameText.text(quoteDetail.currencyName);
        this.$incotermDropDown.dropdown("select", quoteDetail.incotermNo);
        this.$divisionHeaderDropDown.dropdown("select", quoteDetail.divisionHeaderNo);

        this.$freightCurrencyText.text(quoteDetail.currencyCode);
        this.$freightQuoteLabel
            .text(`${addSalesOrdersLocalizer.quoted} ${quoteDetail.freightStr}`)
            .show();
        this.$customerNoteInput
            .val(GlobalTrader.StringHelper.setCleanTextValue(quoteDetail.notes))
            .trigger('change');
        this.$internalNotesInput
            .val(GlobalTrader.StringHelper.setCleanTextValue(quoteDetail.instructions))
            .trigger('change');
        this.$AS9120CheckboxInput.prop('checked', quoteDetail.aS9120);
        this.$AS9120Container.hide();

        this.$form.find('input[name="AS6081"]').prop("checked", quoteDetail.aS6081);
    }
    setupInputBehaviours() {
        this.$form.find('input[name="DateOrdered"]').datepicker2();
        allowPositiveDecimalInput('input[name="Freight"]', true, 2)
        allowPositiveDecimalInput('input[name="ShippingCost"]', true, 2)
        $("input[name='Freight'], input[name='ShippingCost']").on('blur', () => {
            if (!this.value || this.value === '0') {
                $(this).val(this.defaultDecimalValue);
            }
        });

        this.$salesman2PercentInput.on('blur', function () {
            let val = this.value.trim();
            if (val.length > 1) {
                val = val.replace(/^0+/, '') || '0'; // Remove leading zeros unless it's all zeros
            }

            let numericVal = Number(val);
            if (isNaN(numericVal) || numericVal < 0) {
                numericVal = 0;
            } else if (numericVal > 100) {
                numericVal = 100;
            }
            $(this).val(numericVal);
        });
        this.$salesman2PercentInput.on('input', function () {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        this.supportTeamMemberSelectSearchControl = new PageSearchSelectComponent('SupportTeamMember', 'SupportTeamMemberNo', 'single', 'keyword', '/orders/customer-requirements/auto-search-sale-persion', 2);

        this.$customerPOInput.on("blur", (e) => {
            const value = $(e.target).val()
            this.$customerPOInput.val(value.toUpperCase());
        })
    }
    setupValidation() {
        $.validator.addMethod("notEqualToDropdown", function (value, element, param) {
            if (!value || value == 0) {
                return true;
            }
            return value !== $(param).val();
        }, "");

        this.$form.validate({
            ignore: [],
            rules: {
                ContactNo: {
                    required: true,
                    min: 1
                },
                Salesman: {
                    required: true,
                    min: 1,
                    notEqualToDropdown: "#Salesman2-dropdown",
                },
                DivisionNo: {
                    required: true,
                    min: 1
                },
                DivisionHeaderNo: {
                    required: true,
                    min: 1
                },
                CurrencyNo: {
                    required: true,
                    min: 1
                },
                DateOrdered: {
                    required: true,
                },
                TermsNo: {
                    required: true,
                    min: 1
                },
                ShipViaNo: {
                    required: true,
                    min: 1
                },
                ShipToAddressNo: {
                    required: true,
                    min: 1,
                },
                TaxNo: {
                    required: true,
                    min: 1,
                },
                IncotermNo: {
                    required: true,
                    min: 1,
                },
                CustomerPO: {
                    required: true,
                    minlength: 3,
                    maxlength: 30
                },
                Salesman2Percent: {
                    required: {
                        depends: () => {
                            const selectedAdditionalSalesman = parseInt(this.$salesman2DropDown.dropdown("selectedValue")) || null;
                            return selectedAdditionalSalesman && selectedAdditionalSalesman > 0;
                        },
                    },
                    maxlength: 3
                },
                Salesman2: {
                    min: {
                        param: 1,
                        depends: () => {
                            const additionalSalesmanPercent = parseInt(this.$salesman2PercentInput.val());
                            return additionalSalesmanPercent > 0;
                        }
                    },
                    notEqualToDropdown: "#Salesman-dropdown",
                },
                Account: {
                    maxlength: 50
                }
            },
            messages: {
                ContactNo: window.localizedStrings.requiredField,
                Salesman: {
                    min: window.localizedStrings.requiredField,
                    required: window.localizedStrings.requiredField,
                    notEqualToDropdown: addSalesOrdersLocalizer.sameSalesPersonErrorMsg
                },
                DivisionNo: window.localizedStrings.requiredField,
                DivisionHeaderNo: window.localizedStrings.requiredField,
                CurrencyNo: window.localizedStrings.requiredField,
                TermsNo: window.localizedStrings.requiredField,
                ShipViaNo: window.localizedStrings.requiredField,
                ShipToAddressNo: window.localizedStrings.requiredField,
                TaxNo: window.localizedStrings.requiredField,
                IncotermNo: window.localizedStrings.requiredField,
                CustomerPO: {
                    minlength: addSalesOrdersLocalizer.customerPOMinLengthErrorMsg,
                },
                Salesman2Percent: {
                    required: addSalesOrdersLocalizer.additionalSalespersonPercentRequiredErrorMsg,
                },
                Salesman2: {
                    min: addSalesOrdersLocalizer.additionalSalespersonRequiredErrorMsg,
                    notEqualToDropdown: addSalesOrdersLocalizer.sameSalesPersonErrorMsg
                },
                DateOrdered: window.localizedStrings.requiredField,
            },
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
            errorPlacement: function (error, element) {
                const inputName = element.attr("name");
                const inputNamesToPlaceSpecificMessageError = ["Freight", "ShippingCost", "Salesman2Percent", "ContactNo", "Salesman", "DivisionNo", "DivisionHeaderNo", "CurrencyNo", "TermsNo", "ShipViaNo", "ShipToAddressNo", "TaxNo", "IncotermNo", "Salesman2", "DateOrdered", "CustomerPO"];
                if (inputNamesToPlaceSpecificMessageError.includes(inputName)) {
                    if (inputName == 'CustomerPO') {
                        error.appendTo(element.parent().parent().parent())
                    }
                    else {
                        error.appendTo(element.parent().parent());
                    }                        ;
                } else {
                    error.insertAfter(element);
                }
            },
        });
    }
    setupButtonEvent() {
        this.$saveButton.on("click", async () => {
            await this.handleSubmitForm();
        })
    }
    setPrivateValues(data) {
        this._currencyID = data.currencyNo;
        this._shipViaTax = data.shipViaTaxbyAddress;
        this._shipViaIncoterm = data.shipViaIncotermNo;
        this._shipViaDivisionHeader = data.shipViaDivisionHeaderNo;
        this._shipToDivisionHeader = data.shipToDivisionHeaderNo;
        this._shipToTax = data.shipToTaxbyAddress;
        this._shipToIncoterm = data.shipToIncotermNo;
        this._shipViaTaxValue = data.shipViaTaxValue;
        this._shipToTaxValue = data.shipToTaxValue;
    }
    setDivHdTaxIncData() {
        let incotermValue = null;
        if (this._incotermNo > 0) {
            incotermValue = this._incotermNo;
        } else {
            incotermValue = this._shipViaIncoterm > 0 ? this._shipViaIncoterm : this._shipToIncoterm;
        }
        this.$incotermDropDown.dropdown("select", incotermValue);

        this.$taxDropDown.dropdown("select", this._shipViaTax > 0 ? this._shipViaTax : this._shipToTax);
        this.$taxNameText.text(this._shipViaTax > 0 ? this._shipViaTaxValue : this._shipToTaxValue);
        this.$divisionHeaderDropDown.dropdown("select", this._shipViaDivisionHeader > 0 ? this._shipViaDivisionHeader : this._shipToDivisionHeader);
    }
    enableSaveButton(isEnable = true) {
        this.$saveButton.prop('disabled', !isEnable);
    }
    allowEditingCurrency(isAllow = true) {
        if (isAllow) {
            this.$currencyDropdownContainer.show();
            this.$currencyNameText.hide();
        } else {
            this.$currencyDropdownContainer.hide();
            this.$currencyNameText.show();
        }
    }
    allowEditingTerm(isAllow = true) {
        if (isAllow) {
            this.$termDropdownContainer.show();
            this.$termNameText.hide();
        } else {
            this.$termDropdownContainer.hide();
            this.$termNameText.show();
        }
    }
    allowEditingTax(isAllow = true) {
        if (isAllow) {
            this.$taxDropdownContainer.show();
            this.$taxNameText.hide();
        } else {
            this.$taxDropdownContainer.hide();
            this.$taxNameText.show();
        }
    }
    clearAllFormState() {
        this.$form[0].reset();
        this.$form.validate().resetForm();
        this.$form.find('.is-invalid').removeClass("is-invalid");
        this.$container.find(".form-error-summary").hide();
        this.supportTeamMemberSelectSearchControl.resetSearchSelect(false);
        this.resetSelectedValueDropdowns();
        this.$OGELHiddenInput.val('0');
    }
    resetSelectedValueDropdowns() {
        this.$contactDropDown.dropdown("reset");
        this.$salesmanDropDown.dropdown("reset");
        this.$salesman2DropDown.dropdown("reset");
        this.$divisionSalesDropDown.dropdown("reset");
        this.$divisionHeaderDropDown.dropdown("reset");
        this.$currencyDropDown.dropdown("reset");
        this.$termsDropDown.dropdown("reset");
        this.$taxDropDown.dropdown("reset");
        this.$incotermDropDown.dropdown("reset");
        this.$shipViaDropDown.dropdown("reset");
        this.$shipToDropDown.dropdown("reset");
    }
    showOGELDropdown(isAllow = true) {
        if (isAllow) {
            this.$OGELDropDownContainer.show();
            this.$OGELDropDown.dropdown("select",'1');
            this.$OGELHiddenInput.val('1');
        } else {
            this.$OGELDropDownContainer.hide();
            this.$OGELDropDown.dropdown("select",'0');
            this.$OGELHiddenInput.val('0');
        }
    }
    setLoading(isLoading = true) {
        if (isLoading) {
            $('#add-new-sales-order-step-2-loading-content').show();
            this.enableSaveButton(false)
            this.$form.hide();
        } else {
            $('#add-new-sales-order-step-2-loading-content').hide();
            this.enableSaveButton();
            this.$form.show();
        }
    }
    checkFormInputPermissions() {
        this.allowEditingCurrency(this.permission.canEditCurrencyAndTerms);
        this.allowEditingTerm(this.permission.canEditCurrencyAndTerms);
        this.allowEditingTax(this.permission.canEditTax);
    }
}