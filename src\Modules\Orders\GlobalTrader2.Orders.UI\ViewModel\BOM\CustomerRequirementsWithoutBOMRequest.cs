﻿using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.Converters.DateTime;
using GlobalTrader2.SharedUI.JsonConverters.String;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Orders.UI.ViewModel.BOM
{
    public class CustomerRequirementsWithoutBomRequest : PageRequestBase
    {
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? PartSearch { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? CompanySearch { get; set; }
        public bool? IncludeClosed { get; set; }
        public int? CustomerRequirementNoLo { get; set; }
        public int? CustomerRequirementNoHi { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? ReceivedDateFrom { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? ReceivedDateTo { get; set; }
        public int? BomId { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? BomName { get; set; }

    }
}
