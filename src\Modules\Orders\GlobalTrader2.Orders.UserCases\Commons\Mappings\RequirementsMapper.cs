using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.PartWatch;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.PrintEmailLog.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.SalesInfo.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.IHSEccn.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartLinesRL.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.RestrictedManufacturer.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.RLPart.Queries.Dtos;
using System.Globalization;

namespace GlobalTrader2.Orders.UserCases.Commons.Mappings;

public class RequirementsMapper : AutoMapper.Profile
{
    public RequirementsMapper()
    {
        CreateMap<CompanySearchItem, CompanySearchItemDto>().ReverseMap();

        CreateMap<SalesInfo, SalesInfoDto>().ReverseMap();

        CreateMap<CustomerRequirementDetailItem, CustomerRequirementDetailItemDto>()
            .ForMember(x => x.Id, d => d.MapFrom(x => x.CustomerRequirementId))
            .ForMember(x => x.PartNo, d => d.MapFrom(x => x.Part))
            .ForMember(x => x.Part, d => d.MapFrom(x => x.Part)) //check this
            .ForMember(x => x.Type, d => d.MapFrom(x => x.ReqType))
            .ForMember(x => x.MfrNo, d => d.MapFrom(x => x.ManufacturerNo))
            .ForMember(x => x.Mfr, d => d.MapFrom(x => x.ManufacturerCode))
            .ForMember(x => x.PQA, d => d.MapFrom(x => x.PartialQuantityAcceptable))
            .ForMember(x => x.RequirementforTraceability, d => d.MapFrom(x => x.ReqForTraceability))
            .ForMember(x => x.Product, d => d.MapFrom(x => x.ProductName))
            .ForMember(x => x.DC, d => d.MapFrom(x => x.DateCode))
            .ForMember(x => x.Package, d => d.MapFrom(x => x.PackageName))
            .ForMember(x => x.Alt, d => d.MapFrom(x => x.Alternate))
            .ForMember(x => x.Date, d => d.MapFrom(x => x.DatePromised))
            .ReverseMap();

        CreateMap<AdvisoryNote, AdvisoryNoteDto>();
        CreateMap<CurrentAtDate, CurrentAtDateDto>();
        CreateMap<MasterAppSettingItem, SettingItemDto>();
        CreateMap<PartDetail, PartDetailDto>()
            .ForMember(x => x.BestLastPricePaid12, d => d.MapFrom(x => x.last_Price_Paid_12))
            .ForMember(x => x.ClientBestPricePaid12, d => d.MapFrom(x => x.Cleint_Best_Price_Paid_12))
            .ReverseMap();

        CreateMap<CustomerRequirementMainInfo, CustomerRequirementMainInfoDto>().ReverseMap();
        CreateMap<CustomerRequirementWithoutBom, CustomerRequirementWithoutBomDto>().ReverseMap();
        
        CreateMap<PrintDocumentLog, PrintEmailLogDto>()
            //.ForMember(x => x.UpdatedByName, d => d.MapFrom(x => x.EmployeeName))
            .ForMember(x => x.SubSectionName, d => d.MapFrom(x => x.SubSectionName))
            .ForMember(x => x.Date, d => d.MapFrom(x => x.DLUP))
            .ReverseMap();
        CreateMap<CustomerRequirementECCNNotifyInfo, CustomerRequirementECCNNotifyInfoDto>()
            .ForMember(x => x.BOMNo, d => d.MapFrom(x => x.BOMNo == null ? 0 : x.BOMNo))
            .ForMember(x => x.IsSameCurFam, d => d.MapFrom(x => x.IsCurrencyInSameFaimly));
        CreateMap<Eccn, IHSEccnDto>()
            .ForMember(x => x.ECCNNo, d => d.MapFrom(x => x.ECCNId))
            .ForMember(x => x.ECCNWarning, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.Notes ?? "")))
            .ForMember(x => x.ECCNCode, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.ECCNCode ?? "")));
        CreateMap<ManufacturerAdvisoryNote, Orders.Requirements.ManufacturerAdvisoryNote.Queries.Dtos.ManufacturerAdvisoryNoteDto>();
        CreateMap<ManufacturerCheckRestricted, RestrictedManufacturerDto>();

        var currentCulture = CultureInfo.CurrentCulture;
        CreateMap<RequirementPartDetail, RequirementPartDetailDto>()
            .ForMember(dest => dest.StrBestOffer, opt => opt.MapFrom((src, dest) =>
                Functions.FormatCurrency(
                    src.CompetitorBestOffer ?? 0,
                    currentCulture,
                    src.CurrencyCode,
                    5,
                    false)))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom((src, dest) =>
                Functions.FormatCurrencyDescription(
                    src.CurrencyDescription ?? "", src.CurrencyCode)))
            .ForMember(dest => dest.BOMHeader, opt => opt.MapFrom((src) =>
                !string.IsNullOrEmpty(src.BOMHeader) ? src.BOMHeader + " (" + src.BOMStatus + ")" : ""))
            .ReverseMap();

        CreateMap<RLPart, RLPartDto>().ReverseMap();
        CreateMap<PartLinesRL, PartLinesRLDto>().ReverseMap();
        CreateMap<PartWatchMatchingRequirement, PartWatchMatchingRequirementDto>().ReverseMap();
        CreateMap<CompanyMainInfoReadModel, CompanyMainInfoDto>().ReverseMap();
        CreateMap<CustomerRequirementDetailItem, ListForCustomerRequirementDto>().ReverseMap();
        CreateMap<ListForCustomerRequirementDto, CustomerRequirementTemplate>().ReverseMap();
        CreateMap<CustomerRequirementForBom, CustomerRequirementForBomDto>();
    }
}