﻿export class LiteDatatable {
    constructor(selector, options) {
        this.state = {
            selector: selector,
            options: options,
            settings: this._createOption(options),
            filteredObj: null,
            sortConverter: {
                asc: 1,
                desc: 2
            },
            currentXhr: null
        }
    }

    init() {

        if (this.state.options.serverSide) {
            this.state.table = $(this.state.selector).DataTable(this.state.settings);
        }
        else if (this.state.options.paging || !this.state.options.enableResize) {
            this.state.table = $(this.state.selector).DataTable(this.state.settings);
        } else {
            this.state.table = $(this.state.selector).ResizeDataTable(this.state.settings);
        }

        this._registerTableEvents();
        return this.state.table;
    }

    adjustColumn() {
        this.state.table.columns.adjust();
    }

    on(eventName, handler) {
        if (eventName.endsWith(".mdt")) {
            this.state.table.on(eventName.replace(".mdt", ""), handler) // custom
            return;
        }

        $(this.state.selector).on(eventName, handler);
    }

    getContainerId() {
        const id = this.state.selector.replace('#', '');
        return `${id}-lite-datatable-container`;
    }

    selectRowbyId(rowId) {
        const row = this.state.table.row('#' + rowId);
        let rowIndex = row.index();

        this.datatable.row(rowIndex).select();
    }

    setDefaultOrder(columnIndex, dir) {
        this.state.table.order([columnIndex, dir]);
    }

    abortRequest() {
        this.state.currentXhr?.abort();
    }

    getCurrentFilter() {
        return this.state.filteredObj;
    }

    // only data without page info.
    // reload at this current page index
    reloadAsync(filteredObj) {
        if (filteredObj) {
            this.state.filteredObj = filteredObj;
        }

        let promiseResult = new Promise((resolve, reject) => {
            this.state.table.ajax.reload((e) => {
                promiseResult = resolve(e);
            }, false);
        });

        return promiseResult;
    }

    setPageIndex(pageIndex, pageLength) {
        this.state.table.settings()[0].iInitDisplayStart = pageIndex * pageLength;
    }

    changePageIndex(pageIndex) {
        this.state.table.page(pageIndex);
    }

    resetPage() {
        this.state.table.page(0);
    }

    getPageInfo() {
        return this._getPageInfo();
    }

    _getPageInfo(data) {

        let pageInfo = this.state.table.page.info();
        let orderInfo = this.state.table.order();

        if (!Array.isArray(orderInfo[0])) {
            orderInfo = [orderInfo]; // Ensure it’s always a nested array
        }
 
        return {
            index: pageInfo.page,
            size: pageInfo.length,
            orderNameBy: this._getColumnName(orderInfo[0][0]),
            orderBy: orderInfo[0][0],
            sortDir: this.state.sortConverter[orderInfo[0][1]],
            sortNameDir: orderInfo[0][1]
        }
    }

    setPageSize(size) {
        this.state.table.page.len(size);
    }

    _updateSortIcons() {
        let headerCells = this.state.table.columns().header();

        // Reset all to dt-orderable-none
        $(headerCells)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('dt-orderable-none')
            .removeAttr('aria-sort');

        let order = this.state.table.order();
        if (order.length) {
            let columnIndex = order[0][0];
            let direction = order[0][1];

            let th = $(headerCells[columnIndex]);
            if (direction === 'asc') {
                th.removeClass('dt-orderable-none').addClass('dt-orderable-asc');
                th.attr('aria-sort', 'ascending');
            } else {
                th.removeClass('dt-orderable-none').addClass('dt-orderable-desc');
                th.attr('aria-sort', 'descending');
            }
        }
    }

    _getColumnName(columnIndex) {
        let settings = this.state.table.settings()[0]; // Get DataTables settings
        return settings.aoColumns[columnIndex]?.name || ""; // Get name or return empty string if undefined
    }

    _registerTableEvents() {
        this.state.table.on('preXhr.dt', () => {
            this.state.table.rows().deselect();
        })

        this.state.table.on('xhr.dt', () => {
            this.state.table.data = this.state.table.ajax.json()?.data;
            if (this.state.filteredObj) {
                this.state.filteredObj._ignoredSave = false; // always clear after sent;
            }
        })

        this.state.table.on('order.dt', () => {
            this._updateSortIcons();
        })
    }

    _onError(xhr, status, error) {
        let self = this;

        if (error == 'abort') {
            $(self.state.selector).trigger('abort.mdt.dt');
        }
        else {
            let errorMessage = window.localizedStrings.unexpectedError;
            window.showToast('danger', errorMessage);
            $(self.state.selector).trigger('error.mdt.dt');
        }
    }

    _createOption(options) {
        if (options.serverSide) {
            let optionReturn = {
                serverSide: true,
                ajax: {
                    url: options.ajax.url,
                    type: options.ajax.type ?? 'POST',
                    contentType: 'application/json',
                    error: (xhr, status, error) => {
                        this._onError(xhr, status, error);
                    },
                    beforeSend: (xhr) => {
                        this.state.currentXhr = xhr;
                    },
                    data: (data) => {

                        const pageInfo = this._getPageInfo(data);

                        return JSON.stringify({ ...pageInfo, ...{ draw: data.draw }, ...this.state.filteredObj })
                    },
                },
                infoCallback: options.infoCallback,
                layout: options.layout ?? {
                    bottomStart: {
                        info: {
                            text: 'Showing _START_ to _END_ of _TOTAL_ entries'
                        },
                        pageLength: {
                            menu: [5, 10, 25, 50],
                            text: '_MENU_ per page'
                        },
                    },
                    bottomEnd: 'paging',
                    topStart: null,
                    topEnd: null
                },
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                info: options.info ?? true,
                autoWidth: options.autoWidth ?? true,
                processing: options.processing || false,
                //scrollY: options.scrollY ?? null,       
                scrollCollapse: true,
                responsive: true,
                select: options.disableSelect
                    ? undefined
                    : (
                        options.selectOverride ||
                        {
                            style: options.select?.style ?? "single",
                            toggleable: false,
                            info: false
                        }
                    ),
                paging: true,
                ordering: options.ordering ?? false,
                orderMulti: false,
                order: options.order || [[0, 'desc']], 
                searching: false,
                deferLoading: 0, // not call api immediately
                pageLength: options.pageConfig?.pageSize || 5,
                language: options.language ?? {
                    emptyTable: `<div style='text-align:center;'><i>${window.localizedStrings.noDataFound}</i></div>`,
                    zeroRecords: `<div style='text-align:center;'><i>${window.localizedStrings.noDataFound}</i></div>`,
                    infoFiltered: "",
                },
                columns: options.columns,
                rowId: options.rowId
            }
            if (options.scrollY) {
                optionReturn.scrollY = options.scrollY;
            }
            return optionReturn;
        } else { // under refactoring
            return {
                dataSrc: "data",
                ajax: {
                    url: options.ajax.url,
                    beforeSend: (xhr) => {
                        this.state.currentXhr = xhr;
                    },
                    type: options.ajax.type ?? 'POST',
                    contentType: 'application/json'
                },
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                info: options.info || false,
                responsive: true,
                select: options.disableSelect
                    ? undefined
                    : (
                        options.selectOverride ||
                        {
                            style: options.select?.style ?? "single",
                            toggleable: false,
                            info: false
                        }
                    ),
                resizeConfig: {
                    numberOfRowToShow: options.resizeConfig?.numberOfRowToShow ?? 10,
                },
                autoWidth: options.autoWidth ?? true,
                paging: options.paging || false,
                layout: options.layout || null,
                ordering: options.ordering ?? false,
                searching: true,
                scrollCollapse: true,
                createdRow: (row, data, rowindex) => {
                    if (data.inactive) {
                        $(row).addClass("text-light-gray");
                    }
                    if (options.createdRow) {
                        options.createdRow(row, data, rowindex)
                    }
                },
                language: options.language || {
                    emptyTable: `<div style='text-align:center;'><i>${window.localizedStrings.noDataFound}</i></div>`,
                    zeroRecords: `<div style='text-align:center;'><i>${window.localizedStrings.noDataFound}</i></div>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                },
                columns: options.columns,
                rowId: options.rowId
            }
        }
    }
}

